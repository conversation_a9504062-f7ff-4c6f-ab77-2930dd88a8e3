{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Subscription\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport UpgradeRestrictionModal from '../../../components/UpgradeRestrictionModal/UpgradeRestrictionModal';\nimport SubscriptionExpiredModal from '../../../components/SubscriptionExpiredModal/SubscriptionExpiredModal';\nimport './Subscription.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Subscription = () => {\n  _s();\n  var _subscriptionData$act, _selectedPlan$discoun, _selectedPlan$discoun2;\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(null); // Changed to store plan ID instead of boolean\n  const [showProcessingModal, setShowProcessingModal] = useState(false);\n\n  // Debug: Log showProcessingModal state changes\n  useEffect(() => {\n    console.log('🔍 showProcessingModal state changed to:', showProcessingModal);\n  }, [showProcessingModal]);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [paymentStatus, setPaymentStatus] = useState('');\n  const [showUpgradeRestriction, setShowUpgradeRestriction] = useState(false);\n  const [showExpiredModal, setShowExpiredModal] = useState(false);\n  const [processingStartTime, setProcessingStartTime] = useState(null);\n  const [showTryAgain, setShowTryAgain] = useState(false);\n  const [autoNavigateCountdown, setAutoNavigateCountdown] = useState(null);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const dispatch = useDispatch();\n\n  // Fallback sample plans in case API fails\n  const samplePlans = [{\n    _id: \"basic-plan-sample\",\n    title: \"Basic Membership\",\n    features: [\"2-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\"],\n    actualPrice: 28570,\n    discountedPrice: 20000,\n    discountPercentage: 30,\n    duration: 2,\n    status: true\n  }, {\n    _id: \"premium-plan-sample\",\n    title: \"Premium Plan\",\n    features: [\"3-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\", \"Priority support\"],\n    actualPrice: 45000,\n    discountedPrice: 35000,\n    discountPercentage: 22,\n    duration: 3,\n    status: true\n  }];\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n  }, []);\n\n  // Handle body scroll lock when modals are open (simplified approach)\n  useEffect(() => {\n    if (showProcessingModal || showSuccessModal) {\n      // Simply prevent body scroll without position fixed\n      document.body.style.overflow = 'hidden';\n    } else {\n      // Restore body scroll\n      document.body.style.overflow = '';\n    }\n\n    // Cleanup on unmount\n    return () => {\n      document.body.style.overflow = '';\n    };\n  }, [showProcessingModal, showSuccessModal]);\n\n  // Enhanced scroll detection for modal content\n  useEffect(() => {\n    const detectScrollableContent = () => {\n      const modalContents = document.querySelectorAll('.modal-content');\n      modalContents.forEach(content => {\n        if (content.scrollHeight > content.clientHeight) {\n          content.classList.add('has-scroll');\n        } else {\n          content.classList.remove('has-scroll');\n        }\n      });\n    };\n\n    // Detect on modal open\n    if (showProcessingModal || showSuccessModal) {\n      // Small delay to ensure modal is rendered\n      setTimeout(detectScrollableContent, 100);\n\n      // Re-detect on window resize\n      window.addEventListener('resize', detectScrollableContent);\n      return () => {\n        window.removeEventListener('resize', detectScrollableContent);\n      };\n    }\n  }, [showProcessingModal, showSuccessModal]);\n\n  // Check for expired subscription and show modal\n  useEffect(() => {\n    if (subscriptionData && isSubscriptionExpired()) {\n      console.log('🚫 Subscription expired, showing modal');\n      setShowExpiredModal(true);\n    } else {\n      setShowExpiredModal(false);\n    }\n  }, [subscriptionData]);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      console.log('Fetching plans...');\n      const response = await getPlans();\n      console.log('Plans response:', response);\n      if (response.success && response.data && response.data.length > 0) {\n        setPlans(response.data);\n        console.log('Plans loaded successfully from API:', response.data);\n      } else if (Array.isArray(response) && response.length > 0) {\n        // Handle case where response is directly an array of plans\n        setPlans(response);\n        console.log('Plans loaded as array from API:', response);\n      } else {\n        console.warn('No plans from API, using sample plans');\n        setPlans(samplePlans);\n        message.info('Showing sample plans. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('Error loading plans from API:', error);\n      console.log('Using fallback sample plans');\n      setPlans(samplePlans);\n      message.warning('Using sample plans. Please check your connection and try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n      console.log('Current subscription:', response);\n    } catch (error) {\n      console.log('No active subscription found');\n    }\n  };\n\n  // Check if subscription is expired\n  const isSubscriptionExpired = () => {\n    if (!subscriptionData) return true;\n\n    // If no subscription data, consider expired\n    if (!subscriptionData.endDate) return true;\n\n    // If payment status is not paid, consider expired\n    if (subscriptionData.paymentStatus !== 'paid') return true;\n\n    // If status is not active, consider expired\n    if (subscriptionData.status !== 'active') return true;\n\n    // Check if end date has passed\n    const endDate = new Date(subscriptionData.endDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n    endDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    return endDate < today;\n  };\n\n  // Handle subscription renewal from expired modal\n  const handleRenewSubscription = async selectedPlan => {\n    setShowExpiredModal(false);\n    await handlePlanSelect(selectedPlan);\n  };\n\n  // Handle closing payment processing modal\n  const handleCloseProcessingModal = () => {\n    setShowProcessingModal(false);\n    setPaymentLoading(null); // Reset to null instead of false\n    setShowTryAgain(false);\n    setProcessingStartTime(null);\n    setPaymentStatus('');\n    message.info('Payment process cancelled. You can try again anytime.');\n  };\n\n  // Handle try again functionality\n  const handleTryAgain = () => {\n    if (selectedPlan) {\n      setShowTryAgain(false);\n      setProcessingStartTime(null);\n      handlePlanSelect(selectedPlan);\n    }\n  };\n\n  // Test success modal (for debugging)\n  const testSuccessModal = () => {\n    console.log('🧪 Testing success modal...');\n    setShowProcessingModal(false);\n    setShowSuccessModal(true);\n    setPaymentLoading(null);\n  };\n\n  // Test processing modal (for debugging)\n  const testProcessingModal = () => {\n    console.log('🧪 Testing processing modal...');\n    setShowProcessingModal(true);\n    setPaymentStatus('Testing processing modal...');\n    setSelectedPlan(plans[0] || {\n      title: 'Test Plan',\n      discountedPrice: 5000,\n      duration: 1\n    });\n  };\n  const handlePlanSelect = async plan => {\n    // Check if user already has an active subscription\n    if (subscriptionData && subscriptionData.status === 'active' && subscriptionData.paymentStatus === 'paid') {\n      console.log('🚫 User already has active subscription:', subscriptionData);\n      setShowUpgradeRestriction(true);\n      return;\n    }\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n    try {\n      var _user$name;\n      console.log('🚀 Starting payment for plan:', plan.title);\n      console.log('🔧 IMMEDIATELY showing processing modal...');\n\n      // IMMEDIATELY show processing modal when user chooses plan\n      setSelectedPlan(plan);\n      setPaymentLoading(plan._id);\n      setShowProcessingModal(true);\n      setShowTryAgain(false);\n      setProcessingStartTime(Date.now());\n      setPaymentStatus('🚀 Preparing your payment request...');\n      console.log('✅ Processing modal IMMEDIATELY displayed');\n\n      // Small delay to ensure modal is visible before API call\n      await new Promise(resolve => setTimeout(resolve, 200));\n\n      // Set timer for try again button (10 seconds)\n      setTimeout(() => {\n        setShowTryAgain(true);\n      }, 10000);\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      setPaymentStatus('📤 Sending payment request to ZenoPay...');\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        var _response$data;\n        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');\n        console.log('💳 Payment response:', response);\n        console.log('🆔 Order ID:', response.order_id);\n\n        // Show confirmation message to user\n        message.success({\n          content: `💳 Payment initiated! 📱 Check your phone (${user.phoneNumber}) for SMS confirmation from ZenoPay.`,\n          duration: 8,\n          style: {\n            marginTop: '20vh'\n          }\n        });\n\n        // Start checking payment status immediately\n        const orderIdToCheck = response.order_id || ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.order_id) || 'demo_order';\n        console.log('🔍 Starting payment confirmation check for order:', orderIdToCheck);\n        checkPaymentConfirmation(orderIdToCheck);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('❌ Payment failed:', error);\n      setShowProcessingModal(false);\n      message.error('Payment failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const checkPaymentConfirmation = async orderId => {\n    console.log('🚀 Starting payment confirmation check for order:', orderId);\n    let isPolling = true;\n    let handleVisibilityChange;\n    try {\n      setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n\n      // Poll payment status every 2 seconds for optimal responsiveness\n      let attempts = 0;\n      const maxAttempts = 150; // 150 attempts * 2 seconds = 5 minutes\n\n      const pollPaymentStatus = async () => {\n        attempts++;\n        console.log(`🔍 Payment status check attempt ${attempts}/${maxAttempts} for order:`, orderId);\n        try {\n          const statusResponse = await checkPaymentStatus({\n            orderId\n          });\n          console.log('📊 Payment status response:', statusResponse);\n          console.log('🔍 Checking payment conditions:');\n          console.log('  - Live payment:', (statusResponse === null || statusResponse === void 0 ? void 0 : statusResponse.paymentStatus) === 'paid' && (statusResponse === null || statusResponse === void 0 ? void 0 : statusResponse.status) === 'active');\n          console.log('  - Demo payment:', (statusResponse === null || statusResponse === void 0 ? void 0 : statusResponse.status) === 'completed' && (statusResponse === null || statusResponse === void 0 ? void 0 : statusResponse.success) === true);\n          if (statusResponse && (statusResponse.paymentStatus === 'paid' && statusResponse.status === 'active' || statusResponse.status === 'completed' && statusResponse.success === true)) {\n            // Payment confirmed immediately!\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('🎉 Payment confirmed! Activating your subscription...');\n            console.log('✅ Payment confirmed, preparing to show success modal...');\n\n            // Show success INSTANTLY - no delay\n            console.log('🔄 Setting modal states - Processing: false, Success: true');\n            setShowProcessingModal(false);\n            setShowSuccessModal(true);\n            setPaymentLoading(null);\n            console.log('✅ Success modal state set to true');\n\n            // Refresh subscription data\n            checkCurrentSubscription();\n\n            // Show immediate success message\n            message.success({\n              content: '🎉 Payment confirmed! All features are now unlocked!',\n              duration: 5,\n              style: {\n                marginTop: '20vh',\n                fontSize: '16px'\n              }\n            });\n\n            // Start countdown for auto-navigation to hub\n            setAutoNavigateCountdown(5);\n            const countdownInterval = setInterval(() => {\n              setAutoNavigateCountdown(prev => {\n                if (prev <= 1) {\n                  clearInterval(countdownInterval);\n                  console.log('🏠 Auto-navigating to hub after successful payment...');\n                  setShowSuccessModal(false);\n                  window.location.href = '/user/hub';\n                  return null;\n                }\n                return prev - 1;\n              });\n            }, 1000);\n          } else if (attempts >= maxAttempts) {\n            // Timeout - but don't fail completely\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('⏰ Still waiting for confirmation. Please complete the payment on your phone.');\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setPaymentLoading(null); // Reset to null\n              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status or try again.');\n            }, 2000);\n          } else {\n            // Continue polling - NO TIME INDICATION, just encouraging message\n            setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n            setTimeout(pollPaymentStatus, 2000); // Check every 2 seconds for better performance\n          }\n        } catch (error) {\n          console.error('Payment status check error:', error);\n\n          // Handle specific error types\n          if (error.message && error.message.includes('404')) {\n            console.error('❌ Payment status endpoint not found (404)');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Payment verification service is temporarily unavailable. Please contact support or check your subscription status manually.');\n            return;\n          }\n          if (error.message && error.message.includes('401')) {\n            console.error('❌ Authentication required for payment status check');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Please login again to check payment status.');\n            return;\n          }\n          if (attempts >= maxAttempts) {\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Unable to confirm payment status. Please check your subscription status manually.');\n          } else {\n            // Continue polling even if there's an error (unless it's a critical error)\n            setTimeout(pollPaymentStatus, 1000);\n          }\n        }\n      };\n\n      // Add visibility change listener to check immediately when user returns to tab\n      handleVisibilityChange = () => {\n        if (!document.hidden && isPolling) {\n          console.log('User returned to tab, checking payment status immediately...');\n          setPaymentStatus('🔍 Checking payment status...');\n          // Trigger immediate check\n          setTimeout(() => pollPaymentStatus(), 100);\n        }\n      };\n      document.addEventListener('visibilitychange', handleVisibilityChange);\n\n      // Start polling immediately (no delay) - check right away\n      setTimeout(pollPaymentStatus, 500); // Start checking after 0.5 seconds\n    } catch (error) {\n      isPolling = false; // Stop polling\n      if (handleVisibilityChange) {\n        document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n      }\n\n      setShowProcessingModal(false);\n      message.error('Payment confirmation failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    if ((user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'expired' || subscriptionData && subscriptionData.status === 'expired') {\n      return 'expired';\n    }\n    return 'none';\n  };\n  const formatDate = dateString => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const getDaysRemaining = () => {\n    if (!(subscriptionData !== null && subscriptionData !== void 0 && subscriptionData.endDate)) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n  const subscriptionStatus = getSubscriptionStatus();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"subscription-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subscription-container\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"subscription-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            console.log('🧪 Testing success modal...');\n            setSelectedPlan(plans[0] || {\n              title: 'Test Plan',\n              duration: 1,\n              discountedPrice: 13000\n            });\n            setShowSuccessModal(true);\n          },\n          style: {\n            position: 'fixed',\n            top: '10px',\n            right: '10px',\n            background: '#52c41a',\n            color: 'white',\n            border: 'none',\n            padding: '8px 16px',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            zIndex: 9999\n          },\n          children: \"\\uD83E\\uDDEA Test Success Modal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"page-title\",\n          children: [/*#__PURE__*/_jsxDEV(FaCrown, {\n            className: \"title-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 13\n          }, this), \"Subscription Management\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 547,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"page-subtitle\",\n          children: \"Manage your subscription and access premium features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 519,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        className: \"current-subscription\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Current Subscription\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 561,\n          columnNumber: 11\n        }, this), subscriptionStatus === 'active' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card active\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n              className: \"status-icon active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Active Subscription\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCrown, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Plan: \", (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData$act = subscriptionData.activePlan) === null || _subscriptionData$act === void 0 ? void 0 : _subscriptionData$act.title) || 'Premium Plan']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 572,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Expires: \", formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Days Remaining: \", getDaysRemaining()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 580,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 13\n        }, this), subscriptionStatus === 'expired' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card expired\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n              className: \"status-icon expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 589,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Subscription Expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Expired: \", formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"renewal-message\",\n              children: \"Your subscription has expired. Choose a new plan below to continue accessing premium features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 13\n        }, this), subscriptionStatus === 'none' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card none\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaUser, {\n              className: \"status-icon none\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Free Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"upgrade-message\",\n              children: \"You're currently using a free account. Upgrade to a premium plan to unlock all features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 555,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"available-plans\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: subscriptionStatus === 'active' ? '🚀 Upgrade Your Plan' : subscriptionStatus === 'expired' ? '🔄 Renew Your Subscription' : '🎯 Choose Your Plan'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 626,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '10px',\n            justifyContent: 'center',\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: testProcessingModal,\n            style: {\n              background: '#ff6b6b',\n              color: 'white',\n              border: 'none',\n              padding: '8px 16px',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\uD83E\\uDDEA Test Processing Modal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: testSuccessModal,\n            style: {\n              background: '#51cf66',\n              color: 'white',\n              border: 'none',\n              padding: '8px 16px',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\uD83E\\uDDEA Test Success Modal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle\",\n          children: subscriptionStatus === 'active' ? 'Upgrade to a longer plan for better value and extended access' : subscriptionStatus === 'expired' ? 'Your subscription has expired. Renew now to continue accessing premium features' : 'Select a subscription plan to unlock all premium features and start your learning journey'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 666,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading plans...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 13\n        }, this) : plans.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-plans-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-plans-icon\",\n            children: \"\\uD83D\\uDCCB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Plans Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Plans are currently being loaded. Please refresh the page or try again later.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"refresh-btn\",\n            onClick: fetchPlans,\n            children: \"\\uD83D\\uDD04 Refresh Plans\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 681,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plans-grid\",\n          children: plans.map(plan => {\n            var _plan$title, _plan$discountedPrice, _plan$actualPrice, _plan$features;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: \"plan-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"plan-title\",\n                  children: plan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 699,\n                  columnNumber: 21\n                }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('standard')) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"plan-badge\",\n                  children: \"\\uD83D\\uDD25 Popular\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 701,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-pricing\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price-display\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"current-price\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"currency\",\n                      children: \"TZS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 708,\n                      columnNumber: 25\n                    }, this), (_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 707,\n                    columnNumber: 23\n                  }, this), plan.actualPrice > plan.discountedPrice && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"original-price\",\n                      children: [(_plan$actualPrice = plan.actualPrice) === null || _plan$actualPrice === void 0 ? void 0 : _plan$actualPrice.toLocaleString(), \" TZS\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 713,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"discount-badge\",\n                      children: [Math.round((plan.actualPrice - plan.discountedPrice) / plan.actualPrice * 100), \"% OFF\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 714,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 706,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"plan-duration\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"duration-highlight\",\n                    children: plan.duration\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 721,\n                    columnNumber: 23\n                  }, this), \" month\", plan.duration > 1 ? 's' : '', \" access\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 720,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-features\",\n                children: (_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.slice(0, 5).map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                    className: \"feature-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 728,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: feature\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 729,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"select-plan-btn\",\n                onClick: () => handlePlanSelect(plan),\n                disabled: paymentLoading === plan._id,\n                style: {\n                  background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '12px',\n                  padding: '1rem 1.5rem',\n                  fontSize: '1rem',\n                  fontWeight: '600',\n                  cursor: paymentLoading === plan._id ? 'not-allowed' : 'pointer',\n                  transition: 'all 0.3s ease',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: '0.5rem',\n                  width: '100%',\n                  opacity: paymentLoading === plan._id ? 0.6 : 1\n                },\n                onMouseEnter: e => {\n                  if (paymentLoading !== plan._id) {\n                    e.target.style.background = 'linear-gradient(135deg, #1d4ed8, #1e40af)';\n                    e.target.style.transform = 'translateY(-2px)';\n                    e.target.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.4)';\n                  }\n                },\n                onMouseLeave: e => {\n                  if (paymentLoading !== plan._id) {\n                    e.target.style.background = 'linear-gradient(135deg, #3b82f6, #1d4ed8)';\n                    e.target.style.transform = 'translateY(0)';\n                    e.target.style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.3)';\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(FaCreditCard, {\n                  className: \"btn-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 770,\n                  columnNumber: 21\n                }, this), paymentLoading === plan._id ? 'Processing...' : subscriptionStatus === 'active' ? 'Click to Upgrade' : subscriptionStatus === 'expired' ? 'Click to Renew' : 'Click to Pay']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 19\n              }, this)]\n            }, plan._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 690,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 620,\n        columnNumber: 9\n      }, this), (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.6\n        },\n        className: \"phone-warning\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"warning-content\",\n          children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n            className: \"warning-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 795,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Phone Number Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 797,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Please update your phone number in your profile to subscribe to a plan.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 798,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"update-phone-btn\",\n              onClick: () => window.location.href = '/profile',\n              children: \"Update Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 796,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 794,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 788,\n        columnNumber: 11\n      }, this), showProcessingModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-modal-overlay\",\n        onClick: e => e.target === e.currentTarget && handleCloseProcessingModal(),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-modal-container processing\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"modal-close-btn\",\n            onClick: handleCloseProcessingModal,\n            \"aria-label\": \"Close modal\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M18 6L6 18M6 6L18 18\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 824,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 823,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 818,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-header processing\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"processing-icon\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"payment-icon\",\n                width: \"32\",\n                height: \"32\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M2 12C2 8.229 2 6.343 3.172 5.172C4.343 4 6.229 4 10 4H14C17.771 4 19.657 4 20.828 5.172C22 6.343 22 8.229 22 12C22 15.771 22 17.657 20.828 18.828C19.657 20 17.771 20 14 20H10C6.229 20 4.343 20 3.172 18.828C2 17.657 2 15.771 2 12Z\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"1.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 833,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M10 16H6\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"1.5\",\n                  strokeLinecap: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 834,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M14 16H12.5\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"1.5\",\n                  strokeLinecap: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 835,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M2 10L22 10\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"1.5\",\n                  strokeLinecap: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 832,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Processing Payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Secure transaction in progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 840,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 829,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"status-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"status-indicator processing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 847,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"status-text\",\n                children: paymentStatus\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 846,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-info-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 853,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Amount\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 856,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : (_selectedPlan$discoun = selectedPlan.discountedPrice) === null || _selectedPlan$discoun === void 0 ? void 0 : _selectedPlan$discoun.toLocaleString(), \" TZS\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 857,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 855,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Duration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 860,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration, \" month\", (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration) > 1 ? 's' : '']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 861,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 859,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 854,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 852,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"instruction-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"instruction-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"20\",\n                  height: \"20\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M16.5562 12.9062L16.1007 13.359C16.1007 13.359 15.0181 14.4355 12.0631 11.4972C9.10812 8.55901 10.1907 7.48257 10.1907 7.48257L10.4775 7.19738C11.1841 6.49484 11.2507 5.36691 10.6342 4.54348L9.37326 2.85908C8.61028 1.83992 7.13596 1.70529 6.26145 2.57483L4.69185 4.13552C4.25823 4.56668 3.96765 5.12559 4.00289 5.74561C4.09304 7.33182 4.81071 10.7447 8.81536 14.7266C13.0621 18.9492 17.0468 19.117 18.6763 18.9651C19.1917 18.9171 19.6399 18.6546 20.0011 18.2954L21.4217 16.883C22.3806 15.9295 22.1102 14.2949 20.8833 13.628L18.9728 12.5894C18.1672 12.1515 17.1858 12.2801 16.5562 12.9062Z\",\n                    fill: \"currentColor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 870,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 869,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Check Your Phone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 872,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 868,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"phone-number\",\n                children: user === null || user === void 0 ? void 0 : user.phoneNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"instruction-steps\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step\",\n                  children: \"1. You'll receive an SMS with payment instructions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 876,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step\",\n                  children: \"2. Follow the SMS steps to confirm payment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 877,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step\",\n                  children: \"3. Complete the mobile money transaction\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 878,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 875,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 867,\n              columnNumber: 17\n            }, this), showTryAgain && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"try-again-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Taking longer than expected?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 885,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"try-again-btn\",\n                onClick: handleTryAgain,\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M4 12a8 8 0 018-8V2.5\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 888,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 4L9 7L12 10\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 889,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 887,\n                  columnNumber: 23\n                }, this), \"Try Again\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 886,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 884,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 844,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 816,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 812,\n        columnNumber: 11\n      }, this), showSuccessModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-modal-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-modal-container success\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"modal-close-btn\",\n            onClick: () => {\n              setAutoNavigateCountdown(null);\n              setShowSuccessModal(false);\n            },\n            \"aria-label\": \"Close modal\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M18 6L6 18M6 6L18 18\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 916,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 915,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 907,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-header success\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"48\",\n                height: \"48\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z\",\n                  fill: \"#22c55e\",\n                  fillOpacity: \"0.2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 924,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M16 9L10.5 14.5L8 12\",\n                  stroke: \"#22c55e\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 925,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z\",\n                  stroke: \"#22c55e\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 926,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 923,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 922,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Payment Successful!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 929,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Welcome to \", selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title, \"!\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 930,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 921,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-content\",\n            children: [autoNavigateCountdown && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"countdown-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"countdown-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"20\",\n                  height: \"20\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 939,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"3\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 940,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 938,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 937,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Redirecting to Hub in \", autoNavigateCountdown, \" seconds...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 943,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 936,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-summary-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Subscription Activated\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 949,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Plan\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 952,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 953,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 951,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Duration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 956,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration, \" month\", (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration) > 1 ? 's' : '']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 957,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 955,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Amount Paid\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 960,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : (_selectedPlan$discoun2 = selectedPlan.discountedPrice) === null || _selectedPlan$discoun2 === void 0 ? void 0 : _selectedPlan$discoun2.toLocaleString(), \" TZS\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 961,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 959,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row status\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 964,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"status-badge\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"16\",\n                      height: \"16\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M9 12L11 14L15 10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 967,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"9\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 968,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 966,\n                      columnNumber: 25\n                    }, this), \"Active\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 965,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 963,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 950,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 948,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"features-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\uD83D\\uDE80 Premium Features Unlocked\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 978,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"features-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12L11 14L15 10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 982,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 983,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 981,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Unlimited Quizzes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 985,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 980,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12L11 14L15 10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 989,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 990,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 988,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"AI Assistant\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 992,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 987,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12L11 14L15 10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 996,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 997,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 995,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Study Materials\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 999,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 994,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12L11 14L15 10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1003,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1004,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1002,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Progress Tracking\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1006,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1001,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12L11 14L15 10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1010,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1011,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1009,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Learning Videos\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1013,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1008,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12L11 14L15 10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1017,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1018,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1016,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Forum Access\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1020,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1015,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 979,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 977,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"primary-btn\",\n                onClick: () => {\n                  setAutoNavigateCountdown(null);\n                  setShowSuccessModal(false);\n                  window.location.href = '/user/hub';\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"20\",\n                  height: \"20\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1036,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                    points: \"9,22 9,12 15,12 15,22\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1037,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1035,\n                  columnNumber: 21\n                }, this), \"Continue to Hub \", autoNavigateCountdown ? `(${autoNavigateCountdown}s)` : '']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1027,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"secondary-btn\",\n                onClick: () => {\n                  setAutoNavigateCountdown(null);\n                  setShowSuccessModal(false);\n                },\n                children: \"Close\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1041,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1026,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 933,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 905,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 904,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(UpgradeRestrictionModal, {\n        visible: showUpgradeRestriction,\n        onClose: () => setShowUpgradeRestriction(false),\n        currentPlan: plans.find(p => p._id === (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.activePlan)) || (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.plan),\n        subscription: subscriptionData,\n        user: user\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1059,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SubscriptionExpiredModal, {\n        visible: showExpiredModal,\n        onClose: () => setShowExpiredModal(false),\n        onRenew: handleRenewSubscription,\n        subscription: subscriptionData,\n        user: user,\n        plans: plans\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1068,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 517,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 516,\n    columnNumber: 5\n  }, this);\n};\n_s(Subscription, \"Hu16YitvZWzUHeT6QHKnJpmddc4=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c = Subscription;\nexport default Subscription;\nvar _c;\n$RefreshReg$(_c, \"Subscription\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "motion", "message", "FaCrown", "FaCalendarAlt", "FaCheckCircle", "FaTimesCircle", "FaCreditCard", "FaUser", "getPlans", "addPayment", "checkPaymentStatus", "ShowLoading", "HideLoading", "UpgradeRestrictionModal", "SubscriptionExpiredModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Subscription", "_s", "_subscriptionData$act", "_selectedPlan$discoun", "_selectedPlan$discoun2", "plans", "setPlans", "loading", "setLoading", "paymentLoading", "setPaymentLoading", "showProcessingModal", "setShowProcessingModal", "console", "log", "showSuccessModal", "setShowSuccessModal", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "paymentStatus", "setPaymentStatus", "showUpgradeRestriction", "setShowUpgradeRestriction", "showExpiredModal", "setShowExpiredModal", "processingStartTime", "setProcessingStartTime", "showTryAgain", "setShowTryAgain", "autoNavigateCountdown", "setAutoNavigateCountdown", "user", "state", "subscriptionData", "subscription", "dispatch", "samplePlans", "_id", "title", "features", "actualPrice", "discountedPrice", "discountPercentage", "duration", "status", "fetchPlans", "checkCurrentSubscription", "document", "body", "style", "overflow", "detectScrollableContent", "modalContents", "querySelectorAll", "for<PERSON>ach", "content", "scrollHeight", "clientHeight", "classList", "add", "remove", "setTimeout", "window", "addEventListener", "removeEventListener", "isSubscriptionExpired", "response", "success", "data", "length", "Array", "isArray", "warn", "info", "error", "warning", "endDate", "Date", "today", "setHours", "handleRenewSubscription", "handlePlanSelect", "handleCloseProcessingModal", "handleTryAgain", "testSuccessModal", "testProcessingModal", "plan", "phoneNumber", "test", "_user$name", "now", "Promise", "resolve", "paymentData", "userId", "userPhone", "userEmail", "email", "name", "replace", "toLowerCase", "_response$data", "order_id", "marginTop", "orderIdToCheck", "checkPaymentConfirmation", "Error", "orderId", "isPolling", "handleVisibilityChange", "attempts", "maxAttempts", "pollPaymentStatus", "statusResponse", "fontSize", "countdownInterval", "setInterval", "prev", "clearInterval", "location", "href", "includes", "hidden", "getSubscriptionStatus", "subscriptionStatus", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "getDaysRemaining", "diffTime", "diffDays", "Math", "ceil", "max", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "onClick", "position", "top", "right", "background", "color", "border", "padding", "borderRadius", "cursor", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "activePlan", "display", "gap", "justifyContent", "marginBottom", "map", "_plan$title", "_plan$discountedPrice", "_plan$actualPrice", "_plan$features", "whileHover", "scale", "whileTap", "toLocaleString", "round", "slice", "feature", "index", "disabled", "fontWeight", "alignItems", "width", "onMouseEnter", "e", "target", "transform", "boxShadow", "onMouseLeave", "currentTarget", "height", "viewBox", "fill", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "fillOpacity", "cx", "cy", "r", "points", "visible", "onClose", "currentPlan", "find", "p", "onRenew", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Subscription/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport UpgradeRestrictionModal from '../../../components/UpgradeRestrictionModal/UpgradeRestrictionModal';\nimport SubscriptionExpiredModal from '../../../components/SubscriptionExpiredModal/SubscriptionExpiredModal';\nimport './Subscription.css';\n\nconst Subscription = () => {\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(null); // Changed to store plan ID instead of boolean\n  const [showProcessingModal, setShowProcessingModal] = useState(false);\n\n  // Debug: Log showProcessingModal state changes\n  useEffect(() => {\n    console.log('🔍 showProcessingModal state changed to:', showProcessingModal);\n  }, [showProcessingModal]);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [paymentStatus, setPaymentStatus] = useState('');\n  const [showUpgradeRestriction, setShowUpgradeRestriction] = useState(false);\n  const [showExpiredModal, setShowExpiredModal] = useState(false);\n  const [processingStartTime, setProcessingStartTime] = useState(null);\n  const [showTryAgain, setShowTryAgain] = useState(false);\n  const [autoNavigateCountdown, setAutoNavigateCountdown] = useState(null);\n  const { user } = useSelector((state) => state.user);\n  const { subscriptionData } = useSelector((state) => state.subscription);\n  const dispatch = useDispatch();\n\n  // Fallback sample plans in case API fails\n  const samplePlans = [\n    {\n      _id: \"basic-plan-sample\",\n      title: \"Basic Membership\",\n      features: [\n        \"2-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\"\n      ],\n      actualPrice: 28570,\n      discountedPrice: 20000,\n      discountPercentage: 30,\n      duration: 2,\n      status: true\n    },\n    {\n      _id: \"premium-plan-sample\",\n      title: \"Premium Plan\",\n      features: [\n        \"3-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\",\n        \"Priority support\"\n      ],\n      actualPrice: 45000,\n      discountedPrice: 35000,\n      discountPercentage: 22,\n      duration: 3,\n      status: true\n    }\n  ];\n\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n  }, []);\n\n  // Handle body scroll lock when modals are open (simplified approach)\n  useEffect(() => {\n    if (showProcessingModal || showSuccessModal) {\n      // Simply prevent body scroll without position fixed\n      document.body.style.overflow = 'hidden';\n    } else {\n      // Restore body scroll\n      document.body.style.overflow = '';\n    }\n\n    // Cleanup on unmount\n    return () => {\n      document.body.style.overflow = '';\n    };\n  }, [showProcessingModal, showSuccessModal]);\n\n  // Enhanced scroll detection for modal content\n  useEffect(() => {\n    const detectScrollableContent = () => {\n      const modalContents = document.querySelectorAll('.modal-content');\n      modalContents.forEach(content => {\n        if (content.scrollHeight > content.clientHeight) {\n          content.classList.add('has-scroll');\n        } else {\n          content.classList.remove('has-scroll');\n        }\n      });\n    };\n\n    // Detect on modal open\n    if (showProcessingModal || showSuccessModal) {\n      // Small delay to ensure modal is rendered\n      setTimeout(detectScrollableContent, 100);\n\n      // Re-detect on window resize\n      window.addEventListener('resize', detectScrollableContent);\n\n      return () => {\n        window.removeEventListener('resize', detectScrollableContent);\n      };\n    }\n  }, [showProcessingModal, showSuccessModal]);\n\n  // Check for expired subscription and show modal\n  useEffect(() => {\n    if (subscriptionData && isSubscriptionExpired()) {\n      console.log('🚫 Subscription expired, showing modal');\n      setShowExpiredModal(true);\n    } else {\n      setShowExpiredModal(false);\n    }\n  }, [subscriptionData]);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      console.log('Fetching plans...');\n      const response = await getPlans();\n      console.log('Plans response:', response);\n\n      if (response.success && response.data && response.data.length > 0) {\n        setPlans(response.data);\n        console.log('Plans loaded successfully from API:', response.data);\n      } else if (Array.isArray(response) && response.length > 0) {\n        // Handle case where response is directly an array of plans\n        setPlans(response);\n        console.log('Plans loaded as array from API:', response);\n      } else {\n        console.warn('No plans from API, using sample plans');\n        setPlans(samplePlans);\n        message.info('Showing sample plans. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('Error loading plans from API:', error);\n      console.log('Using fallback sample plans');\n      setPlans(samplePlans);\n      message.warning('Using sample plans. Please check your connection and try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n      console.log('Current subscription:', response);\n    } catch (error) {\n      console.log('No active subscription found');\n    }\n  };\n\n  // Check if subscription is expired\n  const isSubscriptionExpired = () => {\n    if (!subscriptionData) return true;\n\n    // If no subscription data, consider expired\n    if (!subscriptionData.endDate) return true;\n\n    // If payment status is not paid, consider expired\n    if (subscriptionData.paymentStatus !== 'paid') return true;\n\n    // If status is not active, consider expired\n    if (subscriptionData.status !== 'active') return true;\n\n    // Check if end date has passed\n    const endDate = new Date(subscriptionData.endDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n    endDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    return endDate < today;\n  };\n\n  // Handle subscription renewal from expired modal\n  const handleRenewSubscription = async (selectedPlan) => {\n    setShowExpiredModal(false);\n    await handlePlanSelect(selectedPlan);\n  };\n\n  // Handle closing payment processing modal\n  const handleCloseProcessingModal = () => {\n    setShowProcessingModal(false);\n    setPaymentLoading(null); // Reset to null instead of false\n    setShowTryAgain(false);\n    setProcessingStartTime(null);\n    setPaymentStatus('');\n    message.info('Payment process cancelled. You can try again anytime.');\n  };\n\n  // Handle try again functionality\n  const handleTryAgain = () => {\n    if (selectedPlan) {\n      setShowTryAgain(false);\n      setProcessingStartTime(null);\n      handlePlanSelect(selectedPlan);\n    }\n  };\n\n  // Test success modal (for debugging)\n  const testSuccessModal = () => {\n    console.log('🧪 Testing success modal...');\n    setShowProcessingModal(false);\n    setShowSuccessModal(true);\n    setPaymentLoading(null);\n  };\n\n  // Test processing modal (for debugging)\n  const testProcessingModal = () => {\n    console.log('🧪 Testing processing modal...');\n    setShowProcessingModal(true);\n    setPaymentStatus('Testing processing modal...');\n    setSelectedPlan(plans[0] || { title: 'Test Plan', discountedPrice: 5000, duration: 1 });\n  };\n\n  const handlePlanSelect = async (plan) => {\n    // Check if user already has an active subscription\n    if (subscriptionData && subscriptionData.status === 'active' && subscriptionData.paymentStatus === 'paid') {\n      console.log('🚫 User already has active subscription:', subscriptionData);\n      setShowUpgradeRestriction(true);\n      return;\n    }\n\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n\n    try {\n      console.log('🚀 Starting payment for plan:', plan.title);\n      console.log('🔧 IMMEDIATELY showing processing modal...');\n\n      // IMMEDIATELY show processing modal when user chooses plan\n      setSelectedPlan(plan);\n      setPaymentLoading(plan._id);\n      setShowProcessingModal(true);\n      setShowTryAgain(false);\n      setProcessingStartTime(Date.now());\n      setPaymentStatus('🚀 Preparing your payment request...');\n\n      console.log('✅ Processing modal IMMEDIATELY displayed');\n\n      // Small delay to ensure modal is visible before API call\n      await new Promise(resolve => setTimeout(resolve, 200));\n\n      // Set timer for try again button (10 seconds)\n      setTimeout(() => {\n        setShowTryAgain(true);\n      }, 10000);\n\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n\n      setPaymentStatus('📤 Sending payment request to ZenoPay...');\n      const response = await addPayment(paymentData);\n\n      if (response.success) {\n        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');\n\n        console.log('💳 Payment response:', response);\n        console.log('🆔 Order ID:', response.order_id);\n\n        // Show confirmation message to user\n        message.success({\n          content: `💳 Payment initiated! 📱 Check your phone (${user.phoneNumber}) for SMS confirmation from ZenoPay.`,\n          duration: 8,\n          style: {\n            marginTop: '20vh',\n          }\n        });\n\n        // Start checking payment status immediately\n        const orderIdToCheck = response.order_id || response.data?.order_id || 'demo_order';\n        console.log('🔍 Starting payment confirmation check for order:', orderIdToCheck);\n\n        checkPaymentConfirmation(orderIdToCheck);\n\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('❌ Payment failed:', error);\n      setShowProcessingModal(false);\n      message.error('Payment failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const checkPaymentConfirmation = async (orderId) => {\n    console.log('🚀 Starting payment confirmation check for order:', orderId);\n    let isPolling = true;\n    let handleVisibilityChange;\n\n    try {\n      setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n\n      // Poll payment status every 2 seconds for optimal responsiveness\n      let attempts = 0;\n      const maxAttempts = 150; // 150 attempts * 2 seconds = 5 minutes\n\n      const pollPaymentStatus = async () => {\n        attempts++;\n        console.log(`🔍 Payment status check attempt ${attempts}/${maxAttempts} for order:`, orderId);\n\n        try {\n          const statusResponse = await checkPaymentStatus({ orderId });\n          console.log('📊 Payment status response:', statusResponse);\n          console.log('🔍 Checking payment conditions:');\n          console.log('  - Live payment:', statusResponse?.paymentStatus === 'paid' && statusResponse?.status === 'active');\n          console.log('  - Demo payment:', statusResponse?.status === 'completed' && statusResponse?.success === true);\n\n          if (statusResponse && (\n            (statusResponse.paymentStatus === 'paid' && statusResponse.status === 'active') ||\n            (statusResponse.status === 'completed' && statusResponse.success === true)\n          )) {\n            // Payment confirmed immediately!\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('🎉 Payment confirmed! Activating your subscription...');\n            console.log('✅ Payment confirmed, preparing to show success modal...');\n\n            // Show success INSTANTLY - no delay\n            console.log('🔄 Setting modal states - Processing: false, Success: true');\n            setShowProcessingModal(false);\n            setShowSuccessModal(true);\n            setPaymentLoading(null);\n            console.log('✅ Success modal state set to true');\n\n            // Refresh subscription data\n            checkCurrentSubscription();\n\n            // Show immediate success message\n            message.success({\n              content: '🎉 Payment confirmed! All features are now unlocked!',\n              duration: 5,\n              style: {\n                marginTop: '20vh',\n                fontSize: '16px'\n              }\n            });\n\n            // Start countdown for auto-navigation to hub\n            setAutoNavigateCountdown(5);\n            const countdownInterval = setInterval(() => {\n              setAutoNavigateCountdown(prev => {\n                if (prev <= 1) {\n                  clearInterval(countdownInterval);\n                  console.log('🏠 Auto-navigating to hub after successful payment...');\n                  setShowSuccessModal(false);\n                  window.location.href = '/user/hub';\n                  return null;\n                }\n                return prev - 1;\n              });\n            }, 1000);\n\n          } else if (attempts >= maxAttempts) {\n            // Timeout - but don't fail completely\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('⏰ Still waiting for confirmation. Please complete the payment on your phone.');\n\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setPaymentLoading(null); // Reset to null\n              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status or try again.');\n            }, 2000);\n\n          } else {\n            // Continue polling - NO TIME INDICATION, just encouraging message\n            setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n            setTimeout(pollPaymentStatus, 2000); // Check every 2 seconds for better performance\n          }\n\n        } catch (error) {\n          console.error('Payment status check error:', error);\n\n          // Handle specific error types\n          if (error.message && error.message.includes('404')) {\n            console.error('❌ Payment status endpoint not found (404)');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Payment verification service is temporarily unavailable. Please contact support or check your subscription status manually.');\n            return;\n          }\n\n          if (error.message && error.message.includes('401')) {\n            console.error('❌ Authentication required for payment status check');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Please login again to check payment status.');\n            return;\n          }\n\n          if (attempts >= maxAttempts) {\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Unable to confirm payment status. Please check your subscription status manually.');\n          } else {\n            // Continue polling even if there's an error (unless it's a critical error)\n            setTimeout(pollPaymentStatus, 1000);\n          }\n        }\n      };\n\n      // Add visibility change listener to check immediately when user returns to tab\n      handleVisibilityChange = () => {\n        if (!document.hidden && isPolling) {\n          console.log('User returned to tab, checking payment status immediately...');\n          setPaymentStatus('🔍 Checking payment status...');\n          // Trigger immediate check\n          setTimeout(() => pollPaymentStatus(), 100);\n        }\n      };\n\n      document.addEventListener('visibilitychange', handleVisibilityChange);\n\n      // Start polling immediately (no delay) - check right away\n      setTimeout(pollPaymentStatus, 500); // Start checking after 0.5 seconds\n\n    } catch (error) {\n      isPolling = false; // Stop polling\n      if (handleVisibilityChange) {\n        document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n      }\n      setShowProcessingModal(false);\n      message.error('Payment confirmation failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    \n    if (user?.subscriptionStatus === 'expired' || (subscriptionData && subscriptionData.status === 'expired')) {\n      return 'expired';\n    }\n    \n    return 'none';\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const getDaysRemaining = () => {\n    if (!subscriptionData?.endDate) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n\n  const subscriptionStatus = getSubscriptionStatus();\n\n  return (\n    <div className=\"subscription-page\">\n      <div className=\"subscription-container\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"subscription-header\"\n        >\n          {/* Debug button - remove in production */}\n          <button\n            onClick={() => {\n              console.log('🧪 Testing success modal...');\n              setSelectedPlan(plans[0] || { title: 'Test Plan', duration: 1, discountedPrice: 13000 });\n              setShowSuccessModal(true);\n            }}\n            style={{\n              position: 'fixed',\n              top: '10px',\n              right: '10px',\n              background: '#52c41a',\n              color: 'white',\n              border: 'none',\n              padding: '8px 16px',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              zIndex: 9999\n            }}\n          >\n            🧪 Test Success Modal\n          </button>\n          <h1 className=\"page-title\">\n            <FaCrown className=\"title-icon\" />\n            Subscription Management\n          </h1>\n          <p className=\"page-subtitle\">Manage your subscription and access premium features</p>\n        </motion.div>\n\n        {/* Current Subscription Status */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"current-subscription\"\n        >\n          <h2 className=\"section-title\">Current Subscription</h2>\n          \n          {subscriptionStatus === 'active' && (\n            <div className=\"subscription-card active\">\n              <div className=\"subscription-status\">\n                <FaCheckCircle className=\"status-icon active\" />\n                <span className=\"status-text\">Active Subscription</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCrown className=\"detail-icon\" />\n                  <span>Plan: {subscriptionData?.activePlan?.title || 'Premium Plan'}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expires: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCheckCircle className=\"detail-icon\" />\n                  <span>Days Remaining: {getDaysRemaining()}</span>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'expired' && (\n            <div className=\"subscription-card expired\">\n              <div className=\"subscription-status\">\n                <FaTimesCircle className=\"status-icon expired\" />\n                <span className=\"status-text\">Subscription Expired</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expired: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <p className=\"renewal-message\">\n                  Your subscription has expired. Choose a new plan below to continue accessing premium features.\n                </p>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'none' && (\n            <div className=\"subscription-card none\">\n              <div className=\"subscription-status\">\n                <FaUser className=\"status-icon none\" />\n                <span className=\"status-text\">Free Account</span>\n              </div>\n              <div className=\"subscription-details\">\n                <p className=\"upgrade-message\">\n                  You're currently using a free account. Upgrade to a premium plan to unlock all features.\n                </p>\n              </div>\n            </div>\n          )}\n        </motion.div>\n\n        {/* Available Plans */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"available-plans\"\n        >\n          <h2 className=\"section-title\">\n            {subscriptionStatus === 'active'\n              ? '🚀 Upgrade Your Plan'\n              : subscriptionStatus === 'expired'\n                ? '🔄 Renew Your Subscription'\n                : '🎯 Choose Your Plan'\n            }\n          </h2>\n\n          {/* Temporary Test Buttons */}\n          <div style={{ display: 'flex', gap: '10px', justifyContent: 'center', marginBottom: '20px' }}>\n            <button\n              onClick={testProcessingModal}\n              style={{\n                background: '#ff6b6b',\n                color: 'white',\n                border: 'none',\n                padding: '8px 16px',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              🧪 Test Processing Modal\n            </button>\n            <button\n              onClick={testSuccessModal}\n              style={{\n                background: '#51cf66',\n                color: 'white',\n                border: 'none',\n                padding: '8px 16px',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              🧪 Test Success Modal\n            </button>\n          </div>\n          <p className=\"section-subtitle\">\n            {subscriptionStatus === 'active'\n              ? 'Upgrade to a longer plan for better value and extended access'\n              : subscriptionStatus === 'expired'\n                ? 'Your subscription has expired. Renew now to continue accessing premium features'\n                : 'Select a subscription plan to unlock all premium features and start your learning journey'\n            }\n          </p>\n          \n          {loading ? (\n            <div className=\"loading-state\">\n              <div className=\"spinner\"></div>\n              <p>Loading plans...</p>\n            </div>\n          ) : plans.length === 0 ? (\n            <div className=\"no-plans-state\">\n              <div className=\"no-plans-icon\">📋</div>\n              <h3>No Plans Available</h3>\n              <p>Plans are currently being loaded. Please refresh the page or try again later.</p>\n              <button className=\"refresh-btn\" onClick={fetchPlans}>\n                🔄 Refresh Plans\n              </button>\n            </div>\n          ) : (\n            <div className=\"plans-grid\">\n              {plans.map((plan) => (\n                <motion.div\n                  key={plan._id}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  className=\"plan-card\"\n                >\n                  <div className=\"plan-header\">\n                    <h3 className=\"plan-title\">{plan.title}</h3>\n                    {plan.title?.toLowerCase().includes('standard') && (\n                      <span className=\"plan-badge\">🔥 Popular</span>\n                    )}\n                  </div>\n                  \n                  <div className=\"plan-pricing\">\n                    <div className=\"price-display\">\n                      <div className=\"current-price\">\n                        <span className=\"currency\">TZS</span>\n                        {plan.discountedPrice?.toLocaleString()}\n                      </div>\n                      {plan.actualPrice > plan.discountedPrice && (\n                        <>\n                          <span className=\"original-price\">{plan.actualPrice?.toLocaleString()} TZS</span>\n                          <span className=\"discount-badge\">\n                            {Math.round(((plan.actualPrice - plan.discountedPrice) / plan.actualPrice) * 100)}% OFF\n                          </span>\n                        </>\n                      )}\n                    </div>\n                    <div className=\"plan-duration\">\n                      <span className=\"duration-highlight\">{plan.duration}</span> month{plan.duration > 1 ? 's' : ''} access\n                    </div>\n                  </div>\n\n                  <div className=\"plan-features\">\n                    {plan.features?.slice(0, 5).map((feature, index) => (\n                      <div key={index} className=\"feature-item\">\n                        <FaCheckCircle className=\"feature-icon\" />\n                        <span>{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n\n                  <button\n                    className=\"select-plan-btn\"\n                    onClick={() => handlePlanSelect(plan)}\n                    disabled={paymentLoading === plan._id}\n                    style={{\n                      background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '12px',\n                      padding: '1rem 1.5rem',\n                      fontSize: '1rem',\n                      fontWeight: '600',\n                      cursor: paymentLoading === plan._id ? 'not-allowed' : 'pointer',\n                      transition: 'all 0.3s ease',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      gap: '0.5rem',\n                      width: '100%',\n                      opacity: paymentLoading === plan._id ? 0.6 : 1\n                    }}\n                    onMouseEnter={(e) => {\n                      if (paymentLoading !== plan._id) {\n                        e.target.style.background = 'linear-gradient(135deg, #1d4ed8, #1e40af)';\n                        e.target.style.transform = 'translateY(-2px)';\n                        e.target.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.4)';\n                      }\n                    }}\n                    onMouseLeave={(e) => {\n                      if (paymentLoading !== plan._id) {\n                        e.target.style.background = 'linear-gradient(135deg, #3b82f6, #1d4ed8)';\n                        e.target.style.transform = 'translateY(0)';\n                        e.target.style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.3)';\n                      }\n                    }}\n                  >\n                    <FaCreditCard className=\"btn-icon\" />\n                    {paymentLoading === plan._id\n                      ? 'Processing...'\n                      : subscriptionStatus === 'active'\n                        ? 'Click to Upgrade'\n                        : subscriptionStatus === 'expired'\n                          ? 'Click to Renew'\n                          : 'Click to Pay'\n                    }\n                  </button>\n                </motion.div>\n              ))}\n            </div>\n          )}\n        </motion.div>\n\n        {/* Phone Number Warning */}\n        {(!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            className=\"phone-warning\"\n          >\n            <div className=\"warning-content\">\n              <FaTimesCircle className=\"warning-icon\" />\n              <div>\n                <h4>Phone Number Required</h4>\n                <p>Please update your phone number in your profile to subscribe to a plan.</p>\n                <button \n                  className=\"update-phone-btn\"\n                  onClick={() => window.location.href = '/profile'}\n                >\n                  Update Phone Number\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Professional Payment Processing Modal */}\n        {showProcessingModal && (\n          <div\n            className=\"payment-modal-overlay\"\n            onClick={(e) => e.target === e.currentTarget && handleCloseProcessingModal()}\n          >\n            <div className=\"payment-modal-container processing\">\n              {/* Close Button */}\n              <button\n                className=\"modal-close-btn\"\n                onClick={handleCloseProcessingModal}\n                aria-label=\"Close modal\"\n              >\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                  <path d=\"M18 6L6 18M6 6L18 18\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                </svg>\n              </button>\n\n              {/* Header */}\n              <div className=\"modal-header processing\">\n                <div className=\"processing-icon\">\n                  <div className=\"spinner\"></div>\n                  <svg className=\"payment-icon\" width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\">\n                    <path d=\"M2 12C2 8.229 2 6.343 3.172 5.172C4.343 4 6.229 4 10 4H14C17.771 4 19.657 4 20.828 5.172C22 6.343 22 8.229 22 12C22 15.771 22 17.657 20.828 18.828C19.657 20 17.771 20 14 20H10C6.229 20 4.343 20 3.172 18.828C2 17.657 2 15.771 2 12Z\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                    <path d=\"M10 16H6\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\"/>\n                    <path d=\"M14 16H12.5\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\"/>\n                    <path d=\"M2 10L22 10\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\"/>\n                  </svg>\n                </div>\n                <h2>Processing Payment</h2>\n                <p>Secure transaction in progress</p>\n              </div>\n\n              {/* Content */}\n              <div className=\"modal-content\">\n                {/* Status */}\n                <div className=\"status-card\">\n                  <div className=\"status-indicator processing\"></div>\n                  <p className=\"status-text\">{paymentStatus}</p>\n                </div>\n\n                {/* Plan Info */}\n                <div className=\"plan-info-card\">\n                  <h3>{selectedPlan?.title}</h3>\n                  <div className=\"plan-details\">\n                    <div className=\"detail-row\">\n                      <span>Amount</span>\n                      <strong>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</strong>\n                    </div>\n                    <div className=\"detail-row\">\n                      <span>Duration</span>\n                      <strong>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</strong>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Phone Instructions */}\n                <div className=\"instruction-card\">\n                  <div className=\"instruction-header\">\n                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M16.5562 12.9062L16.1007 13.359C16.1007 13.359 15.0181 14.4355 12.0631 11.4972C9.10812 8.55901 10.1907 7.48257 10.1907 7.48257L10.4775 7.19738C11.1841 6.49484 11.2507 5.36691 10.6342 4.54348L9.37326 2.85908C8.61028 1.83992 7.13596 1.70529 6.26145 2.57483L4.69185 4.13552C4.25823 4.56668 3.96765 5.12559 4.00289 5.74561C4.09304 7.33182 4.81071 10.7447 8.81536 14.7266C13.0621 18.9492 17.0468 19.117 18.6763 18.9651C19.1917 18.9171 19.6399 18.6546 20.0011 18.2954L21.4217 16.883C22.3806 15.9295 22.1102 14.2949 20.8833 13.628L18.9728 12.5894C18.1672 12.1515 17.1858 12.2801 16.5562 12.9062Z\" fill=\"currentColor\"/>\n                    </svg>\n                    <span>Check Your Phone</span>\n                  </div>\n                  <div className=\"phone-number\">{user?.phoneNumber}</div>\n                  <div className=\"instruction-steps\">\n                    <div className=\"step\">1. You'll receive an SMS with payment instructions</div>\n                    <div className=\"step\">2. Follow the SMS steps to confirm payment</div>\n                    <div className=\"step\">3. Complete the mobile money transaction</div>\n                  </div>\n                </div>\n\n                {/* Try Again */}\n                {showTryAgain && (\n                  <div className=\"try-again-card\">\n                    <p>Taking longer than expected?</p>\n                    <button className=\"try-again-btn\" onClick={handleTryAgain}>\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M4 12a8 8 0 018-8V2.5\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                        <path d=\"M12 4L9 7L12 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      </svg>\n                      Try Again\n                    </button>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n\n\n        {/* Professional Success Modal */}\n        {showSuccessModal && (\n          <div className=\"payment-modal-overlay\">\n            <div className=\"payment-modal-container success\">\n              {/* Close Button */}\n              <button\n                className=\"modal-close-btn\"\n                onClick={() => {\n                  setAutoNavigateCountdown(null);\n                  setShowSuccessModal(false);\n                }}\n                aria-label=\"Close modal\"\n              >\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                  <path d=\"M18 6L6 18M6 6L18 18\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                </svg>\n              </button>\n\n              {/* Header */}\n              <div className=\"modal-header success\">\n                <div className=\"success-icon\">\n                  <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\">\n                    <path d=\"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z\" fill=\"#22c55e\" fillOpacity=\"0.2\"/>\n                    <path d=\"M16 9L10.5 14.5L8 12\" stroke=\"#22c55e\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    <path d=\"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z\" stroke=\"#22c55e\" strokeWidth=\"2\"/>\n                  </svg>\n                </div>\n                <h2>Payment Successful!</h2>\n                <p>Welcome to {selectedPlan?.title}!</p>\n              </div>\n              {/* Content */}\n              <div className=\"modal-content\">\n                {/* Auto-Navigation Notice */}\n                {autoNavigateCountdown && (\n                  <div className=\"countdown-card\">\n                    <div className=\"countdown-icon\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                    </div>\n                    <p>Redirecting to Hub in {autoNavigateCountdown} seconds...</p>\n                  </div>\n                )}\n\n                {/* Plan Summary */}\n                <div className=\"plan-summary-card\">\n                  <h3>Subscription Activated</h3>\n                  <div className=\"plan-details\">\n                    <div className=\"detail-row\">\n                      <span>Plan</span>\n                      <strong>{selectedPlan?.title}</strong>\n                    </div>\n                    <div className=\"detail-row\">\n                      <span>Duration</span>\n                      <strong>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</strong>\n                    </div>\n                    <div className=\"detail-row\">\n                      <span>Amount Paid</span>\n                      <strong>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</strong>\n                    </div>\n                    <div className=\"detail-row status\">\n                      <span>Status</span>\n                      <div className=\"status-badge\">\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                          <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                        </svg>\n                        Active\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Features Unlocked */}\n                <div className=\"features-card\">\n                  <h3>🚀 Premium Features Unlocked</h3>\n                  <div className=\"features-grid\">\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Unlimited Quizzes</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>AI Assistant</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Study Materials</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Progress Tracking</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Learning Videos</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Forum Access</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Action Buttons */}\n                <div className=\"modal-actions\">\n                  <button\n                    className=\"primary-btn\"\n                    onClick={() => {\n                      setAutoNavigateCountdown(null);\n                      setShowSuccessModal(false);\n                      window.location.href = '/user/hub';\n                    }}\n                  >\n                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      <polyline points=\"9,22 9,12 15,12 15,22\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                    </svg>\n                    Continue to Hub {autoNavigateCountdown ? `(${autoNavigateCountdown}s)` : ''}\n                  </button>\n                  <button\n                    className=\"secondary-btn\"\n                    onClick={() => {\n                      setAutoNavigateCountdown(null);\n                      setShowSuccessModal(false);\n                    }}\n                  >\n                    Close\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n\n\n        {/* Upgrade Restriction Modal */}\n        <UpgradeRestrictionModal\n          visible={showUpgradeRestriction}\n          onClose={() => setShowUpgradeRestriction(false)}\n          currentPlan={plans.find(p => p._id === subscriptionData?.activePlan) || subscriptionData?.plan}\n          subscription={subscriptionData}\n          user={user}\n        />\n\n        {/* Subscription Expired Modal */}\n        <SubscriptionExpiredModal\n          visible={showExpiredModal}\n          onClose={() => setShowExpiredModal(false)}\n          onRenew={handleRenewSubscription}\n          subscription={subscriptionData}\n          user={user}\n          plans={plans}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default Subscription;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,OAAO,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,EAAEC,YAAY,EAAEC,MAAM,QAAQ,gBAAgB;AAC3G,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,2BAA2B;AAC1E,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,uBAAuB,MAAM,qEAAqE;AACzG,OAAOC,wBAAwB,MAAM,uEAAuE;AAC5G,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACzB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACkC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACAC,SAAS,CAAC,MAAM;IACdmC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEH,mBAAmB,CAAC;EAC9E,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;EACzB,MAAM,CAACI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4C,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoD,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM;IAAEsD;EAAK,CAAC,GAAGpD,WAAW,CAAEqD,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE;EAAiB,CAAC,GAAGtD,WAAW,CAAEqD,KAAK,IAAKA,KAAK,CAACE,YAAY,CAAC;EACvE,MAAMC,QAAQ,GAAGvD,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMwD,WAAW,GAAG,CAClB;IACEC,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,kBAAkB;IACzBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,CAC/B;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,EACD;IACEP,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,EAC9B,kBAAkB,CACnB;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,CACF;EAEDlE,SAAS,CAAC,MAAM;IACdmE,UAAU,CAAC,CAAC;IACZC,wBAAwB,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApE,SAAS,CAAC,MAAM;IACd,IAAIiC,mBAAmB,IAAII,gBAAgB,EAAE;MAC3C;MACAgC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC,CAAC,MAAM;MACL;MACAH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;IACnC;;IAEA;IACA,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;IACnC,CAAC;EACH,CAAC,EAAE,CAACvC,mBAAmB,EAAEI,gBAAgB,CAAC,CAAC;;EAE3C;EACArC,SAAS,CAAC,MAAM;IACd,MAAMyE,uBAAuB,GAAGA,CAAA,KAAM;MACpC,MAAMC,aAAa,GAAGL,QAAQ,CAACM,gBAAgB,CAAC,gBAAgB,CAAC;MACjED,aAAa,CAACE,OAAO,CAACC,OAAO,IAAI;QAC/B,IAAIA,OAAO,CAACC,YAAY,GAAGD,OAAO,CAACE,YAAY,EAAE;UAC/CF,OAAO,CAACG,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;QACrC,CAAC,MAAM;UACLJ,OAAO,CAACG,SAAS,CAACE,MAAM,CAAC,YAAY,CAAC;QACxC;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,IAAIjD,mBAAmB,IAAII,gBAAgB,EAAE;MAC3C;MACA8C,UAAU,CAACV,uBAAuB,EAAE,GAAG,CAAC;;MAExC;MACAW,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEZ,uBAAuB,CAAC;MAE1D,OAAO,MAAM;QACXW,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEb,uBAAuB,CAAC;MAC/D,CAAC;IACH;EACF,CAAC,EAAE,CAACxC,mBAAmB,EAAEI,gBAAgB,CAAC,CAAC;;EAE3C;EACArC,SAAS,CAAC,MAAM;IACd,IAAIuD,gBAAgB,IAAIgC,qBAAqB,CAAC,CAAC,EAAE;MAC/CpD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrDU,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM;MACLA,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC,EAAE,CAACS,gBAAgB,CAAC,CAAC;EAEtB,MAAMY,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFrC,UAAU,CAAC,IAAI,CAAC;MAChBK,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,MAAMoD,QAAQ,GAAG,MAAM7E,QAAQ,CAAC,CAAC;MACjCwB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoD,QAAQ,CAAC;MAExC,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACjE/D,QAAQ,CAAC4D,QAAQ,CAACE,IAAI,CAAC;QACvBvD,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEoD,QAAQ,CAACE,IAAI,CAAC;MACnE,CAAC,MAAM,IAAIE,KAAK,CAACC,OAAO,CAACL,QAAQ,CAAC,IAAIA,QAAQ,CAACG,MAAM,GAAG,CAAC,EAAE;QACzD;QACA/D,QAAQ,CAAC4D,QAAQ,CAAC;QAClBrD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEoD,QAAQ,CAAC;MAC1D,CAAC,MAAM;QACLrD,OAAO,CAAC2D,IAAI,CAAC,uCAAuC,CAAC;QACrDlE,QAAQ,CAAC8B,WAAW,CAAC;QACrBtD,OAAO,CAAC2F,IAAI,CAAC,qDAAqD,CAAC;MACrE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd7D,OAAO,CAAC6D,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD7D,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1CR,QAAQ,CAAC8B,WAAW,CAAC;MACrBtD,OAAO,CAAC6F,OAAO,CAAC,iEAAiE,CAAC;IACpF,CAAC,SAAS;MACRnE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsC,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACF,MAAMoB,QAAQ,GAAG,MAAM3E,kBAAkB,CAAC,CAAC;MAC3CsB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEoD,QAAQ,CAAC;IAChD,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACd7D,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC7C;EACF,CAAC;;EAED;EACA,MAAMmD,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAAChC,gBAAgB,EAAE,OAAO,IAAI;;IAElC;IACA,IAAI,CAACA,gBAAgB,CAAC2C,OAAO,EAAE,OAAO,IAAI;;IAE1C;IACA,IAAI3C,gBAAgB,CAACd,aAAa,KAAK,MAAM,EAAE,OAAO,IAAI;;IAE1D;IACA,IAAIc,gBAAgB,CAACW,MAAM,KAAK,QAAQ,EAAE,OAAO,IAAI;;IAErD;IACA,MAAMgC,OAAO,GAAG,IAAIC,IAAI,CAAC5C,gBAAgB,CAAC2C,OAAO,CAAC;IAClD,MAAME,KAAK,GAAG,IAAID,IAAI,CAAC,CAAC;IACxBC,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5BH,OAAO,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAE9B,OAAOH,OAAO,GAAGE,KAAK;EACxB,CAAC;;EAED;EACA,MAAME,uBAAuB,GAAG,MAAO/D,YAAY,IAAK;IACtDO,mBAAmB,CAAC,KAAK,CAAC;IAC1B,MAAMyD,gBAAgB,CAAChE,YAAY,CAAC;EACtC,CAAC;;EAED;EACA,MAAMiE,0BAA0B,GAAGA,CAAA,KAAM;IACvCtE,sBAAsB,CAAC,KAAK,CAAC;IAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IACzBkB,eAAe,CAAC,KAAK,CAAC;IACtBF,sBAAsB,CAAC,IAAI,CAAC;IAC5BN,gBAAgB,CAAC,EAAE,CAAC;IACpBtC,OAAO,CAAC2F,IAAI,CAAC,uDAAuD,CAAC;EACvE,CAAC;;EAED;EACA,MAAMU,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIlE,YAAY,EAAE;MAChBW,eAAe,CAAC,KAAK,CAAC;MACtBF,sBAAsB,CAAC,IAAI,CAAC;MAC5BuD,gBAAgB,CAAChE,YAAY,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAMmE,gBAAgB,GAAGA,CAAA,KAAM;IAC7BvE,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC1CF,sBAAsB,CAAC,KAAK,CAAC;IAC7BI,mBAAmB,CAAC,IAAI,CAAC;IACzBN,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM2E,mBAAmB,GAAGA,CAAA,KAAM;IAChCxE,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7CF,sBAAsB,CAAC,IAAI,CAAC;IAC5BQ,gBAAgB,CAAC,6BAA6B,CAAC;IAC/CF,eAAe,CAACb,KAAK,CAAC,CAAC,CAAC,IAAI;MAAEiC,KAAK,EAAE,WAAW;MAAEG,eAAe,EAAE,IAAI;MAAEE,QAAQ,EAAE;IAAE,CAAC,CAAC;EACzF,CAAC;EAED,MAAMsC,gBAAgB,GAAG,MAAOK,IAAI,IAAK;IACvC;IACA,IAAIrD,gBAAgB,IAAIA,gBAAgB,CAACW,MAAM,KAAK,QAAQ,IAAIX,gBAAgB,CAACd,aAAa,KAAK,MAAM,EAAE;MACzGN,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEmB,gBAAgB,CAAC;MACzEX,yBAAyB,CAAC,IAAI,CAAC;MAC/B;IACF;IAEA,IAAI,CAACS,IAAI,CAACwD,WAAW,IAAI,CAAC,gBAAgB,CAACC,IAAI,CAACzD,IAAI,CAACwD,WAAW,CAAC,EAAE;MACjEzG,OAAO,CAAC4F,KAAK,CAAC,oEAAoE,CAAC;MACnF;IACF;IAEA,IAAI;MAAA,IAAAe,UAAA;MACF5E,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEwE,IAAI,CAAChD,KAAK,CAAC;MACxDzB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;;MAEzD;MACAI,eAAe,CAACoE,IAAI,CAAC;MACrB5E,iBAAiB,CAAC4E,IAAI,CAACjD,GAAG,CAAC;MAC3BzB,sBAAsB,CAAC,IAAI,CAAC;MAC5BgB,eAAe,CAAC,KAAK,CAAC;MACtBF,sBAAsB,CAACmD,IAAI,CAACa,GAAG,CAAC,CAAC,CAAC;MAClCtE,gBAAgB,CAAC,sCAAsC,CAAC;MAExDP,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;MAEvD;MACA,MAAM,IAAI6E,OAAO,CAACC,OAAO,IAAI/B,UAAU,CAAC+B,OAAO,EAAE,GAAG,CAAC,CAAC;;MAEtD;MACA/B,UAAU,CAAC,MAAM;QACfjC,eAAe,CAAC,IAAI,CAAC;MACvB,CAAC,EAAE,KAAK,CAAC;MAET,MAAMiE,WAAW,GAAG;QAClBP,IAAI,EAAEA,IAAI;QACVQ,MAAM,EAAE/D,IAAI,CAACM,GAAG;QAChB0D,SAAS,EAAEhE,IAAI,CAACwD,WAAW;QAC3BS,SAAS,EAAEjE,IAAI,CAACkE,KAAK,IAAK,IAAAR,UAAA,GAAE1D,IAAI,CAACmE,IAAI,cAAAT,UAAA,uBAATA,UAAA,CAAWU,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC3E,CAAC;MAEDhF,gBAAgB,CAAC,0CAA0C,CAAC;MAC5D,MAAM8C,QAAQ,GAAG,MAAM5E,UAAU,CAACuG,WAAW,CAAC;MAE9C,IAAI3B,QAAQ,CAACC,OAAO,EAAE;QAAA,IAAAkC,cAAA;QACpBjF,gBAAgB,CAAC,wDAAwD,CAAC;QAE1EP,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEoD,QAAQ,CAAC;QAC7CrD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEoD,QAAQ,CAACoC,QAAQ,CAAC;;QAE9C;QACAxH,OAAO,CAACqF,OAAO,CAAC;UACdZ,OAAO,EAAG,8CAA6CxB,IAAI,CAACwD,WAAY,sCAAqC;UAC7G5C,QAAQ,EAAE,CAAC;UACXM,KAAK,EAAE;YACLsD,SAAS,EAAE;UACb;QACF,CAAC,CAAC;;QAEF;QACA,MAAMC,cAAc,GAAGtC,QAAQ,CAACoC,QAAQ,MAAAD,cAAA,GAAInC,QAAQ,CAACE,IAAI,cAAAiC,cAAA,uBAAbA,cAAA,CAAeC,QAAQ,KAAI,YAAY;QACnFzF,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE0F,cAAc,CAAC;QAEhFC,wBAAwB,CAACD,cAAc,CAAC;MAE1C,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAACxC,QAAQ,CAACpF,OAAO,IAAI,gBAAgB,CAAC;MACvD;IACF,CAAC,CAAC,OAAO4F,KAAK,EAAE;MACd7D,OAAO,CAAC6D,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC9D,sBAAsB,CAAC,KAAK,CAAC;MAC7B9B,OAAO,CAAC4F,KAAK,CAAC,kBAAkB,GAAGA,KAAK,CAAC5F,OAAO,CAAC;MACjD4B,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3B;EACF,CAAC;;EAED,MAAM+F,wBAAwB,GAAG,MAAOE,OAAO,IAAK;IAClD9F,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE6F,OAAO,CAAC;IACzE,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,sBAAsB;IAE1B,IAAI;MACFzF,gBAAgB,CAAC,0EAA0E,CAAC;;MAE5F;MACA,IAAI0F,QAAQ,GAAG,CAAC;MAChB,MAAMC,WAAW,GAAG,GAAG,CAAC,CAAC;;MAEzB,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;QACpCF,QAAQ,EAAE;QACVjG,OAAO,CAACC,GAAG,CAAE,mCAAkCgG,QAAS,IAAGC,WAAY,aAAY,EAAEJ,OAAO,CAAC;QAE7F,IAAI;UACF,MAAMM,cAAc,GAAG,MAAM1H,kBAAkB,CAAC;YAAEoH;UAAQ,CAAC,CAAC;UAC5D9F,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEmG,cAAc,CAAC;UAC1DpG,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9CD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,CAAAmG,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE9F,aAAa,MAAK,MAAM,IAAI,CAAA8F,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAErE,MAAM,MAAK,QAAQ,CAAC;UACjH/B,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,CAAAmG,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAErE,MAAM,MAAK,WAAW,IAAI,CAAAqE,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE9C,OAAO,MAAK,IAAI,CAAC;UAE5G,IAAI8C,cAAc,KACfA,cAAc,CAAC9F,aAAa,KAAK,MAAM,IAAI8F,cAAc,CAACrE,MAAM,KAAK,QAAQ,IAC7EqE,cAAc,CAACrE,MAAM,KAAK,WAAW,IAAIqE,cAAc,CAAC9C,OAAO,KAAK,IAAK,CAC3E,EAAE;YACD;YACAyC,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1B9D,QAAQ,CAACiB,mBAAmB,CAAC,kBAAkB,EAAE6C,sBAAsB,CAAC,CAAC,CAAC;YAC5E;;YAEAzF,gBAAgB,CAAC,uDAAuD,CAAC;YACzEP,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;;YAEtE;YACAD,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;YACzEF,sBAAsB,CAAC,KAAK,CAAC;YAC7BI,mBAAmB,CAAC,IAAI,CAAC;YACzBN,iBAAiB,CAAC,IAAI,CAAC;YACvBG,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;YAEhD;YACAgC,wBAAwB,CAAC,CAAC;;YAE1B;YACAhE,OAAO,CAACqF,OAAO,CAAC;cACdZ,OAAO,EAAE,sDAAsD;cAC/DZ,QAAQ,EAAE,CAAC;cACXM,KAAK,EAAE;gBACLsD,SAAS,EAAE,MAAM;gBACjBW,QAAQ,EAAE;cACZ;YACF,CAAC,CAAC;;YAEF;YACApF,wBAAwB,CAAC,CAAC,CAAC;YAC3B,MAAMqF,iBAAiB,GAAGC,WAAW,CAAC,MAAM;cAC1CtF,wBAAwB,CAACuF,IAAI,IAAI;gBAC/B,IAAIA,IAAI,IAAI,CAAC,EAAE;kBACbC,aAAa,CAACH,iBAAiB,CAAC;kBAChCtG,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;kBACpEE,mBAAmB,CAAC,KAAK,CAAC;kBAC1B8C,MAAM,CAACyD,QAAQ,CAACC,IAAI,GAAG,WAAW;kBAClC,OAAO,IAAI;gBACb;gBACA,OAAOH,IAAI,GAAG,CAAC;cACjB,CAAC,CAAC;YACJ,CAAC,EAAE,IAAI,CAAC;UAEV,CAAC,MAAM,IAAIP,QAAQ,IAAIC,WAAW,EAAE;YAClC;YACAH,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1B9D,QAAQ,CAACiB,mBAAmB,CAAC,kBAAkB,EAAE6C,sBAAsB,CAAC,CAAC,CAAC;YAC5E;;YAEAzF,gBAAgB,CAAC,8EAA8E,CAAC;YAEhGyC,UAAU,CAAC,MAAM;cACfjD,sBAAsB,CAAC,KAAK,CAAC;cAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;cACzB5B,OAAO,CAAC6F,OAAO,CAAC,0GAA0G,CAAC;YAC7H,CAAC,EAAE,IAAI,CAAC;UAEV,CAAC,MAAM;YACL;YACAvD,gBAAgB,CAAC,0EAA0E,CAAC;YAC5FyC,UAAU,CAACmD,iBAAiB,EAAE,IAAI,CAAC,CAAC,CAAC;UACvC;QAEF,CAAC,CAAC,OAAOtC,KAAK,EAAE;UACd7D,OAAO,CAAC6D,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;UAEnD;UACA,IAAIA,KAAK,CAAC5F,OAAO,IAAI4F,KAAK,CAAC5F,OAAO,CAAC2I,QAAQ,CAAC,KAAK,CAAC,EAAE;YAClD5G,OAAO,CAAC6D,KAAK,CAAC,2CAA2C,CAAC;YAC1DkC,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1B9D,QAAQ,CAACiB,mBAAmB,CAAC,kBAAkB,EAAE6C,sBAAsB,CAAC;YAC1E;YACAjG,sBAAsB,CAAC,KAAK,CAAC;YAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;YACzB5B,OAAO,CAAC4F,KAAK,CAAC,6HAA6H,CAAC;YAC5I;UACF;UAEA,IAAIA,KAAK,CAAC5F,OAAO,IAAI4F,KAAK,CAAC5F,OAAO,CAAC2I,QAAQ,CAAC,KAAK,CAAC,EAAE;YAClD5G,OAAO,CAAC6D,KAAK,CAAC,oDAAoD,CAAC;YACnEkC,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1B9D,QAAQ,CAACiB,mBAAmB,CAAC,kBAAkB,EAAE6C,sBAAsB,CAAC;YAC1E;YACAjG,sBAAsB,CAAC,KAAK,CAAC;YAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;YACzB5B,OAAO,CAAC4F,KAAK,CAAC,6CAA6C,CAAC;YAC5D;UACF;UAEA,IAAIoC,QAAQ,IAAIC,WAAW,EAAE;YAC3BH,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1B9D,QAAQ,CAACiB,mBAAmB,CAAC,kBAAkB,EAAE6C,sBAAsB,CAAC,CAAC,CAAC;YAC5E;;YACAjG,sBAAsB,CAAC,KAAK,CAAC;YAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;YACzB5B,OAAO,CAAC4F,KAAK,CAAC,mFAAmF,CAAC;UACpG,CAAC,MAAM;YACL;YACAb,UAAU,CAACmD,iBAAiB,EAAE,IAAI,CAAC;UACrC;QACF;MACF,CAAC;;MAED;MACAH,sBAAsB,GAAGA,CAAA,KAAM;QAC7B,IAAI,CAAC9D,QAAQ,CAAC2E,MAAM,IAAId,SAAS,EAAE;UACjC/F,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;UAC3EM,gBAAgB,CAAC,+BAA+B,CAAC;UACjD;UACAyC,UAAU,CAAC,MAAMmD,iBAAiB,CAAC,CAAC,EAAE,GAAG,CAAC;QAC5C;MACF,CAAC;MAEDjE,QAAQ,CAACgB,gBAAgB,CAAC,kBAAkB,EAAE8C,sBAAsB,CAAC;;MAErE;MACAhD,UAAU,CAACmD,iBAAiB,EAAE,GAAG,CAAC,CAAC,CAAC;IAEtC,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdkC,SAAS,GAAG,KAAK,CAAC,CAAC;MACnB,IAAIC,sBAAsB,EAAE;QAC1B9D,QAAQ,CAACiB,mBAAmB,CAAC,kBAAkB,EAAE6C,sBAAsB,CAAC,CAAC,CAAC;MAC5E;;MACAjG,sBAAsB,CAAC,KAAK,CAAC;MAC7B9B,OAAO,CAAC4F,KAAK,CAAC,+BAA+B,GAAGA,KAAK,CAAC5F,OAAO,CAAC;MAC9D4B,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3B;EACF,CAAC;;EAED,MAAMiH,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI1F,gBAAgB,IAAIA,gBAAgB,CAACd,aAAa,KAAK,MAAM,IAAIc,gBAAgB,CAACW,MAAM,KAAK,QAAQ,EAAE;MACzG,MAAMgC,OAAO,GAAG,IAAIC,IAAI,CAAC5C,gBAAgB,CAAC2C,OAAO,CAAC;MAClD,MAAMc,GAAG,GAAG,IAAIb,IAAI,CAAC,CAAC;MACtB,IAAID,OAAO,GAAGc,GAAG,EAAE;QACjB,OAAO,QAAQ;MACjB;IACF;IAEA,IAAI,CAAA3D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6F,kBAAkB,MAAK,SAAS,IAAK3F,gBAAgB,IAAIA,gBAAgB,CAACW,MAAM,KAAK,SAAU,EAAE;MACzG,OAAO,SAAS;IAClB;IAEA,OAAO,MAAM;EACf,CAAC;EAED,MAAMiF,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAIjD,IAAI,CAACiD,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAAClG,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAE2C,OAAO,GAAE,OAAO,CAAC;IACxC,MAAMA,OAAO,GAAG,IAAIC,IAAI,CAAC5C,gBAAgB,CAAC2C,OAAO,CAAC;IAClD,MAAMc,GAAG,GAAG,IAAIb,IAAI,CAAC,CAAC;IACtB,MAAMuD,QAAQ,GAAGxD,OAAO,GAAGc,GAAG;IAC9B,MAAM2C,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOE,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAAC;EAC9B,CAAC;EAED,MAAMT,kBAAkB,GAAGD,qBAAqB,CAAC,CAAC;EAElD,oBACE9H,OAAA;IAAK4I,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChC7I,OAAA;MAAK4I,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErC7I,OAAA,CAAChB,MAAM,CAAC8J,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAErG,QAAQ,EAAE;QAAI,CAAE;QAC9B8F,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAG/B7I,OAAA;UACEoJ,OAAO,EAAEA,CAAA,KAAM;YACbpI,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;YAC1CI,eAAe,CAACb,KAAK,CAAC,CAAC,CAAC,IAAI;cAAEiC,KAAK,EAAE,WAAW;cAAEK,QAAQ,EAAE,CAAC;cAAEF,eAAe,EAAE;YAAM,CAAC,CAAC;YACxFzB,mBAAmB,CAAC,IAAI,CAAC;UAC3B,CAAE;UACFiC,KAAK,EAAE;YACLiG,QAAQ,EAAE,OAAO;YACjBC,GAAG,EAAE,MAAM;YACXC,KAAK,EAAE,MAAM;YACbC,UAAU,EAAE,SAAS;YACrBC,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE,UAAU;YACnBC,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,SAAS;YACjBC,MAAM,EAAE;UACV,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlK,OAAA;UAAI4I,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACxB7I,OAAA,CAACd,OAAO;YAAC0J,SAAS,EAAC;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2BAEpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlK,OAAA;UAAG4I,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoD;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eAGblK,OAAA,CAAChB,MAAM,CAAC8J,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAErG,QAAQ,EAAE,GAAG;UAAEqH,KAAK,EAAE;QAAI,CAAE;QAC1CvB,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBAEhC7I,OAAA;UAAI4I,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoB;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEtDnC,kBAAkB,KAAK,QAAQ,iBAC9B/H,OAAA;UAAK4I,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvC7I,OAAA;YAAK4I,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC7I,OAAA,CAACZ,aAAa;cAACwJ,SAAS,EAAC;YAAoB;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDlK,OAAA;cAAM4I,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAmB;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNlK,OAAA;YAAK4I,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC7I,OAAA;cAAK4I,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7I,OAAA,CAACd,OAAO;gBAAC0J,SAAS,EAAC;cAAa;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnClK,OAAA;gBAAA6I,QAAA,GAAM,QAAM,EAAC,CAAAzG,gBAAgB,aAAhBA,gBAAgB,wBAAA/B,qBAAA,GAAhB+B,gBAAgB,CAAEgI,UAAU,cAAA/J,qBAAA,uBAA5BA,qBAAA,CAA8BoC,KAAK,KAAI,cAAc;cAAA;gBAAAsH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACNlK,OAAA;cAAK4I,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7I,OAAA,CAACb,aAAa;gBAACyJ,SAAS,EAAC;cAAa;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzClK,OAAA;gBAAA6I,QAAA,GAAM,WAAS,EAACb,UAAU,CAAC5F,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2C,OAAO,CAAC;cAAA;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNlK,OAAA;cAAK4I,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7I,OAAA,CAACZ,aAAa;gBAACwJ,SAAS,EAAC;cAAa;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzClK,OAAA;gBAAA6I,QAAA,GAAM,kBAAgB,EAACP,gBAAgB,CAAC,CAAC;cAAA;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAnC,kBAAkB,KAAK,SAAS,iBAC/B/H,OAAA;UAAK4I,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxC7I,OAAA;YAAK4I,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC7I,OAAA,CAACX,aAAa;cAACuJ,SAAS,EAAC;YAAqB;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDlK,OAAA;cAAM4I,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAoB;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACNlK,OAAA;YAAK4I,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC7I,OAAA;cAAK4I,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7I,OAAA,CAACb,aAAa;gBAACyJ,SAAS,EAAC;cAAa;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzClK,OAAA;gBAAA6I,QAAA,GAAM,WAAS,EAACb,UAAU,CAAC5F,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2C,OAAO,CAAC;cAAA;gBAAAgF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNlK,OAAA;cAAG4I,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAnC,kBAAkB,KAAK,MAAM,iBAC5B/H,OAAA;UAAK4I,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC7I,OAAA;YAAK4I,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC7I,OAAA,CAACT,MAAM;cAACqJ,SAAS,EAAC;YAAkB;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvClK,OAAA;cAAM4I,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAY;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNlK,OAAA;YAAK4I,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnC7I,OAAA;cAAG4I,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAGblK,OAAA,CAAChB,MAAM,CAAC8J,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAErG,QAAQ,EAAE,GAAG;UAAEqH,KAAK,EAAE;QAAI,CAAE;QAC1CvB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE3B7I,OAAA;UAAI4I,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC1Bd,kBAAkB,KAAK,QAAQ,GAC5B,sBAAsB,GACtBA,kBAAkB,KAAK,SAAS,GAC9B,4BAA4B,GAC5B;QAAqB;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEzB,CAAC,eAGLlK,OAAA;UAAKoD,KAAK,EAAE;YAAEiH,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAA3B,QAAA,gBAC3F7I,OAAA;YACEoJ,OAAO,EAAE5D,mBAAoB;YAC7BpC,KAAK,EAAE;cACLoG,UAAU,EAAE,SAAS;cACrBC,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE,UAAU;cACnBC,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAE,SAAS;cACjBxC,QAAQ,EAAE;YACZ,CAAE;YAAAwB,QAAA,EACH;UAED;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlK,OAAA;YACEoJ,OAAO,EAAE7D,gBAAiB;YAC1BnC,KAAK,EAAE;cACLoG,UAAU,EAAE,SAAS;cACrBC,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE,UAAU;cACnBC,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAE,SAAS;cACjBxC,QAAQ,EAAE;YACZ,CAAE;YAAAwB,QAAA,EACH;UAED;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNlK,OAAA;UAAG4I,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC5Bd,kBAAkB,KAAK,QAAQ,GAC5B,+DAA+D,GAC/DA,kBAAkB,KAAK,SAAS,GAC9B,iFAAiF,GACjF;QAA2F;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEhG,CAAC,EAEHxJ,OAAO,gBACNV,OAAA;UAAK4I,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B7I,OAAA;YAAK4I,SAAS,EAAC;UAAS;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BlK,OAAA;YAAA6I,QAAA,EAAG;UAAgB;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,GACJ1J,KAAK,CAACgE,MAAM,KAAK,CAAC,gBACpBxE,OAAA;UAAK4I,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B7I,OAAA;YAAK4I,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvClK,OAAA;YAAA6I,QAAA,EAAI;UAAkB;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BlK,OAAA;YAAA6I,QAAA,EAAG;UAA6E;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpFlK,OAAA;YAAQ4I,SAAS,EAAC,aAAa;YAACQ,OAAO,EAAEpG,UAAW;YAAA6F,QAAA,EAAC;UAErD;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENlK,OAAA;UAAK4I,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxBrI,KAAK,CAACiK,GAAG,CAAEhF,IAAI;YAAA,IAAAiF,WAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,cAAA;YAAA,oBACd7K,OAAA,CAAChB,MAAM,CAAC8J,GAAG;cAETgC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BnC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAErB7I,OAAA;gBAAK4I,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B7I,OAAA;kBAAI4I,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEpD,IAAI,CAAChD;gBAAK;kBAAAsH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC3C,EAAAQ,WAAA,GAAAjF,IAAI,CAAChD,KAAK,cAAAiI,WAAA,uBAAVA,WAAA,CAAYnE,WAAW,CAAC,CAAC,CAACqB,QAAQ,CAAC,UAAU,CAAC,kBAC7C5H,OAAA;kBAAM4I,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAU;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENlK,OAAA;gBAAK4I,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B7I,OAAA;kBAAK4I,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5B7I,OAAA;oBAAK4I,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5B7I,OAAA;sBAAM4I,SAAS,EAAC,UAAU;sBAAAC,QAAA,EAAC;oBAAG;sBAAAkB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,GAAAS,qBAAA,GACpClF,IAAI,CAAC7C,eAAe,cAAA+H,qBAAA,uBAApBA,qBAAA,CAAsBM,cAAc,CAAC,CAAC;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,EACLzE,IAAI,CAAC9C,WAAW,GAAG8C,IAAI,CAAC7C,eAAe,iBACtC5C,OAAA,CAAAE,SAAA;oBAAA2I,QAAA,gBACE7I,OAAA;sBAAM4I,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,IAAA+B,iBAAA,GAAEnF,IAAI,CAAC9C,WAAW,cAAAiI,iBAAA,uBAAhBA,iBAAA,CAAkBK,cAAc,CAAC,CAAC,EAAC,MAAI;oBAAA;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChFlK,OAAA;sBAAM4I,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,GAC7BJ,IAAI,CAACyC,KAAK,CAAE,CAACzF,IAAI,CAAC9C,WAAW,GAAG8C,IAAI,CAAC7C,eAAe,IAAI6C,IAAI,CAAC9C,WAAW,GAAI,GAAG,CAAC,EAAC,OACpF;oBAAA;sBAAAoH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,eACP,CACH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNlK,OAAA;kBAAK4I,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5B7I,OAAA;oBAAM4I,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAEpD,IAAI,CAAC3C;kBAAQ;oBAAAiH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,UAAM,EAACzE,IAAI,CAAC3C,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,SACjG;gBAAA;kBAAAiH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlK,OAAA;gBAAK4I,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAAgC,cAAA,GAC3BpF,IAAI,CAAC/C,QAAQ,cAAAmI,cAAA,uBAAbA,cAAA,CAAeM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACV,GAAG,CAAC,CAACW,OAAO,EAAEC,KAAK,kBAC7CrL,OAAA;kBAAiB4I,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACvC7I,OAAA,CAACZ,aAAa;oBAACwJ,SAAS,EAAC;kBAAc;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1ClK,OAAA;oBAAA6I,QAAA,EAAOuC;kBAAO;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFdmB,KAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENlK,OAAA;gBACE4I,SAAS,EAAC,iBAAiB;gBAC3BQ,OAAO,EAAEA,CAAA,KAAMhE,gBAAgB,CAACK,IAAI,CAAE;gBACtC6F,QAAQ,EAAE1K,cAAc,KAAK6E,IAAI,CAACjD,GAAI;gBACtCY,KAAK,EAAE;kBACLoG,UAAU,EAAE,2CAA2C;kBACvDC,KAAK,EAAE,OAAO;kBACdC,MAAM,EAAE,MAAM;kBACdE,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,aAAa;kBACtBtC,QAAQ,EAAE,MAAM;kBAChBkE,UAAU,EAAE,KAAK;kBACjB1B,MAAM,EAAEjJ,cAAc,KAAK6E,IAAI,CAACjD,GAAG,GAAG,aAAa,GAAG,SAAS;kBAC/D2G,UAAU,EAAE,eAAe;kBAC3BkB,OAAO,EAAE,MAAM;kBACfmB,UAAU,EAAE,QAAQ;kBACpBjB,cAAc,EAAE,QAAQ;kBACxBD,GAAG,EAAE,QAAQ;kBACbmB,KAAK,EAAE,MAAM;kBACbzC,OAAO,EAAEpI,cAAc,KAAK6E,IAAI,CAACjD,GAAG,GAAG,GAAG,GAAG;gBAC/C,CAAE;gBACFkJ,YAAY,EAAGC,CAAC,IAAK;kBACnB,IAAI/K,cAAc,KAAK6E,IAAI,CAACjD,GAAG,EAAE;oBAC/BmJ,CAAC,CAACC,MAAM,CAACxI,KAAK,CAACoG,UAAU,GAAG,2CAA2C;oBACvEmC,CAAC,CAACC,MAAM,CAACxI,KAAK,CAACyI,SAAS,GAAG,kBAAkB;oBAC7CF,CAAC,CAACC,MAAM,CAACxI,KAAK,CAAC0I,SAAS,GAAG,oCAAoC;kBACjE;gBACF,CAAE;gBACFC,YAAY,EAAGJ,CAAC,IAAK;kBACnB,IAAI/K,cAAc,KAAK6E,IAAI,CAACjD,GAAG,EAAE;oBAC/BmJ,CAAC,CAACC,MAAM,CAACxI,KAAK,CAACoG,UAAU,GAAG,2CAA2C;oBACvEmC,CAAC,CAACC,MAAM,CAACxI,KAAK,CAACyI,SAAS,GAAG,eAAe;oBAC1CF,CAAC,CAACC,MAAM,CAACxI,KAAK,CAAC0I,SAAS,GAAG,oCAAoC;kBACjE;gBACF,CAAE;gBAAAjD,QAAA,gBAEF7I,OAAA,CAACV,YAAY;kBAACsJ,SAAS,EAAC;gBAAU;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACpCtJ,cAAc,KAAK6E,IAAI,CAACjD,GAAG,GACxB,eAAe,GACfuF,kBAAkB,KAAK,QAAQ,GAC7B,kBAAkB,GAClBA,kBAAkB,KAAK,SAAS,GAC9B,gBAAgB,GAChB,cAAc;cAAA;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CAAC;YAAA,GAtFJzE,IAAI,CAACjD,GAAG;cAAAuH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuFH,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,EAGZ,CAAC,CAAChI,IAAI,CAACwD,WAAW,IAAI,CAAC,gBAAgB,CAACC,IAAI,CAACzD,IAAI,CAACwD,WAAW,CAAC,kBAC7D1F,OAAA,CAAChB,MAAM,CAAC8J,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAErG,QAAQ,EAAE,GAAG;UAAEqH,KAAK,EAAE;QAAI,CAAE;QAC1CvB,SAAS,EAAC,eAAe;QAAAC,QAAA,eAEzB7I,OAAA;UAAK4I,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B7I,OAAA,CAACX,aAAa;YAACuJ,SAAS,EAAC;UAAc;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1ClK,OAAA;YAAA6I,QAAA,gBACE7I,OAAA;cAAA6I,QAAA,EAAI;YAAqB;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9BlK,OAAA;cAAA6I,QAAA,EAAG;YAAuE;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9ElK,OAAA;cACE4I,SAAS,EAAC,kBAAkB;cAC5BQ,OAAO,EAAEA,CAAA,KAAMnF,MAAM,CAACyD,QAAQ,CAACC,IAAI,GAAG,UAAW;cAAAkB,QAAA,EAClD;YAED;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,EAGApJ,mBAAmB,iBAClBd,OAAA;QACE4I,SAAS,EAAC,uBAAuB;QACjCQ,OAAO,EAAGuC,CAAC,IAAKA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACK,aAAa,IAAI3G,0BAA0B,CAAC,CAAE;QAAAwD,QAAA,eAE7E7I,OAAA;UAAK4I,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBAEjD7I,OAAA;YACE4I,SAAS,EAAC,iBAAiB;YAC3BQ,OAAO,EAAE/D,0BAA2B;YACpC,cAAW,aAAa;YAAAwD,QAAA,eAExB7I,OAAA;cAAKyL,KAAK,EAAC,IAAI;cAACQ,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAAAtD,QAAA,eACzD7I,OAAA;gBAAMoM,CAAC,EAAC,sBAAsB;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC;cAAO;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAGTlK,OAAA;YAAK4I,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtC7I,OAAA;cAAK4I,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B7I,OAAA;gBAAK4I,SAAS,EAAC;cAAS;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/BlK,OAAA;gBAAK4I,SAAS,EAAC,cAAc;gBAAC6C,KAAK,EAAC,IAAI;gBAACQ,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAtD,QAAA,gBAClF7I,OAAA;kBAAMoM,CAAC,EAAC,wOAAwO;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAK;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC1RlK,OAAA;kBAAMoM,CAAC,EAAC,UAAU;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,KAAK;kBAACC,aAAa,EAAC;gBAAO;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAClFlK,OAAA;kBAAMoM,CAAC,EAAC,aAAa;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,KAAK;kBAACC,aAAa,EAAC;gBAAO;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACrFlK,OAAA;kBAAMoM,CAAC,EAAC,aAAa;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,KAAK;kBAACC,aAAa,EAAC;gBAAO;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlK,OAAA;cAAA6I,QAAA,EAAI;YAAkB;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3BlK,OAAA;cAAA6I,QAAA,EAAG;YAA8B;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eAGNlK,OAAA;YAAK4I,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAE5B7I,OAAA;cAAK4I,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B7I,OAAA;gBAAK4I,SAAS,EAAC;cAA6B;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnDlK,OAAA;gBAAG4I,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEvH;cAAa;gBAAAyI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eAGNlK,OAAA;cAAK4I,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B7I,OAAA;gBAAA6I,QAAA,EAAKzH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqB;cAAK;gBAAAsH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9BlK,OAAA;gBAAK4I,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B7I,OAAA;kBAAK4I,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB7I,OAAA;oBAAA6I,QAAA,EAAM;kBAAM;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnBlK,OAAA;oBAAA6I,QAAA,GAASzH,YAAY,aAAZA,YAAY,wBAAAd,qBAAA,GAAZc,YAAY,CAAEwB,eAAe,cAAAtC,qBAAA,uBAA7BA,qBAAA,CAA+B2K,cAAc,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,eACNlK,OAAA;kBAAK4I,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB7I,OAAA;oBAAA6I,QAAA,EAAM;kBAAQ;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrBlK,OAAA;oBAAA6I,QAAA,GAASzH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0B,QAAQ,EAAC,QAAM,EAAC,CAAA1B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0B,QAAQ,IAAG,CAAC,GAAG,GAAG,GAAG,EAAE;kBAAA;oBAAAiH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlK,OAAA;cAAK4I,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B7I,OAAA;gBAAK4I,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjC7I,OAAA;kBAAKyL,KAAK,EAAC,IAAI;kBAACQ,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAtD,QAAA,eACzD7I,OAAA;oBAAMoM,CAAC,EAAC,8kBAA8kB;oBAACD,IAAI,EAAC;kBAAc;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzmB,CAAC,eACNlK,OAAA;kBAAA6I,QAAA,EAAM;gBAAgB;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eACNlK,OAAA;gBAAK4I,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE3G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwD;cAAW;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvDlK,OAAA;gBAAK4I,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC7I,OAAA;kBAAK4I,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAkD;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9ElK,OAAA;kBAAK4I,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAA0C;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtElK,OAAA;kBAAK4I,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAwC;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLpI,YAAY,iBACX9B,OAAA;cAAK4I,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B7I,OAAA;gBAAA6I,QAAA,EAAG;cAA4B;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnClK,OAAA;gBAAQ4I,SAAS,EAAC,eAAe;gBAACQ,OAAO,EAAE9D,cAAe;gBAAAuD,QAAA,gBACxD7I,OAAA;kBAAKyL,KAAK,EAAC,IAAI;kBAACQ,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAtD,QAAA,gBACzD7I,OAAA;oBAAMoM,CAAC,EAAC,uBAAuB;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,aAAa,EAAC;kBAAO;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC7FlK,OAAA;oBAAMoM,CAAC,EAAC,iBAAiB;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC;kBAAO;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3G,CAAC,aAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAKAhJ,gBAAgB,iBACflB,OAAA;QAAK4I,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpC7I,OAAA;UAAK4I,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAE9C7I,OAAA;YACE4I,SAAS,EAAC,iBAAiB;YAC3BQ,OAAO,EAAEA,CAAA,KAAM;cACbnH,wBAAwB,CAAC,IAAI,CAAC;cAC9Bd,mBAAmB,CAAC,KAAK,CAAC;YAC5B,CAAE;YACF,cAAW,aAAa;YAAA0H,QAAA,eAExB7I,OAAA;cAAKyL,KAAK,EAAC,IAAI;cAACQ,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAAAtD,QAAA,eACzD7I,OAAA;gBAAMoM,CAAC,EAAC,sBAAsB;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC;cAAO;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAGTlK,OAAA;YAAK4I,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC7I,OAAA;cAAK4I,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B7I,OAAA;gBAAKyL,KAAK,EAAC,IAAI;gBAACQ,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAAtD,QAAA,gBACzD7I,OAAA;kBAAMoM,CAAC,EAAC,mHAAmH;kBAACD,IAAI,EAAC,SAAS;kBAACM,WAAW,EAAC;gBAAK;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC9JlK,OAAA;kBAAMoM,CAAC,EAAC,sBAAsB;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC;gBAAO;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC9GlK,OAAA;kBAAMoM,CAAC,EAAC,mHAAmH;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC;gBAAG;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3J;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlK,OAAA;cAAA6I,QAAA,EAAI;YAAmB;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BlK,OAAA;cAAA6I,QAAA,GAAG,aAAW,EAACzH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqB,KAAK,EAAC,GAAC;YAAA;cAAAsH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eAENlK,OAAA;YAAK4I,SAAS,EAAC,eAAe;YAAAC,QAAA,GAE3B7G,qBAAqB,iBACpBhC,OAAA;cAAK4I,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B7I,OAAA;gBAAK4I,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAC7B7I,OAAA;kBAAKyL,KAAK,EAAC,IAAI;kBAACQ,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAtD,QAAA,gBACzD7I,OAAA;oBAAMoM,CAAC,EAAC,0HAA0H;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,aAAa,EAAC;kBAAO;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChMlK,OAAA;oBAAQ0M,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,GAAG;oBAACP,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC;kBAAG;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlK,OAAA;gBAAA6I,QAAA,GAAG,wBAAsB,EAAC7G,qBAAqB,EAAC,aAAW;cAAA;gBAAA+H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CACN,eAGDlK,OAAA;cAAK4I,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC7I,OAAA;gBAAA6I,QAAA,EAAI;cAAsB;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BlK,OAAA;gBAAK4I,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B7I,OAAA;kBAAK4I,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB7I,OAAA;oBAAA6I,QAAA,EAAM;kBAAI;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjBlK,OAAA;oBAAA6I,QAAA,EAASzH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqB;kBAAK;oBAAAsH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACNlK,OAAA;kBAAK4I,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB7I,OAAA;oBAAA6I,QAAA,EAAM;kBAAQ;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrBlK,OAAA;oBAAA6I,QAAA,GAASzH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0B,QAAQ,EAAC,QAAM,EAAC,CAAA1B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0B,QAAQ,IAAG,CAAC,GAAG,GAAG,GAAG,EAAE;kBAAA;oBAAAiH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC,eACNlK,OAAA;kBAAK4I,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB7I,OAAA;oBAAA6I,QAAA,EAAM;kBAAW;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxBlK,OAAA;oBAAA6I,QAAA,GAASzH,YAAY,aAAZA,YAAY,wBAAAb,sBAAA,GAAZa,YAAY,CAAEwB,eAAe,cAAArC,sBAAA,uBAA7BA,sBAAA,CAA+B0K,cAAc,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,eACNlK,OAAA;kBAAK4I,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC7I,OAAA;oBAAA6I,QAAA,EAAM;kBAAM;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnBlK,OAAA;oBAAK4I,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3B7I,OAAA;sBAAKyL,KAAK,EAAC,IAAI;sBAACQ,MAAM,EAAC,IAAI;sBAACC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAAAtD,QAAA,gBACzD7I,OAAA;wBAAMoM,CAAC,EAAC,mBAAmB;wBAACC,MAAM,EAAC,cAAc;wBAACC,WAAW,EAAC,GAAG;wBAACC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC;sBAAO;wBAAAzC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eAChHlK,OAAA;wBAAQ0M,EAAE,EAAC,IAAI;wBAACC,EAAE,EAAC,IAAI;wBAACC,CAAC,EAAC,GAAG;wBAACP,MAAM,EAAC,cAAc;wBAACC,WAAW,EAAC;sBAAG;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClE,CAAC,UAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlK,OAAA;cAAK4I,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B7I,OAAA;gBAAA6I,QAAA,EAAI;cAA4B;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrClK,OAAA;gBAAK4I,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B7I,OAAA;kBAAK4I,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7I,OAAA;oBAAKyL,KAAK,EAAC,IAAI;oBAACQ,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAtD,QAAA,gBACzD7I,OAAA;sBAAMoM,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHlK,OAAA;sBAAQ0M,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACP,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNlK,OAAA;oBAAA6I,QAAA,EAAM;kBAAiB;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACNlK,OAAA;kBAAK4I,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7I,OAAA;oBAAKyL,KAAK,EAAC,IAAI;oBAACQ,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAtD,QAAA,gBACzD7I,OAAA;sBAAMoM,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHlK,OAAA;sBAAQ0M,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACP,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNlK,OAAA;oBAAA6I,QAAA,EAAM;kBAAY;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACNlK,OAAA;kBAAK4I,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7I,OAAA;oBAAKyL,KAAK,EAAC,IAAI;oBAACQ,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAtD,QAAA,gBACzD7I,OAAA;sBAAMoM,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHlK,OAAA;sBAAQ0M,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACP,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNlK,OAAA;oBAAA6I,QAAA,EAAM;kBAAe;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACNlK,OAAA;kBAAK4I,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7I,OAAA;oBAAKyL,KAAK,EAAC,IAAI;oBAACQ,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAtD,QAAA,gBACzD7I,OAAA;sBAAMoM,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHlK,OAAA;sBAAQ0M,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACP,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNlK,OAAA;oBAAA6I,QAAA,EAAM;kBAAiB;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACNlK,OAAA;kBAAK4I,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7I,OAAA;oBAAKyL,KAAK,EAAC,IAAI;oBAACQ,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAtD,QAAA,gBACzD7I,OAAA;sBAAMoM,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHlK,OAAA;sBAAQ0M,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACP,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNlK,OAAA;oBAAA6I,QAAA,EAAM;kBAAe;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACNlK,OAAA;kBAAK4I,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7I,OAAA;oBAAKyL,KAAK,EAAC,IAAI;oBAACQ,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAAtD,QAAA,gBACzD7I,OAAA;sBAAMoM,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHlK,OAAA;sBAAQ0M,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACP,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNlK,OAAA;oBAAA6I,QAAA,EAAM;kBAAY;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlK,OAAA;cAAK4I,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B7I,OAAA;gBACE4I,SAAS,EAAC,aAAa;gBACvBQ,OAAO,EAAEA,CAAA,KAAM;kBACbnH,wBAAwB,CAAC,IAAI,CAAC;kBAC9Bd,mBAAmB,CAAC,KAAK,CAAC;kBAC1B8C,MAAM,CAACyD,QAAQ,CAACC,IAAI,GAAG,WAAW;gBACpC,CAAE;gBAAAkB,QAAA,gBAEF7I,OAAA;kBAAKyL,KAAK,EAAC,IAAI;kBAACQ,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAAtD,QAAA,gBACzD7I,OAAA;oBAAMoM,CAAC,EAAC,8KAA8K;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC;kBAAG;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC9NlK,OAAA;oBAAU6M,MAAM,EAAC,uBAAuB;oBAACR,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC;kBAAG;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC,oBACU,EAAClI,qBAAqB,GAAI,IAAGA,qBAAsB,IAAG,GAAG,EAAE;cAAA;gBAAA+H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACTlK,OAAA;gBACE4I,SAAS,EAAC,eAAe;gBACzBQ,OAAO,EAAEA,CAAA,KAAM;kBACbnH,wBAAwB,CAAC,IAAI,CAAC;kBAC9Bd,mBAAmB,CAAC,KAAK,CAAC;gBAC5B,CAAE;gBAAA0H,QAAA,EACH;cAED;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAKDlK,OAAA,CAACH,uBAAuB;QACtBiN,OAAO,EAAEtL,sBAAuB;QAChCuL,OAAO,EAAEA,CAAA,KAAMtL,yBAAyB,CAAC,KAAK,CAAE;QAChDuL,WAAW,EAAExM,KAAK,CAACyM,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1K,GAAG,MAAKJ,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEgI,UAAU,EAAC,KAAIhI,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEqD,IAAI,CAAC;QAC/FpD,YAAY,EAAED,gBAAiB;QAC/BF,IAAI,EAAEA;MAAK;QAAA6H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAGFlK,OAAA,CAACF,wBAAwB;QACvBgN,OAAO,EAAEpL,gBAAiB;QAC1BqL,OAAO,EAAEA,CAAA,KAAMpL,mBAAmB,CAAC,KAAK,CAAE;QAC1CwL,OAAO,EAAEhI,uBAAwB;QACjC9C,YAAY,EAAED,gBAAiB;QAC/BF,IAAI,EAAEA,IAAK;QACX1B,KAAK,EAAEA;MAAM;QAAAuJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9J,EAAA,CA1iCID,YAAY;EAAA,QAkBCrB,WAAW,EACCA,WAAW,EACvBC,WAAW;AAAA;AAAAqO,EAAA,GApBxBjN,YAAY;AA4iClB,eAAeA,YAAY;AAAC,IAAAiN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}