const mongoose = require('mongoose');

// Simple subscription check
console.log('🔄 Starting subscription analysis...');

// Connect to MongoDB
const mongoUrl = 'mongodb+srv://brainwaveuser:<EMAIL>/brainwave?retryWrites=true&w=majority';

mongoose.connect(mongoUrl)
  .then(() => {
    console.log('✅ Connected to MongoDB');
    return analyzeSubscriptions();
  })
  .catch((error) => {
    console.error('❌ Connection error:', error.message);
    process.exit(1);
  });

async function analyzeSubscriptions() {
  try {
    console.log('\n🔍 Analyzing subscription data...');
    
    const db = mongoose.connection.db;
    
    // Get current date
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayString = today.toISOString().split('T')[0];
    
    console.log(`📅 Today's date: ${todayString}`);
    
    // Check collections
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);
    
    console.log('\n📋 Available collections:', collectionNames.join(', '));
    
    // Check users
    const usersCollection = db.collection('users');
    const totalUsers = await usersCollection.countDocuments();
    console.log(`\n👥 Total users: ${totalUsers}`);
    
    // Check subscriptions
    const subscriptionsCollection = db.collection('subscriptions');
    const totalSubscriptions = await subscriptionsCollection.countDocuments();
    console.log(`📋 Total subscriptions: ${totalSubscriptions}`);
    
    if (totalSubscriptions === 0) {
      console.log('\n⚠️  No subscription records found in database');
      
      // Check if users have subscription data in user model
      const usersWithSubData = await usersCollection.find({
        $or: [
          { subscriptionStatus: { $exists: true, $ne: null } },
          { subscriptionEndDate: { $exists: true, $ne: null } },
          { subscriptionPlan: { $exists: true, $ne: null } }
        ]
      }).toArray();
      
      console.log(`\n👤 Users with subscription data in user model: ${usersWithSubData.length}`);
      
      if (usersWithSubData.length > 0) {
        console.log('\n📊 USERS WITH SUBSCRIPTION DATA:');
        console.log('-'.repeat(50));
        
        usersWithSubData.forEach((user, index) => {
          const endDate = user.subscriptionEndDate ? new Date(user.subscriptionEndDate) : null;
          const isActive = endDate && endDate >= today;
          const daysRemaining = endDate ? Math.ceil((endDate - today) / (1000 * 60 * 60 * 24)) : null;
          
          console.log(`${index + 1}. ${user.firstName || ''} ${user.lastName || ''} (@${user.username || 'N/A'})`);
          console.log(`   📧 Email: ${user.email || 'Not provided'}`);
          console.log(`   📱 Phone: ${user.phoneNumber || 'Not provided'}`);
          console.log(`   📊 Status: ${user.subscriptionStatus || 'Not set'}`);
          console.log(`   📅 End Date: ${user.subscriptionEndDate || 'Not set'}`);
          console.log(`   📋 Plan: ${user.subscriptionPlan || 'Not set'}`);
          console.log(`   💳 Payment Required: ${user.paymentRequired}`);
          console.log(`   ✅ Currently Active: ${isActive ? 'YES' : 'NO'}`);
          if (daysRemaining !== null) {
            console.log(`   ⏰ Days Remaining: ${daysRemaining} days`);
          }
          console.log('');
        });
        
        // Count active users
        const activeUsers = usersWithSubData.filter(user => {
          const endDate = user.subscriptionEndDate ? new Date(user.subscriptionEndDate) : null;
          return endDate && endDate >= today;
        });
        
        console.log('\n📊 SUMMARY:');
        console.log('='.repeat(50));
        console.log(`Total Users: ${totalUsers}`);
        console.log(`Users with Subscription Data: ${usersWithSubData.length}`);
        console.log(`Currently Active Subscriptions: ${activeUsers.length}`);
        console.log(`Expired/Inactive Subscriptions: ${usersWithSubData.length - activeUsers.length}`);
        
        if (activeUsers.length > 0) {
          console.log('\n✅ CURRENTLY ACTIVE USERS:');
          console.log('-'.repeat(40));
          activeUsers.forEach((user, index) => {
            const endDate = new Date(user.subscriptionEndDate);
            const daysRemaining = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));
            console.log(`${index + 1}. ${user.firstName || ''} ${user.lastName || ''} - Expires: ${user.subscriptionEndDate} (${daysRemaining} days)`);
          });
        } else {
          console.log('\n❌ NO CURRENTLY ACTIVE SUBSCRIPTIONS FOUND');
        }
      }
      
      await mongoose.connection.close();
      return;
    }
    
    // If we have subscription records, analyze them
    const subscriptions = await subscriptionsCollection.aggregate([
      {
        $lookup: {
          from: 'users',
          localField: 'user',
          foreignField: '_id',
          as: 'userData'
        }
      },
      {
        $lookup: {
          from: 'plans',
          localField: 'activePlan',
          foreignField: '_id',
          as: 'planData'
        }
      }
    ]).toArray();
    
    console.log(`\n📊 Analyzing ${subscriptions.length} subscription records...`);
    
    let activeCount = 0;
    let expiredCount = 0;
    let pendingCount = 0;
    
    const activeUsers = [];
    const expiredUsers = [];
    
    subscriptions.forEach((sub) => {
      const user = sub.userData[0];
      const plan = sub.planData[0];
      
      if (!user) return;
      
      const endDate = sub.endDate ? new Date(sub.endDate) : null;
      const isDateValid = endDate && endDate >= today;
      const isStatusActive = sub.status === 'active';
      const isPaymentPaid = sub.paymentStatus === 'paid';
      
      const userInfo = {
        name: `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username || 'Unknown',
        username: user.username || 'N/A',
        email: user.email || 'Not provided',
        phone: user.phoneNumber || 'Not provided',
        planTitle: plan?.title || 'Unknown Plan',
        endDate: sub.endDate || 'Not set',
        status: sub.status,
        paymentStatus: sub.paymentStatus,
        daysRemaining: endDate ? Math.ceil((endDate - today) / (1000 * 60 * 60 * 24)) : null
      };
      
      if (isStatusActive && isPaymentPaid && isDateValid) {
        activeCount++;
        activeUsers.push(userInfo);
      } else if (sub.status === 'expired' || (endDate && endDate < today)) {
        expiredCount++;
        expiredUsers.push(userInfo);
      } else {
        pendingCount++;
      }
    });
    
    // Display results
    console.log('\n🎯 SUBSCRIPTION ANALYSIS RESULTS');
    console.log('='.repeat(60));
    console.log(`✅ Active subscriptions: ${activeCount}`);
    console.log(`❌ Expired subscriptions: ${expiredCount}`);
    console.log(`⏳ Pending/Other subscriptions: ${pendingCount}`);
    
    if (activeUsers.length > 0) {
      console.log('\n✅ USERS WITH ACTIVE SUBSCRIPTIONS:');
      console.log('-'.repeat(50));
      activeUsers.forEach((user, index) => {
        console.log(`${index + 1}. ${user.name} (@${user.username})`);
        console.log(`   📧 Email: ${user.email}`);
        console.log(`   📱 Phone: ${user.phone}`);
        console.log(`   📋 Plan: ${user.planTitle}`);
        console.log(`   📅 End Date: ${user.endDate}`);
        console.log(`   ⏰ Days Remaining: ${user.daysRemaining} days`);
        console.log(`   💳 Payment: ${user.paymentStatus} | Status: ${user.status}`);
        console.log('');
      });
      
      console.log('\n📋 QUICK ACTIVE USERS LIST:');
      console.log('-'.repeat(40));
      activeUsers.forEach((user, index) => {
        console.log(`${index + 1}. ${user.name} - Expires: ${user.endDate} (${user.daysRemaining} days)`);
      });
    } else {
      console.log('\n❌ NO ACTIVE SUBSCRIPTIONS FOUND');
    }
    
    if (expiredUsers.length > 0) {
      console.log('\n❌ RECENTLY EXPIRED SUBSCRIPTIONS (first 5):');
      console.log('-'.repeat(50));
      expiredUsers.slice(0, 5).forEach((user, index) => {
        console.log(`${index + 1}. ${user.name} (@${user.username})`);
        console.log(`   📅 End Date: ${user.endDate}`);
        console.log(`   ⏰ Expired: ${Math.abs(user.daysRemaining || 0)} days ago`);
        console.log(`   📋 Plan: ${user.planTitle}`);
        console.log('');
      });
    }
    
    // Final summary
    const totalAnalyzed = activeCount + expiredCount + pendingCount;
    const activePercentage = totalAnalyzed > 0 ? ((activeCount / totalAnalyzed) * 100).toFixed(1) : 0;
    
    console.log('\n📊 FINAL SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Users: ${totalUsers}`);
    console.log(`Total Subscription Records: ${totalAnalyzed}`);
    console.log(`Currently Active: ${activeCount}`);
    console.log(`Active Rate: ${activePercentage}%`);
    
  } catch (error) {
    console.error('❌ Error during analysis:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('\n✅ Database connection closed');
  }
}
