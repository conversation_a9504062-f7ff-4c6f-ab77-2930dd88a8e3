{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Subscription\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport UpgradeRestrictionModal from '../../../components/UpgradeRestrictionModal/UpgradeRestrictionModal';\nimport SubscriptionExpiredModal from '../../../components/SubscriptionExpiredModal/SubscriptionExpiredModal';\nimport './Subscription.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Subscription = () => {\n  _s();\n  var _subscriptionData$act, _selectedPlan$discoun, _selectedPlan$discoun2;\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(null); // Changed to store plan ID instead of boolean\n  const [showProcessingModal, setShowProcessingModal] = useState(false);\n\n  // Debug: Log showProcessingModal state changes\n  useEffect(() => {\n    console.log('🔍 showProcessingModal state changed to:', showProcessingModal);\n  }, [showProcessingModal]);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [paymentStatus, setPaymentStatus] = useState('');\n  const [showUpgradeRestriction, setShowUpgradeRestriction] = useState(false);\n  const [showExpiredModal, setShowExpiredModal] = useState(false);\n  const [processingStartTime, setProcessingStartTime] = useState(null);\n  const [showTryAgain, setShowTryAgain] = useState(false);\n  const [autoNavigateCountdown, setAutoNavigateCountdown] = useState(null);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const dispatch = useDispatch();\n\n  // Fallback sample plans in case API fails\n  const samplePlans = [{\n    _id: \"basic-plan-sample\",\n    title: \"Basic Membership\",\n    features: [\"2-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\"],\n    actualPrice: 28570,\n    discountedPrice: 20000,\n    discountPercentage: 30,\n    duration: 2,\n    status: true\n  }, {\n    _id: \"premium-plan-sample\",\n    title: \"Premium Plan\",\n    features: [\"3-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\", \"Priority support\"],\n    actualPrice: 45000,\n    discountedPrice: 35000,\n    discountPercentage: 22,\n    duration: 3,\n    status: true\n  }];\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n  }, []);\n\n  // Handle body scroll lock when modals are open\n  useEffect(() => {\n    if (showProcessingModal || showSuccessModal) {\n      // Lock body scroll\n      const scrollY = window.scrollY;\n      document.body.style.overflow = 'hidden';\n      document.body.style.position = 'fixed';\n      document.body.style.width = '100%';\n      document.body.style.top = `-${scrollY}px`;\n    } else {\n      // Unlock body scroll\n      const scrollY = document.body.style.top;\n      document.body.style.overflow = '';\n      document.body.style.position = '';\n      document.body.style.width = '';\n      document.body.style.top = '';\n      if (scrollY) {\n        window.scrollTo(0, parseInt(scrollY || '0') * -1);\n      }\n    }\n\n    // Cleanup on unmount\n    return () => {\n      document.body.style.overflow = '';\n      document.body.style.position = '';\n      document.body.style.width = '';\n      document.body.style.top = '';\n    };\n  }, [showProcessingModal, showSuccessModal]);\n\n  // Check for expired subscription and show modal\n  useEffect(() => {\n    if (subscriptionData && isSubscriptionExpired()) {\n      console.log('🚫 Subscription expired, showing modal');\n      setShowExpiredModal(true);\n    } else {\n      setShowExpiredModal(false);\n    }\n  }, [subscriptionData]);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      console.log('Fetching plans...');\n      const response = await getPlans();\n      console.log('Plans response:', response);\n      if (response.success && response.data && response.data.length > 0) {\n        setPlans(response.data);\n        console.log('Plans loaded successfully from API:', response.data);\n      } else if (Array.isArray(response) && response.length > 0) {\n        // Handle case where response is directly an array of plans\n        setPlans(response);\n        console.log('Plans loaded as array from API:', response);\n      } else {\n        console.warn('No plans from API, using sample plans');\n        setPlans(samplePlans);\n        message.info('Showing sample plans. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('Error loading plans from API:', error);\n      console.log('Using fallback sample plans');\n      setPlans(samplePlans);\n      message.warning('Using sample plans. Please check your connection and try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n      console.log('Current subscription:', response);\n    } catch (error) {\n      console.log('No active subscription found');\n    }\n  };\n\n  // Check if subscription is expired\n  const isSubscriptionExpired = () => {\n    if (!subscriptionData) return true;\n\n    // If no subscription data, consider expired\n    if (!subscriptionData.endDate) return true;\n\n    // If payment status is not paid, consider expired\n    if (subscriptionData.paymentStatus !== 'paid') return true;\n\n    // If status is not active, consider expired\n    if (subscriptionData.status !== 'active') return true;\n\n    // Check if end date has passed\n    const endDate = new Date(subscriptionData.endDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n    endDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    return endDate < today;\n  };\n\n  // Handle subscription renewal from expired modal\n  const handleRenewSubscription = async selectedPlan => {\n    setShowExpiredModal(false);\n    await handlePlanSelect(selectedPlan);\n  };\n\n  // Handle closing payment processing modal\n  const handleCloseProcessingModal = () => {\n    setShowProcessingModal(false);\n    setPaymentLoading(null); // Reset to null instead of false\n    setShowTryAgain(false);\n    setProcessingStartTime(null);\n    setPaymentStatus('');\n    message.info('Payment process cancelled. You can try again anytime.');\n  };\n\n  // Handle try again functionality\n  const handleTryAgain = () => {\n    if (selectedPlan) {\n      setShowTryAgain(false);\n      setProcessingStartTime(null);\n      handlePlanSelect(selectedPlan);\n    }\n  };\n\n  // Test success modal (for debugging)\n  const testSuccessModal = () => {\n    console.log('🧪 Testing success modal...');\n    setShowProcessingModal(false);\n    setShowSuccessModal(true);\n    setPaymentLoading(null);\n  };\n\n  // Test processing modal (for debugging)\n  const testProcessingModal = () => {\n    console.log('🧪 Testing processing modal...');\n    setShowProcessingModal(true);\n    setPaymentStatus('Testing processing modal...');\n    setSelectedPlan(plans[0] || {\n      title: 'Test Plan',\n      discountedPrice: 5000,\n      duration: 1\n    });\n  };\n  const handlePlanSelect = async plan => {\n    // Check if user already has an active subscription\n    if (subscriptionData && subscriptionData.status === 'active' && subscriptionData.paymentStatus === 'paid') {\n      console.log('🚫 User already has active subscription:', subscriptionData);\n      setShowUpgradeRestriction(true);\n      return;\n    }\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n    try {\n      var _user$name;\n      console.log('🚀 Starting payment for plan:', plan.title);\n      console.log('🔧 IMMEDIATELY showing processing modal...');\n\n      // IMMEDIATELY show processing modal when user chooses plan\n      setSelectedPlan(plan);\n      setPaymentLoading(plan._id);\n      setShowProcessingModal(true);\n      setShowTryAgain(false);\n      setProcessingStartTime(Date.now());\n      setPaymentStatus('🚀 Preparing your payment request...');\n      console.log('✅ Processing modal IMMEDIATELY displayed');\n\n      // Small delay to ensure modal is visible before API call\n      await new Promise(resolve => setTimeout(resolve, 200));\n\n      // Set timer for try again button (10 seconds)\n      const tryAgainTimer = setTimeout(() => {\n        setShowTryAgain(true);\n      }, 10000);\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      setPaymentStatus('📤 Sending payment request to ZenoPay...');\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        var _response$data;\n        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');\n        console.log('💳 Payment response:', response);\n        console.log('🆔 Order ID:', response.order_id);\n\n        // Show confirmation message to user\n        message.success({\n          content: `💳 Payment initiated! 📱 Check your phone (${user.phoneNumber}) for SMS confirmation from ZenoPay.`,\n          duration: 8,\n          style: {\n            marginTop: '20vh'\n          }\n        });\n\n        // Start checking payment status immediately\n        const orderIdToCheck = response.order_id || ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.order_id) || 'demo_order';\n        console.log('🔍 Starting payment confirmation check for order:', orderIdToCheck);\n        checkPaymentConfirmation(orderIdToCheck);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('❌ Payment failed:', error);\n      setShowProcessingModal(false);\n      message.error('Payment failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const checkPaymentConfirmation = async orderId => {\n    console.log('🚀 Starting payment confirmation check for order:', orderId);\n    let isPolling = true;\n    let handleVisibilityChange;\n    try {\n      setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n\n      // Poll payment status every 2 seconds for optimal responsiveness\n      let attempts = 0;\n      const maxAttempts = 150; // 150 attempts * 2 seconds = 5 minutes\n\n      const pollPaymentStatus = async () => {\n        attempts++;\n        console.log(`🔍 Payment status check attempt ${attempts}/${maxAttempts} for order:`, orderId);\n        try {\n          const statusResponse = await checkPaymentStatus({\n            orderId\n          });\n          console.log('📊 Payment status response:', statusResponse);\n          console.log('🔍 Checking payment conditions:');\n          console.log('  - Live payment:', (statusResponse === null || statusResponse === void 0 ? void 0 : statusResponse.paymentStatus) === 'paid' && (statusResponse === null || statusResponse === void 0 ? void 0 : statusResponse.status) === 'active');\n          console.log('  - Demo payment:', (statusResponse === null || statusResponse === void 0 ? void 0 : statusResponse.status) === 'completed' && (statusResponse === null || statusResponse === void 0 ? void 0 : statusResponse.success) === true);\n          if (statusResponse && (statusResponse.paymentStatus === 'paid' && statusResponse.status === 'active' || statusResponse.status === 'completed' && statusResponse.success === true)) {\n            // Payment confirmed immediately!\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('🎉 Payment confirmed! Activating your subscription...');\n            console.log('✅ Payment confirmed, preparing to show success modal...');\n\n            // Show success INSTANTLY - no delay\n            console.log('🔄 Setting modal states - Processing: false, Success: true');\n            setShowProcessingModal(false);\n            setShowSuccessModal(true);\n            setPaymentLoading(null);\n            console.log('✅ Success modal state set to true');\n\n            // Refresh subscription data\n            checkCurrentSubscription();\n\n            // Show immediate success message\n            message.success({\n              content: '🎉 Payment confirmed! All features are now unlocked!',\n              duration: 5,\n              style: {\n                marginTop: '20vh',\n                fontSize: '16px'\n              }\n            });\n\n            // Start countdown for auto-navigation to hub\n            setAutoNavigateCountdown(5);\n            const countdownInterval = setInterval(() => {\n              setAutoNavigateCountdown(prev => {\n                if (prev <= 1) {\n                  clearInterval(countdownInterval);\n                  console.log('🏠 Auto-navigating to hub after successful payment...');\n                  setShowSuccessModal(false);\n                  window.location.href = '/user/hub';\n                  return null;\n                }\n                return prev - 1;\n              });\n            }, 1000);\n          } else if (attempts >= maxAttempts) {\n            // Timeout - but don't fail completely\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('⏰ Still waiting for confirmation. Please complete the payment on your phone.');\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setPaymentLoading(null); // Reset to null\n              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status or try again.');\n            }, 2000);\n          } else {\n            // Continue polling - NO TIME INDICATION, just encouraging message\n            setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n            setTimeout(pollPaymentStatus, 2000); // Check every 2 seconds for better performance\n          }\n        } catch (error) {\n          console.error('Payment status check error:', error);\n\n          // Handle specific error types\n          if (error.message && error.message.includes('404')) {\n            console.error('❌ Payment status endpoint not found (404)');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Payment verification service is temporarily unavailable. Please contact support or check your subscription status manually.');\n            return;\n          }\n          if (error.message && error.message.includes('401')) {\n            console.error('❌ Authentication required for payment status check');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Please login again to check payment status.');\n            return;\n          }\n          if (attempts >= maxAttempts) {\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Unable to confirm payment status. Please check your subscription status manually.');\n          } else {\n            // Continue polling even if there's an error (unless it's a critical error)\n            setTimeout(pollPaymentStatus, 1000);\n          }\n        }\n      };\n\n      // Add visibility change listener to check immediately when user returns to tab\n      handleVisibilityChange = () => {\n        if (!document.hidden && isPolling) {\n          console.log('User returned to tab, checking payment status immediately...');\n          setPaymentStatus('🔍 Checking payment status...');\n          // Trigger immediate check\n          setTimeout(() => pollPaymentStatus(), 100);\n        }\n      };\n      document.addEventListener('visibilitychange', handleVisibilityChange);\n\n      // Start polling immediately (no delay) - check right away\n      setTimeout(pollPaymentStatus, 500); // Start checking after 0.5 seconds\n    } catch (error) {\n      isPolling = false; // Stop polling\n      if (handleVisibilityChange) {\n        document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n      }\n\n      setShowProcessingModal(false);\n      message.error('Payment confirmation failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    if ((user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'expired' || subscriptionData && subscriptionData.status === 'expired') {\n      return 'expired';\n    }\n    return 'none';\n  };\n  const formatDate = dateString => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const getDaysRemaining = () => {\n    if (!(subscriptionData !== null && subscriptionData !== void 0 && subscriptionData.endDate)) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n  const subscriptionStatus = getSubscriptionStatus();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"subscription-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subscription-container\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"subscription-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            console.log('🧪 Testing success modal...');\n            setSelectedPlan(plans[0] || {\n              title: 'Test Plan',\n              duration: 1,\n              discountedPrice: 13000\n            });\n            setShowSuccessModal(true);\n          },\n          style: {\n            position: 'fixed',\n            top: '10px',\n            right: '10px',\n            background: '#52c41a',\n            color: 'white',\n            border: 'none',\n            padding: '8px 16px',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            zIndex: 9999\n          },\n          children: \"\\uD83E\\uDDEA Test Success Modal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"page-title\",\n          children: [/*#__PURE__*/_jsxDEV(FaCrown, {\n            className: \"title-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 13\n          }, this), \"Subscription Management\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"page-subtitle\",\n          children: \"Manage your subscription and access premium features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 538,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 506,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        className: \"current-subscription\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Current Subscription\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 11\n        }, this), subscriptionStatus === 'active' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card active\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n              className: \"status-icon active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Active Subscription\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCrown, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 558,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Plan: \", (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData$act = subscriptionData.activePlan) === null || _subscriptionData$act === void 0 ? void 0 : _subscriptionData$act.title) || 'Premium Plan']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 557,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 562,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Expires: \", formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 566,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Days Remaining: \", getDaysRemaining()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 13\n        }, this), subscriptionStatus === 'expired' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card expired\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n              className: \"status-icon expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Subscription Expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Expired: \", formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 582,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"renewal-message\",\n              children: \"Your subscription has expired. Choose a new plan below to continue accessing premium features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 13\n        }, this), subscriptionStatus === 'none' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card none\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaUser, {\n              className: \"status-icon none\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Free Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"upgrade-message\",\n              children: \"You're currently using a free account. Upgrade to a premium plan to unlock all features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 542,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"available-plans\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: subscriptionStatus === 'active' ? '🚀 Upgrade Your Plan' : subscriptionStatus === 'expired' ? '🔄 Renew Your Subscription' : '🎯 Choose Your Plan'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 613,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '10px',\n            justifyContent: 'center',\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: testProcessingModal,\n            style: {\n              background: '#ff6b6b',\n              color: 'white',\n              border: 'none',\n              padding: '8px 16px',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\uD83E\\uDDEA Test Processing Modal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: testSuccessModal,\n            style: {\n              background: '#51cf66',\n              color: 'white',\n              border: 'none',\n              padding: '8px 16px',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\uD83E\\uDDEA Test Success Modal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle\",\n          children: subscriptionStatus === 'active' ? 'Upgrade to a longer plan for better value and extended access' : subscriptionStatus === 'expired' ? 'Your subscription has expired. Renew now to continue accessing premium features' : 'Select a subscription plan to unlock all premium features and start your learning journey'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 653,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 664,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading plans...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 13\n        }, this) : plans.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-plans-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-plans-icon\",\n            children: \"\\uD83D\\uDCCB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 669,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Plans Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 670,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Plans are currently being loaded. Please refresh the page or try again later.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 671,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"refresh-btn\",\n            onClick: fetchPlans,\n            children: \"\\uD83D\\uDD04 Refresh Plans\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 672,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 668,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plans-grid\",\n          children: plans.map(plan => {\n            var _plan$title, _plan$discountedPrice, _plan$actualPrice, _plan$features;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: \"plan-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"plan-title\",\n                  children: plan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 686,\n                  columnNumber: 21\n                }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('standard')) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"plan-badge\",\n                  children: \"\\uD83D\\uDD25 Popular\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 688,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 685,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-pricing\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price-display\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"current-price\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"currency\",\n                      children: \"TZS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 695,\n                      columnNumber: 25\n                    }, this), (_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 694,\n                    columnNumber: 23\n                  }, this), plan.actualPrice > plan.discountedPrice && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"original-price\",\n                      children: [(_plan$actualPrice = plan.actualPrice) === null || _plan$actualPrice === void 0 ? void 0 : _plan$actualPrice.toLocaleString(), \" TZS\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 700,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"discount-badge\",\n                      children: [Math.round((plan.actualPrice - plan.discountedPrice) / plan.actualPrice * 100), \"% OFF\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 701,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 693,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"plan-duration\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"duration-highlight\",\n                    children: plan.duration\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 708,\n                    columnNumber: 23\n                  }, this), \" month\", plan.duration > 1 ? 's' : '', \" access\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 707,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 692,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-features\",\n                children: (_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.slice(0, 5).map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                    className: \"feature-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 715,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: feature\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 716,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 714,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"select-plan-btn\",\n                onClick: () => handlePlanSelect(plan),\n                disabled: paymentLoading === plan._id,\n                style: {\n                  background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '12px',\n                  padding: '1rem 1.5rem',\n                  fontSize: '1rem',\n                  fontWeight: '600',\n                  cursor: paymentLoading === plan._id ? 'not-allowed' : 'pointer',\n                  transition: 'all 0.3s ease',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: '0.5rem',\n                  width: '100%',\n                  opacity: paymentLoading === plan._id ? 0.6 : 1\n                },\n                onMouseEnter: e => {\n                  if (paymentLoading !== plan._id) {\n                    e.target.style.background = 'linear-gradient(135deg, #1d4ed8, #1e40af)';\n                    e.target.style.transform = 'translateY(-2px)';\n                    e.target.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.4)';\n                  }\n                },\n                onMouseLeave: e => {\n                  if (paymentLoading !== plan._id) {\n                    e.target.style.background = 'linear-gradient(135deg, #3b82f6, #1d4ed8)';\n                    e.target.style.transform = 'translateY(0)';\n                    e.target.style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.3)';\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(FaCreditCard, {\n                  className: \"btn-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 757,\n                  columnNumber: 21\n                }, this), paymentLoading === plan._id ? 'Processing...' : subscriptionStatus === 'active' ? 'Click to Upgrade' : subscriptionStatus === 'expired' ? 'Click to Renew' : 'Click to Pay']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 721,\n                columnNumber: 19\n              }, this)]\n            }, plan._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 677,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 607,\n        columnNumber: 9\n      }, this), (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.6\n        },\n        className: \"phone-warning\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"warning-content\",\n          children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n            className: \"warning-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 782,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Phone Number Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 784,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Please update your phone number in your profile to subscribe to a plan.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 785,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"update-phone-btn\",\n              onClick: () => window.location.href = '/profile',\n              children: \"Update Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 783,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 781,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 775,\n        columnNumber: 11\n      }, this), showProcessingModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-overlay-best\",\n        style: {\n          position: 'fixed',\n          top: '0',\n          left: '0',\n          width: '100vw',\n          height: '100vh',\n          background: 'rgba(0, 0, 0, 0.85)',\n          backdropFilter: 'blur(15px)',\n          WebkitBackdropFilter: 'blur(15px)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: '10000',\n          padding: '20px',\n          boxSizing: 'border-box'\n        },\n        onClick: e => e.target === e.currentTarget && handleCloseProcessingModal(),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-container-best\",\n          style: {\n            background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',\n            borderRadius: '20px',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',\n            border: '1px solid rgba(59, 130, 246, 0.1)',\n            width: '100%',\n            maxWidth: '480px',\n            maxHeight: '90vh',\n            display: 'flex',\n            flexDirection: 'column',\n            overflow: 'hidden',\n            position: 'relative',\n            transform: 'translateZ(0)'\n          },\n          onClick: e => e.stopPropagation(),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              padding: '1.5rem',\n              color: 'white',\n              textAlign: 'center',\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"payment-modal-close\",\n              onClick: handleCloseProcessingModal,\n              title: \"Close payment window\",\n              style: {\n                position: 'absolute',\n                top: '12px',\n                right: '12px',\n                background: 'rgba(255, 255, 255, 0.2)',\n                border: 'none',\n                borderRadius: '50%',\n                width: '32px',\n                height: '32px',\n                color: 'white',\n                fontSize: '16px',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                transition: 'all 0.3s ease',\n                zIndex: 10\n              },\n              onMouseEnter: e => e.target.style.background = 'rgba(255, 255, 255, 0.3)',\n              onMouseLeave: e => e.target.style.background = 'rgba(255, 255, 255, 0.2)',\n              children: \"\\u2715\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 845,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '0.5rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"payment-processing-animation\",\n                style: {\n                  width: '60px',\n                  height: '60px',\n                  margin: '0 auto 1rem',\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"payment-spinner\",\n                  style: {\n                    width: '100%',\n                    height: '100%',\n                    border: '3px solid rgba(255, 255, 255, 0.3)',\n                    borderTop: '3px solid white',\n                    borderRadius: '50%',\n                    animation: 'spin 1s linear infinite'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 880,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: 'translate(-50%, -50%)',\n                    fontSize: '20px'\n                  },\n                  children: \"\\uD83D\\uDCB3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 888,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: '0',\n                fontSize: '1.4rem',\n                fontWeight: '700',\n                textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'\n              },\n              children: \"Processing Payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 898,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0.5rem 0 0 0',\n                fontSize: '0.9rem',\n                opacity: '0.9'\n              },\n              children: \"Secure transaction in progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 906,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: '1',\n              overflowY: 'auto',\n              overflowX: 'hidden',\n              padding: '0'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '20px',\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '16px',\n                minHeight: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(59, 130, 246, 0.05)',\n                  border: '1px solid rgba(59, 130, 246, 0.2)',\n                  borderRadius: '10px',\n                  padding: '0.75rem',\n                  textAlign: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: '0',\n                    color: '#1e40af',\n                    fontSize: '0.95rem',\n                    fontWeight: '500'\n                  },\n                  children: paymentStatus\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 937,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 930,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(16, 185, 129, 0.05)',\n                  border: '1px solid rgba(16, 185, 129, 0.2)',\n                  borderRadius: '10px',\n                  padding: '0.75rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 0.5rem 0',\n                    color: '#065f46',\n                    fontSize: '1.1rem',\n                    fontWeight: '600'\n                  },\n                  children: selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 954,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    fontSize: '0.9rem',\n                    color: '#047857'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Amount: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : (_selectedPlan$discoun = selectedPlan.discountedPrice) === null || _selectedPlan$discoun === void 0 ? void 0 : _selectedPlan$discoun.toLocaleString(), \" TZS\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 969,\n                      columnNumber: 35\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 969,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Duration: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration, \" month\", (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration) > 1 ? 's' : '']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 970,\n                      columnNumber: 37\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 970,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 962,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 948,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(245, 158, 11, 0.05)',\n                  border: '1px solid rgba(245, 158, 11, 0.2)',\n                  borderRadius: '10px',\n                  padding: '0.75rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 0.75rem 0',\n                    color: '#92400e',\n                    fontSize: '1rem',\n                    fontWeight: '600',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  },\n                  children: \"\\uD83D\\uDCF1 Check Your Phone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 981,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: '0 0 0.75rem 0',\n                    color: '#b45309',\n                    fontSize: '0.9rem',\n                    fontWeight: '500',\n                    background: 'rgba(245, 158, 11, 0.1)',\n                    padding: '0.5rem',\n                    borderRadius: '8px',\n                    textAlign: 'center'\n                  },\n                  children: [\"Number: \", user === null || user === void 0 ? void 0 : user.phoneNumber]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 992,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#a16207',\n                    fontSize: '0.85rem',\n                    lineHeight: '1.5'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: '0.25rem'\n                    },\n                    children: \"1. You will receive an SMS with payment instructions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1009,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: '0.25rem'\n                    },\n                    children: \"2. Follow the SMS steps to confirm payment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1010,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: \"3. Complete the mobile money transaction\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1011,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1004,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 975,\n                columnNumber: 17\n              }, this), showTryAgain && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(239, 68, 68, 0.05)',\n                  border: '1px solid rgba(239, 68, 68, 0.2)',\n                  borderRadius: '12px',\n                  padding: '16px',\n                  textAlign: 'center',\n                  marginTop: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: '0 0 0.5rem 0',\n                    color: '#dc2626',\n                    fontSize: '0.9rem',\n                    fontWeight: '600'\n                  },\n                  children: \"\\u26A0\\uFE0F Taking longer than expected?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1025,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: '0 0 1rem 0',\n                    color: '#b91c1c',\n                    fontSize: '0.8rem'\n                  },\n                  children: \"If you haven't received SMS or facing connection issues:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1033,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"try-again-button\",\n                  onClick: handleTryAgain,\n                  style: {\n                    background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                    color: 'white',\n                    border: 'none',\n                    padding: '0.75rem 1.5rem',\n                    borderRadius: '10px',\n                    fontSize: '0.9rem',\n                    fontWeight: '600',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',\n                    width: '100%',\n                    maxWidth: '200px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    gap: '0.5rem',\n                    margin: '0 auto'\n                  },\n                  onMouseEnter: e => {\n                    e.target.style.background = 'linear-gradient(135deg, #1d4ed8 0%, #1e3a8a 100%)';\n                    e.target.style.transform = 'translateY(-2px)';\n                    e.target.style.boxShadow = '0 6px 16px rgba(59, 130, 246, 0.4)';\n                  },\n                  onMouseLeave: e => {\n                    e.target.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';\n                    e.target.style.transform = 'translateY(0)';\n                    e.target.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.3)';\n                  },\n                  children: \"\\uD83D\\uDD04 Try Again\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1040,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1017,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 922,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 916,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 819,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 799,\n        columnNumber: 11\n      }, this), showSuccessModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-overlay-best\",\n        style: {\n          position: 'fixed',\n          top: '0',\n          left: '0',\n          width: '100vw',\n          height: '100vh',\n          background: 'rgba(0, 0, 0, 0.9)',\n          backdropFilter: 'blur(20px)',\n          WebkitBackdropFilter: 'blur(20px)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: '10000',\n          padding: '20px',\n          boxSizing: 'border-box'\n        },\n        onClick: e => {\n          if (e.target === e.currentTarget) {\n            setAutoNavigateCountdown(null); // Clear countdown\n            setShowSuccessModal(false);\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-container-best success-modal-best\",\n          style: {\n            background: 'linear-gradient(145deg, #ffffff 0%, #f0fdf4 100%)',\n            borderRadius: '24px',\n            boxShadow: '0 25px 80px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(34, 197, 94, 0.2)',\n            border: '2px solid rgba(34, 197, 94, 0.1)',\n            width: '100%',\n            maxWidth: '600px',\n            maxHeight: '95vh',\n            display: 'flex',\n            flexDirection: 'column',\n            overflow: 'hidden',\n            position: 'relative',\n            transform: 'translateZ(0)'\n          },\n          onClick: e => e.stopPropagation(),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n              padding: '2rem',\n              color: 'white',\n              textAlign: 'center',\n              position: 'relative',\n              borderRadius: '28px 28px 0 0',\n              flexShrink: 0\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setAutoNavigateCountdown(null); // Clear countdown\n                setShowSuccessModal(false);\n              },\n              style: {\n                position: 'absolute',\n                top: '16px',\n                right: '16px',\n                background: 'rgba(255, 255, 255, 0.2)',\n                border: 'none',\n                borderRadius: '50%',\n                width: '36px',\n                height: '36px',\n                color: 'white',\n                fontSize: '18px',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                transition: 'all 0.3s ease',\n                zIndex: 10\n              },\n              onMouseEnter: e => e.target.style.background = 'rgba(255, 255, 255, 0.3)',\n              onMouseLeave: e => e.target.style.background = 'rgba(255, 255, 255, 0.2)',\n              children: \"\\u2715\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1138,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '4rem',\n                marginBottom: '1rem',\n                animation: 'bounce 2s infinite'\n              },\n              children: \"\\uD83C\\uDF89\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1167,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                margin: '0',\n                fontSize: '2rem',\n                fontWeight: '800',\n                textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',\n                marginBottom: '0.5rem'\n              },\n              children: \"Payment Successful!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1175,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0',\n                fontSize: '1.1rem',\n                opacity: '0.95',\n                fontWeight: '500'\n              },\n              children: [\"Welcome to \", selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title, \"! \\uD83D\\uDE80\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1185,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1129,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: '1',\n              overflowY: 'auto',\n              overflowX: 'hidden',\n              padding: '0'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '24px',\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '20px',\n                minHeight: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)',\n                  border: '2px solid #22c55e',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  textAlign: 'center',\n                  boxShadow: '0 4px 20px rgba(34, 197, 94, 0.2)'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    background: 'rgba(34, 197, 94, 0.1)',\n                    borderRadius: '12px',\n                    padding: '1rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    style: {\n                      color: '#15803d',\n                      marginBottom: '1rem',\n                      fontSize: '1.2rem',\n                      fontWeight: '700'\n                    },\n                    children: \"\\uD83C\\uDFAF Subscription Details\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1224,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'grid',\n                      gap: '0.75rem',\n                      textAlign: 'left'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.5rem',\n                        background: 'rgba(255, 255, 255, 0.7)',\n                        borderRadius: '8px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          fontWeight: '600',\n                          color: '#374151'\n                        },\n                        children: \"\\uD83D\\uDCCB Plan:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1245,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: '#059669',\n                          fontWeight: '700'\n                        },\n                        children: selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1246,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1237,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.5rem',\n                        background: 'rgba(255, 255, 255, 0.7)',\n                        borderRadius: '8px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          fontWeight: '600',\n                          color: '#374151'\n                        },\n                        children: \"\\u23F0 Duration:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1256,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: '#059669',\n                          fontWeight: '700'\n                        },\n                        children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration, \" month\", (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration) > 1 ? 's' : '']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1257,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1248,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.5rem',\n                        background: 'rgba(255, 255, 255, 0.7)',\n                        borderRadius: '8px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          fontWeight: '600',\n                          color: '#374151'\n                        },\n                        children: \"\\uD83D\\uDCB0 Amount:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1267,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: '#059669',\n                          fontWeight: '700'\n                        },\n                        children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : (_selectedPlan$discoun2 = selectedPlan.discountedPrice) === null || _selectedPlan$discoun2 === void 0 ? void 0 : _selectedPlan$discoun2.toLocaleString(), \" TZS\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1268,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1259,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.5rem',\n                        background: 'linear-gradient(135deg, #22c55e, #16a34a)',\n                        borderRadius: '8px',\n                        color: 'white'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          fontWeight: '600'\n                        },\n                        children: \"\\uD83D\\uDC8E Status:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1279,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          fontWeight: '800',\n                          fontSize: '1.1rem'\n                        },\n                        children: \"ACTIVE \\u2705\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1280,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1270,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1232,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1218,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1210,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%)',\n                  border: '2px solid #f59e0b',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  boxShadow: '0 4px 20px rgba(245, 158, 11, 0.2)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    color: '#92400e',\n                    marginBottom: '1rem',\n                    textAlign: 'center',\n                    fontSize: '1.2rem',\n                    fontWeight: '700'\n                  },\n                  children: \"\\uD83D\\uDE80 All Premium Features Unlocked!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1294,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                    gap: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: 'rgba(255, 255, 255, 0.8)',\n                      borderRadius: '12px',\n                      padding: '1rem'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginBottom: '0.5rem',\n                        fontSize: '0.95rem',\n                        fontWeight: '600',\n                        color: '#374151'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          marginBottom: '0.25rem'\n                        },\n                        children: [\"\\u2705 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Unlimited Quizzes\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1314,\n                          columnNumber: 68\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1314,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          marginBottom: '0.25rem'\n                        },\n                        children: [\"\\uD83E\\uDD16 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"AI Assistant\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1315,\n                          columnNumber: 69\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1315,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [\"\\uD83D\\uDCDA \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Study Materials\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1316,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1316,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1313,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1308,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: 'rgba(255, 255, 255, 0.8)',\n                      borderRadius: '12px',\n                      padding: '1rem'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginBottom: '0.5rem',\n                        fontSize: '0.95rem',\n                        fontWeight: '600',\n                        color: '#374151'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          marginBottom: '0.25rem'\n                        },\n                        children: [\"\\uD83D\\uDCCA \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Progress Tracking\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1325,\n                          columnNumber: 69\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1325,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          marginBottom: '0.25rem'\n                        },\n                        children: [\"\\uD83C\\uDFA5 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Learning Videos\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1326,\n                          columnNumber: 69\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1326,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [\"\\uD83D\\uDCAC \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Forum Access\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1327,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1327,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1324,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1319,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1303,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1287,\n                columnNumber: 17\n              }, this), autoNavigateCountdown && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%)',\n                  border: '2px solid #0288d1',\n                  borderRadius: '12px',\n                  padding: '1rem',\n                  textAlign: 'center',\n                  marginTop: '1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: '0',\n                    color: '#01579b',\n                    fontSize: '0.9rem',\n                    fontWeight: '600'\n                  },\n                  children: [\"\\uD83D\\uDE80 Automatically redirecting to Hub in \", autoNavigateCountdown, \" seconds...\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1343,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1335,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  gap: '1rem',\n                  justifyContent: 'center',\n                  flexWrap: 'wrap',\n                  marginTop: 'auto',\n                  paddingTop: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setAutoNavigateCountdown(null); // Clear countdown\n                    setShowSuccessModal(false);\n                    window.location.href = '/user/hub';\n                  },\n                  style: {\n                    background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                    border: 'none',\n                    color: 'white',\n                    padding: '1rem 2rem',\n                    borderRadius: '12px',\n                    fontSize: '1rem',\n                    fontWeight: '700',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    boxShadow: '0 4px 20px rgba(59, 130, 246, 0.3)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    minWidth: '180px',\n                    justifyContent: 'center'\n                  },\n                  onMouseEnter: e => {\n                    e.target.style.background = 'linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%)';\n                    e.target.style.transform = 'translateY(-2px)';\n                    e.target.style.boxShadow = '0 6px 25px rgba(59, 130, 246, 0.4)';\n                  },\n                  onMouseLeave: e => {\n                    e.target.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';\n                    e.target.style.transform = 'translateY(0)';\n                    e.target.style.boxShadow = '0 4px 20px rgba(59, 130, 246, 0.3)';\n                  },\n                  children: [\"\\uD83C\\uDFE0 Continue to Hub \", autoNavigateCountdown ? `(${autoNavigateCountdown}s)` : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1363,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setAutoNavigateCountdown(null); // Clear countdown\n                    setShowSuccessModal(false);\n                  },\n                  style: {\n                    background: 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)',\n                    border: '2px solid #d1d5db',\n                    color: '#374151',\n                    padding: '1rem 2rem',\n                    borderRadius: '12px',\n                    fontSize: '1rem',\n                    fontWeight: '600',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    minWidth: '120px',\n                    justifyContent: 'center'\n                  },\n                  onMouseEnter: e => {\n                    e.target.style.background = 'linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%)';\n                    e.target.style.transform = 'translateY(-1px)';\n                    e.target.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.15)';\n                  },\n                  onMouseLeave: e => {\n                    e.target.style.background = 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)';\n                    e.target.style.transform = 'translateY(0)';\n                    e.target.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';\n                  },\n                  children: \"\\u2728 Close\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1399,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1355,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: '1.5rem',\n                  padding: '1rem',\n                  background: 'rgba(34, 197, 94, 0.1)',\n                  borderRadius: '12px',\n                  textAlign: 'center',\n                  border: '1px solid rgba(34, 197, 94, 0.2)'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: '0',\n                    fontSize: '1rem',\n                    color: '#15803d',\n                    fontWeight: '600',\n                    lineHeight: '1.5'\n                  },\n                  children: \"\\uD83C\\uDF89 Congratulations! You now have full access to all BrainWave features. Start exploring and excel in your studies! \\uD83D\\uDE80\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1445,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1437,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1202,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1196,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1110,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1085,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(UpgradeRestrictionModal, {\n        visible: showUpgradeRestriction,\n        onClose: () => setShowUpgradeRestriction(false),\n        currentPlan: plans.find(p => p._id === (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.activePlan)) || (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.plan),\n        subscription: subscriptionData,\n        user: user\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1462,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SubscriptionExpiredModal, {\n        visible: showExpiredModal,\n        onClose: () => setShowExpiredModal(false),\n        onRenew: handleRenewSubscription,\n        subscription: subscriptionData,\n        user: user,\n        plans: plans\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1471,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 504,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 503,\n    columnNumber: 5\n  }, this);\n};\n_s(Subscription, \"CFfbvS/cIqXwYgbj+uGA0/ZAfD4=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c = Subscription;\nexport default Subscription;\nvar _c;\n$RefreshReg$(_c, \"Subscription\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "motion", "message", "FaCrown", "FaCalendarAlt", "FaCheckCircle", "FaTimesCircle", "FaCreditCard", "FaUser", "getPlans", "addPayment", "checkPaymentStatus", "ShowLoading", "HideLoading", "UpgradeRestrictionModal", "SubscriptionExpiredModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Subscription", "_s", "_subscriptionData$act", "_selectedPlan$discoun", "_selectedPlan$discoun2", "plans", "setPlans", "loading", "setLoading", "paymentLoading", "setPaymentLoading", "showProcessingModal", "setShowProcessingModal", "console", "log", "showSuccessModal", "setShowSuccessModal", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "paymentStatus", "setPaymentStatus", "showUpgradeRestriction", "setShowUpgradeRestriction", "showExpiredModal", "setShowExpiredModal", "processingStartTime", "setProcessingStartTime", "showTryAgain", "setShowTryAgain", "autoNavigateCountdown", "setAutoNavigateCountdown", "user", "state", "subscriptionData", "subscription", "dispatch", "samplePlans", "_id", "title", "features", "actualPrice", "discountedPrice", "discountPercentage", "duration", "status", "fetchPlans", "checkCurrentSubscription", "scrollY", "window", "document", "body", "style", "overflow", "position", "width", "top", "scrollTo", "parseInt", "isSubscriptionExpired", "response", "success", "data", "length", "Array", "isArray", "warn", "info", "error", "warning", "endDate", "Date", "today", "setHours", "handleRenewSubscription", "handlePlanSelect", "handleCloseProcessingModal", "handleTryAgain", "testSuccessModal", "testProcessingModal", "plan", "phoneNumber", "test", "_user$name", "now", "Promise", "resolve", "setTimeout", "tryAgainTimer", "paymentData", "userId", "userPhone", "userEmail", "email", "name", "replace", "toLowerCase", "_response$data", "order_id", "content", "marginTop", "orderIdToCheck", "checkPaymentConfirmation", "Error", "orderId", "isPolling", "handleVisibilityChange", "attempts", "maxAttempts", "pollPaymentStatus", "statusResponse", "removeEventListener", "fontSize", "countdownInterval", "setInterval", "prev", "clearInterval", "location", "href", "includes", "hidden", "addEventListener", "getSubscriptionStatus", "subscriptionStatus", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "getDaysRemaining", "diffTime", "diffDays", "Math", "ceil", "max", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "onClick", "right", "background", "color", "border", "padding", "borderRadius", "cursor", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "activePlan", "display", "gap", "justifyContent", "marginBottom", "map", "_plan$title", "_plan$discountedPrice", "_plan$actualPrice", "_plan$features", "whileHover", "scale", "whileTap", "toLocaleString", "round", "slice", "feature", "index", "disabled", "fontWeight", "alignItems", "onMouseEnter", "e", "target", "transform", "boxShadow", "onMouseLeave", "left", "height", "<PERSON><PERSON>ilter", "WebkitBackdropFilter", "boxSizing", "currentTarget", "max<PERSON><PERSON><PERSON>", "maxHeight", "flexDirection", "stopPropagation", "textAlign", "margin", "borderTop", "animation", "textShadow", "flex", "overflowY", "overflowX", "minHeight", "lineHeight", "flexShrink", "gridTemplateColumns", "flexWrap", "paddingTop", "min<PERSON><PERSON><PERSON>", "visible", "onClose", "currentPlan", "find", "p", "onRenew", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Subscription/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport UpgradeRestrictionModal from '../../../components/UpgradeRestrictionModal/UpgradeRestrictionModal';\nimport SubscriptionExpiredModal from '../../../components/SubscriptionExpiredModal/SubscriptionExpiredModal';\nimport './Subscription.css';\n\nconst Subscription = () => {\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(null); // Changed to store plan ID instead of boolean\n  const [showProcessingModal, setShowProcessingModal] = useState(false);\n\n  // Debug: Log showProcessingModal state changes\n  useEffect(() => {\n    console.log('🔍 showProcessingModal state changed to:', showProcessingModal);\n  }, [showProcessingModal]);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [paymentStatus, setPaymentStatus] = useState('');\n  const [showUpgradeRestriction, setShowUpgradeRestriction] = useState(false);\n  const [showExpiredModal, setShowExpiredModal] = useState(false);\n  const [processingStartTime, setProcessingStartTime] = useState(null);\n  const [showTryAgain, setShowTryAgain] = useState(false);\n  const [autoNavigateCountdown, setAutoNavigateCountdown] = useState(null);\n  const { user } = useSelector((state) => state.user);\n  const { subscriptionData } = useSelector((state) => state.subscription);\n  const dispatch = useDispatch();\n\n  // Fallback sample plans in case API fails\n  const samplePlans = [\n    {\n      _id: \"basic-plan-sample\",\n      title: \"Basic Membership\",\n      features: [\n        \"2-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\"\n      ],\n      actualPrice: 28570,\n      discountedPrice: 20000,\n      discountPercentage: 30,\n      duration: 2,\n      status: true\n    },\n    {\n      _id: \"premium-plan-sample\",\n      title: \"Premium Plan\",\n      features: [\n        \"3-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\",\n        \"Priority support\"\n      ],\n      actualPrice: 45000,\n      discountedPrice: 35000,\n      discountPercentage: 22,\n      duration: 3,\n      status: true\n    }\n  ];\n\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n  }, []);\n\n  // Handle body scroll lock when modals are open\n  useEffect(() => {\n    if (showProcessingModal || showSuccessModal) {\n      // Lock body scroll\n      const scrollY = window.scrollY;\n      document.body.style.overflow = 'hidden';\n      document.body.style.position = 'fixed';\n      document.body.style.width = '100%';\n      document.body.style.top = `-${scrollY}px`;\n    } else {\n      // Unlock body scroll\n      const scrollY = document.body.style.top;\n      document.body.style.overflow = '';\n      document.body.style.position = '';\n      document.body.style.width = '';\n      document.body.style.top = '';\n      if (scrollY) {\n        window.scrollTo(0, parseInt(scrollY || '0') * -1);\n      }\n    }\n\n    // Cleanup on unmount\n    return () => {\n      document.body.style.overflow = '';\n      document.body.style.position = '';\n      document.body.style.width = '';\n      document.body.style.top = '';\n    };\n  }, [showProcessingModal, showSuccessModal]);\n\n  // Check for expired subscription and show modal\n  useEffect(() => {\n    if (subscriptionData && isSubscriptionExpired()) {\n      console.log('🚫 Subscription expired, showing modal');\n      setShowExpiredModal(true);\n    } else {\n      setShowExpiredModal(false);\n    }\n  }, [subscriptionData]);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      console.log('Fetching plans...');\n      const response = await getPlans();\n      console.log('Plans response:', response);\n\n      if (response.success && response.data && response.data.length > 0) {\n        setPlans(response.data);\n        console.log('Plans loaded successfully from API:', response.data);\n      } else if (Array.isArray(response) && response.length > 0) {\n        // Handle case where response is directly an array of plans\n        setPlans(response);\n        console.log('Plans loaded as array from API:', response);\n      } else {\n        console.warn('No plans from API, using sample plans');\n        setPlans(samplePlans);\n        message.info('Showing sample plans. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('Error loading plans from API:', error);\n      console.log('Using fallback sample plans');\n      setPlans(samplePlans);\n      message.warning('Using sample plans. Please check your connection and try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n      console.log('Current subscription:', response);\n    } catch (error) {\n      console.log('No active subscription found');\n    }\n  };\n\n  // Check if subscription is expired\n  const isSubscriptionExpired = () => {\n    if (!subscriptionData) return true;\n\n    // If no subscription data, consider expired\n    if (!subscriptionData.endDate) return true;\n\n    // If payment status is not paid, consider expired\n    if (subscriptionData.paymentStatus !== 'paid') return true;\n\n    // If status is not active, consider expired\n    if (subscriptionData.status !== 'active') return true;\n\n    // Check if end date has passed\n    const endDate = new Date(subscriptionData.endDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n    endDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    return endDate < today;\n  };\n\n  // Handle subscription renewal from expired modal\n  const handleRenewSubscription = async (selectedPlan) => {\n    setShowExpiredModal(false);\n    await handlePlanSelect(selectedPlan);\n  };\n\n  // Handle closing payment processing modal\n  const handleCloseProcessingModal = () => {\n    setShowProcessingModal(false);\n    setPaymentLoading(null); // Reset to null instead of false\n    setShowTryAgain(false);\n    setProcessingStartTime(null);\n    setPaymentStatus('');\n    message.info('Payment process cancelled. You can try again anytime.');\n  };\n\n  // Handle try again functionality\n  const handleTryAgain = () => {\n    if (selectedPlan) {\n      setShowTryAgain(false);\n      setProcessingStartTime(null);\n      handlePlanSelect(selectedPlan);\n    }\n  };\n\n  // Test success modal (for debugging)\n  const testSuccessModal = () => {\n    console.log('🧪 Testing success modal...');\n    setShowProcessingModal(false);\n    setShowSuccessModal(true);\n    setPaymentLoading(null);\n  };\n\n  // Test processing modal (for debugging)\n  const testProcessingModal = () => {\n    console.log('🧪 Testing processing modal...');\n    setShowProcessingModal(true);\n    setPaymentStatus('Testing processing modal...');\n    setSelectedPlan(plans[0] || { title: 'Test Plan', discountedPrice: 5000, duration: 1 });\n  };\n\n  const handlePlanSelect = async (plan) => {\n    // Check if user already has an active subscription\n    if (subscriptionData && subscriptionData.status === 'active' && subscriptionData.paymentStatus === 'paid') {\n      console.log('🚫 User already has active subscription:', subscriptionData);\n      setShowUpgradeRestriction(true);\n      return;\n    }\n\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n\n    try {\n      console.log('🚀 Starting payment for plan:', plan.title);\n      console.log('🔧 IMMEDIATELY showing processing modal...');\n\n      // IMMEDIATELY show processing modal when user chooses plan\n      setSelectedPlan(plan);\n      setPaymentLoading(plan._id);\n      setShowProcessingModal(true);\n      setShowTryAgain(false);\n      setProcessingStartTime(Date.now());\n      setPaymentStatus('🚀 Preparing your payment request...');\n\n      console.log('✅ Processing modal IMMEDIATELY displayed');\n\n      // Small delay to ensure modal is visible before API call\n      await new Promise(resolve => setTimeout(resolve, 200));\n\n      // Set timer for try again button (10 seconds)\n      const tryAgainTimer = setTimeout(() => {\n        setShowTryAgain(true);\n      }, 10000);\n\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n\n      setPaymentStatus('📤 Sending payment request to ZenoPay...');\n      const response = await addPayment(paymentData);\n\n      if (response.success) {\n        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');\n\n        console.log('💳 Payment response:', response);\n        console.log('🆔 Order ID:', response.order_id);\n\n        // Show confirmation message to user\n        message.success({\n          content: `💳 Payment initiated! 📱 Check your phone (${user.phoneNumber}) for SMS confirmation from ZenoPay.`,\n          duration: 8,\n          style: {\n            marginTop: '20vh',\n          }\n        });\n\n        // Start checking payment status immediately\n        const orderIdToCheck = response.order_id || response.data?.order_id || 'demo_order';\n        console.log('🔍 Starting payment confirmation check for order:', orderIdToCheck);\n\n        checkPaymentConfirmation(orderIdToCheck);\n\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('❌ Payment failed:', error);\n      setShowProcessingModal(false);\n      message.error('Payment failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const checkPaymentConfirmation = async (orderId) => {\n    console.log('🚀 Starting payment confirmation check for order:', orderId);\n    let isPolling = true;\n    let handleVisibilityChange;\n\n    try {\n      setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n\n      // Poll payment status every 2 seconds for optimal responsiveness\n      let attempts = 0;\n      const maxAttempts = 150; // 150 attempts * 2 seconds = 5 minutes\n\n      const pollPaymentStatus = async () => {\n        attempts++;\n        console.log(`🔍 Payment status check attempt ${attempts}/${maxAttempts} for order:`, orderId);\n\n        try {\n          const statusResponse = await checkPaymentStatus({ orderId });\n          console.log('📊 Payment status response:', statusResponse);\n          console.log('🔍 Checking payment conditions:');\n          console.log('  - Live payment:', statusResponse?.paymentStatus === 'paid' && statusResponse?.status === 'active');\n          console.log('  - Demo payment:', statusResponse?.status === 'completed' && statusResponse?.success === true);\n\n          if (statusResponse && (\n            (statusResponse.paymentStatus === 'paid' && statusResponse.status === 'active') ||\n            (statusResponse.status === 'completed' && statusResponse.success === true)\n          )) {\n            // Payment confirmed immediately!\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('🎉 Payment confirmed! Activating your subscription...');\n            console.log('✅ Payment confirmed, preparing to show success modal...');\n\n            // Show success INSTANTLY - no delay\n            console.log('🔄 Setting modal states - Processing: false, Success: true');\n            setShowProcessingModal(false);\n            setShowSuccessModal(true);\n            setPaymentLoading(null);\n            console.log('✅ Success modal state set to true');\n\n            // Refresh subscription data\n            checkCurrentSubscription();\n\n            // Show immediate success message\n            message.success({\n              content: '🎉 Payment confirmed! All features are now unlocked!',\n              duration: 5,\n              style: {\n                marginTop: '20vh',\n                fontSize: '16px'\n              }\n            });\n\n            // Start countdown for auto-navigation to hub\n            setAutoNavigateCountdown(5);\n            const countdownInterval = setInterval(() => {\n              setAutoNavigateCountdown(prev => {\n                if (prev <= 1) {\n                  clearInterval(countdownInterval);\n                  console.log('🏠 Auto-navigating to hub after successful payment...');\n                  setShowSuccessModal(false);\n                  window.location.href = '/user/hub';\n                  return null;\n                }\n                return prev - 1;\n              });\n            }, 1000);\n\n          } else if (attempts >= maxAttempts) {\n            // Timeout - but don't fail completely\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('⏰ Still waiting for confirmation. Please complete the payment on your phone.');\n\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setPaymentLoading(null); // Reset to null\n              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status or try again.');\n            }, 2000);\n\n          } else {\n            // Continue polling - NO TIME INDICATION, just encouraging message\n            setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n            setTimeout(pollPaymentStatus, 2000); // Check every 2 seconds for better performance\n          }\n\n        } catch (error) {\n          console.error('Payment status check error:', error);\n\n          // Handle specific error types\n          if (error.message && error.message.includes('404')) {\n            console.error('❌ Payment status endpoint not found (404)');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Payment verification service is temporarily unavailable. Please contact support or check your subscription status manually.');\n            return;\n          }\n\n          if (error.message && error.message.includes('401')) {\n            console.error('❌ Authentication required for payment status check');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Please login again to check payment status.');\n            return;\n          }\n\n          if (attempts >= maxAttempts) {\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Unable to confirm payment status. Please check your subscription status manually.');\n          } else {\n            // Continue polling even if there's an error (unless it's a critical error)\n            setTimeout(pollPaymentStatus, 1000);\n          }\n        }\n      };\n\n      // Add visibility change listener to check immediately when user returns to tab\n      handleVisibilityChange = () => {\n        if (!document.hidden && isPolling) {\n          console.log('User returned to tab, checking payment status immediately...');\n          setPaymentStatus('🔍 Checking payment status...');\n          // Trigger immediate check\n          setTimeout(() => pollPaymentStatus(), 100);\n        }\n      };\n\n      document.addEventListener('visibilitychange', handleVisibilityChange);\n\n      // Start polling immediately (no delay) - check right away\n      setTimeout(pollPaymentStatus, 500); // Start checking after 0.5 seconds\n\n    } catch (error) {\n      isPolling = false; // Stop polling\n      if (handleVisibilityChange) {\n        document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n      }\n      setShowProcessingModal(false);\n      message.error('Payment confirmation failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    \n    if (user?.subscriptionStatus === 'expired' || (subscriptionData && subscriptionData.status === 'expired')) {\n      return 'expired';\n    }\n    \n    return 'none';\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const getDaysRemaining = () => {\n    if (!subscriptionData?.endDate) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n\n  const subscriptionStatus = getSubscriptionStatus();\n\n  return (\n    <div className=\"subscription-page\">\n      <div className=\"subscription-container\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"subscription-header\"\n        >\n          {/* Debug button - remove in production */}\n          <button\n            onClick={() => {\n              console.log('🧪 Testing success modal...');\n              setSelectedPlan(plans[0] || { title: 'Test Plan', duration: 1, discountedPrice: 13000 });\n              setShowSuccessModal(true);\n            }}\n            style={{\n              position: 'fixed',\n              top: '10px',\n              right: '10px',\n              background: '#52c41a',\n              color: 'white',\n              border: 'none',\n              padding: '8px 16px',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              zIndex: 9999\n            }}\n          >\n            🧪 Test Success Modal\n          </button>\n          <h1 className=\"page-title\">\n            <FaCrown className=\"title-icon\" />\n            Subscription Management\n          </h1>\n          <p className=\"page-subtitle\">Manage your subscription and access premium features</p>\n        </motion.div>\n\n        {/* Current Subscription Status */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"current-subscription\"\n        >\n          <h2 className=\"section-title\">Current Subscription</h2>\n          \n          {subscriptionStatus === 'active' && (\n            <div className=\"subscription-card active\">\n              <div className=\"subscription-status\">\n                <FaCheckCircle className=\"status-icon active\" />\n                <span className=\"status-text\">Active Subscription</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCrown className=\"detail-icon\" />\n                  <span>Plan: {subscriptionData?.activePlan?.title || 'Premium Plan'}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expires: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCheckCircle className=\"detail-icon\" />\n                  <span>Days Remaining: {getDaysRemaining()}</span>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'expired' && (\n            <div className=\"subscription-card expired\">\n              <div className=\"subscription-status\">\n                <FaTimesCircle className=\"status-icon expired\" />\n                <span className=\"status-text\">Subscription Expired</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expired: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <p className=\"renewal-message\">\n                  Your subscription has expired. Choose a new plan below to continue accessing premium features.\n                </p>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'none' && (\n            <div className=\"subscription-card none\">\n              <div className=\"subscription-status\">\n                <FaUser className=\"status-icon none\" />\n                <span className=\"status-text\">Free Account</span>\n              </div>\n              <div className=\"subscription-details\">\n                <p className=\"upgrade-message\">\n                  You're currently using a free account. Upgrade to a premium plan to unlock all features.\n                </p>\n              </div>\n            </div>\n          )}\n        </motion.div>\n\n        {/* Available Plans */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"available-plans\"\n        >\n          <h2 className=\"section-title\">\n            {subscriptionStatus === 'active'\n              ? '🚀 Upgrade Your Plan'\n              : subscriptionStatus === 'expired'\n                ? '🔄 Renew Your Subscription'\n                : '🎯 Choose Your Plan'\n            }\n          </h2>\n\n          {/* Temporary Test Buttons */}\n          <div style={{ display: 'flex', gap: '10px', justifyContent: 'center', marginBottom: '20px' }}>\n            <button\n              onClick={testProcessingModal}\n              style={{\n                background: '#ff6b6b',\n                color: 'white',\n                border: 'none',\n                padding: '8px 16px',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              🧪 Test Processing Modal\n            </button>\n            <button\n              onClick={testSuccessModal}\n              style={{\n                background: '#51cf66',\n                color: 'white',\n                border: 'none',\n                padding: '8px 16px',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              🧪 Test Success Modal\n            </button>\n          </div>\n          <p className=\"section-subtitle\">\n            {subscriptionStatus === 'active'\n              ? 'Upgrade to a longer plan for better value and extended access'\n              : subscriptionStatus === 'expired'\n                ? 'Your subscription has expired. Renew now to continue accessing premium features'\n                : 'Select a subscription plan to unlock all premium features and start your learning journey'\n            }\n          </p>\n          \n          {loading ? (\n            <div className=\"loading-state\">\n              <div className=\"spinner\"></div>\n              <p>Loading plans...</p>\n            </div>\n          ) : plans.length === 0 ? (\n            <div className=\"no-plans-state\">\n              <div className=\"no-plans-icon\">📋</div>\n              <h3>No Plans Available</h3>\n              <p>Plans are currently being loaded. Please refresh the page or try again later.</p>\n              <button className=\"refresh-btn\" onClick={fetchPlans}>\n                🔄 Refresh Plans\n              </button>\n            </div>\n          ) : (\n            <div className=\"plans-grid\">\n              {plans.map((plan) => (\n                <motion.div\n                  key={plan._id}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  className=\"plan-card\"\n                >\n                  <div className=\"plan-header\">\n                    <h3 className=\"plan-title\">{plan.title}</h3>\n                    {plan.title?.toLowerCase().includes('standard') && (\n                      <span className=\"plan-badge\">🔥 Popular</span>\n                    )}\n                  </div>\n                  \n                  <div className=\"plan-pricing\">\n                    <div className=\"price-display\">\n                      <div className=\"current-price\">\n                        <span className=\"currency\">TZS</span>\n                        {plan.discountedPrice?.toLocaleString()}\n                      </div>\n                      {plan.actualPrice > plan.discountedPrice && (\n                        <>\n                          <span className=\"original-price\">{plan.actualPrice?.toLocaleString()} TZS</span>\n                          <span className=\"discount-badge\">\n                            {Math.round(((plan.actualPrice - plan.discountedPrice) / plan.actualPrice) * 100)}% OFF\n                          </span>\n                        </>\n                      )}\n                    </div>\n                    <div className=\"plan-duration\">\n                      <span className=\"duration-highlight\">{plan.duration}</span> month{plan.duration > 1 ? 's' : ''} access\n                    </div>\n                  </div>\n\n                  <div className=\"plan-features\">\n                    {plan.features?.slice(0, 5).map((feature, index) => (\n                      <div key={index} className=\"feature-item\">\n                        <FaCheckCircle className=\"feature-icon\" />\n                        <span>{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n\n                  <button\n                    className=\"select-plan-btn\"\n                    onClick={() => handlePlanSelect(plan)}\n                    disabled={paymentLoading === plan._id}\n                    style={{\n                      background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '12px',\n                      padding: '1rem 1.5rem',\n                      fontSize: '1rem',\n                      fontWeight: '600',\n                      cursor: paymentLoading === plan._id ? 'not-allowed' : 'pointer',\n                      transition: 'all 0.3s ease',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      gap: '0.5rem',\n                      width: '100%',\n                      opacity: paymentLoading === plan._id ? 0.6 : 1\n                    }}\n                    onMouseEnter={(e) => {\n                      if (paymentLoading !== plan._id) {\n                        e.target.style.background = 'linear-gradient(135deg, #1d4ed8, #1e40af)';\n                        e.target.style.transform = 'translateY(-2px)';\n                        e.target.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.4)';\n                      }\n                    }}\n                    onMouseLeave={(e) => {\n                      if (paymentLoading !== plan._id) {\n                        e.target.style.background = 'linear-gradient(135deg, #3b82f6, #1d4ed8)';\n                        e.target.style.transform = 'translateY(0)';\n                        e.target.style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.3)';\n                      }\n                    }}\n                  >\n                    <FaCreditCard className=\"btn-icon\" />\n                    {paymentLoading === plan._id\n                      ? 'Processing...'\n                      : subscriptionStatus === 'active'\n                        ? 'Click to Upgrade'\n                        : subscriptionStatus === 'expired'\n                          ? 'Click to Renew'\n                          : 'Click to Pay'\n                    }\n                  </button>\n                </motion.div>\n              ))}\n            </div>\n          )}\n        </motion.div>\n\n        {/* Phone Number Warning */}\n        {(!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            className=\"phone-warning\"\n          >\n            <div className=\"warning-content\">\n              <FaTimesCircle className=\"warning-icon\" />\n              <div>\n                <h4>Phone Number Required</h4>\n                <p>Please update your phone number in your profile to subscribe to a plan.</p>\n                <button \n                  className=\"update-phone-btn\"\n                  onClick={() => window.location.href = '/profile'}\n                >\n                  Update Phone Number\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Best Design Payment Processing Modal */}\n        {showProcessingModal && (\n          <div\n            className=\"modal-overlay-best\"\n            style={{\n              position: 'fixed',\n              top: '0',\n              left: '0',\n              width: '100vw',\n              height: '100vh',\n              background: 'rgba(0, 0, 0, 0.85)',\n              backdropFilter: 'blur(15px)',\n              WebkitBackdropFilter: 'blur(15px)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              zIndex: '10000',\n              padding: '20px',\n              boxSizing: 'border-box'\n            }}\n            onClick={(e) => e.target === e.currentTarget && handleCloseProcessingModal()}\n          >\n            <div\n              className=\"modal-container-best\"\n              style={{\n                background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',\n                borderRadius: '20px',\n                boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1)',\n                border: '1px solid rgba(59, 130, 246, 0.1)',\n                width: '100%',\n                maxWidth: '480px',\n                maxHeight: '90vh',\n                display: 'flex',\n                flexDirection: 'column',\n                overflow: 'hidden',\n                position: 'relative',\n                transform: 'translateZ(0)'\n              }}\n              onClick={(e) => e.stopPropagation()}\n            >\n              {/* Professional Header */}\n              <div style={{\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                padding: '1.5rem',\n                color: 'white',\n                textAlign: 'center',\n                position: 'relative'\n              }}>\n                <button\n                  className=\"payment-modal-close\"\n                  onClick={handleCloseProcessingModal}\n                  title=\"Close payment window\"\n                  style={{\n                    position: 'absolute',\n                    top: '12px',\n                    right: '12px',\n                    background: 'rgba(255, 255, 255, 0.2)',\n                    border: 'none',\n                    borderRadius: '50%',\n                    width: '32px',\n                    height: '32px',\n                    color: 'white',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    transition: 'all 0.3s ease',\n                    zIndex: 10\n                  }}\n                  onMouseEnter={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.3)'}\n                  onMouseLeave={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.2)'}\n                >\n                  ✕\n                </button>\n\n                <div style={{ marginBottom: '0.5rem' }}>\n                  <div className=\"payment-processing-animation\" style={{\n                    width: '60px',\n                    height: '60px',\n                    margin: '0 auto 1rem',\n                    position: 'relative'\n                  }}>\n                    <div className=\"payment-spinner\" style={{\n                      width: '100%',\n                      height: '100%',\n                      border: '3px solid rgba(255, 255, 255, 0.3)',\n                      borderTop: '3px solid white',\n                      borderRadius: '50%',\n                      animation: 'spin 1s linear infinite'\n                    }}></div>\n                    <div style={{\n                      position: 'absolute',\n                      top: '50%',\n                      left: '50%',\n                      transform: 'translate(-50%, -50%)',\n                      fontSize: '20px'\n                    }}>💳</div>\n                  </div>\n                </div>\n\n                <h3 style={{\n                  margin: '0',\n                  fontSize: '1.4rem',\n                  fontWeight: '700',\n                  textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'\n                }}>\n                  Processing Payment\n                </h3>\n                <p style={{\n                  margin: '0.5rem 0 0 0',\n                  fontSize: '0.9rem',\n                  opacity: '0.9'\n                }}>\n                  Secure transaction in progress\n                </p>\n              </div>\n\n              {/* Scrollable Content Area */}\n              <div style={{\n                flex: '1',\n                overflowY: 'auto',\n                overflowX: 'hidden',\n                padding: '0'\n              }}>\n                <div style={{\n                  padding: '20px',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: '16px',\n                  minHeight: '100%'\n                }}>\n                {/* Payment Status */}\n                <div style={{\n                  background: 'rgba(59, 130, 246, 0.05)',\n                  border: '1px solid rgba(59, 130, 246, 0.2)',\n                  borderRadius: '10px',\n                  padding: '0.75rem',\n                  textAlign: 'center'\n                }}>\n                  <p style={{\n                    margin: '0',\n                    color: '#1e40af',\n                    fontSize: '0.95rem',\n                    fontWeight: '500'\n                  }}>\n                    {paymentStatus}\n                  </p>\n                </div>\n\n                {/* Plan Details */}\n                <div style={{\n                  background: 'rgba(16, 185, 129, 0.05)',\n                  border: '1px solid rgba(16, 185, 129, 0.2)',\n                  borderRadius: '10px',\n                  padding: '0.75rem'\n                }}>\n                  <h4 style={{\n                    margin: '0 0 0.5rem 0',\n                    color: '#065f46',\n                    fontSize: '1.1rem',\n                    fontWeight: '600'\n                  }}>\n                    {selectedPlan?.title}\n                  </h4>\n                  <div style={{\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    fontSize: '0.9rem',\n                    color: '#047857'\n                  }}>\n                    <span>Amount: <strong>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</strong></span>\n                    <span>Duration: <strong>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</strong></span>\n                  </div>\n                </div>\n\n                {/* Phone Instructions */}\n                <div style={{\n                  background: 'rgba(245, 158, 11, 0.05)',\n                  border: '1px solid rgba(245, 158, 11, 0.2)',\n                  borderRadius: '10px',\n                  padding: '0.75rem'\n                }}>\n                  <h4 style={{\n                    margin: '0 0 0.75rem 0',\n                    color: '#92400e',\n                    fontSize: '1rem',\n                    fontWeight: '600',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  }}>\n                    📱 Check Your Phone\n                  </h4>\n                  <p style={{\n                    margin: '0 0 0.75rem 0',\n                    color: '#b45309',\n                    fontSize: '0.9rem',\n                    fontWeight: '500',\n                    background: 'rgba(245, 158, 11, 0.1)',\n                    padding: '0.5rem',\n                    borderRadius: '8px',\n                    textAlign: 'center'\n                  }}>\n                    Number: {user?.phoneNumber}\n                  </p>\n                  <div style={{\n                    color: '#a16207',\n                    fontSize: '0.85rem',\n                    lineHeight: '1.5'\n                  }}>\n                    <div style={{ marginBottom: '0.25rem' }}>1. You will receive an SMS with payment instructions</div>\n                    <div style={{ marginBottom: '0.25rem' }}>2. Follow the SMS steps to confirm payment</div>\n                    <div>3. Complete the mobile money transaction</div>\n                  </div>\n                </div>\n\n                  {/* Try Again Section - Always Visible */}\n                  {showTryAgain && (\n                    <div style={{\n                      background: 'rgba(239, 68, 68, 0.05)',\n                      border: '1px solid rgba(239, 68, 68, 0.2)',\n                      borderRadius: '12px',\n                      padding: '16px',\n                      textAlign: 'center',\n                      marginTop: '8px'\n                    }}>\n                    <p style={{\n                      margin: '0 0 0.5rem 0',\n                      color: '#dc2626',\n                      fontSize: '0.9rem',\n                      fontWeight: '600'\n                    }}>\n                      ⚠️ Taking longer than expected?\n                    </p>\n                    <p style={{\n                      margin: '0 0 1rem 0',\n                      color: '#b91c1c',\n                      fontSize: '0.8rem'\n                    }}>\n                      If you haven't received SMS or facing connection issues:\n                    </p>\n                    <button\n                      className=\"try-again-button\"\n                      onClick={handleTryAgain}\n                      style={{\n                        background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                        color: 'white',\n                        border: 'none',\n                        padding: '0.75rem 1.5rem',\n                        borderRadius: '10px',\n                        fontSize: '0.9rem',\n                        fontWeight: '600',\n                        cursor: 'pointer',\n                        transition: 'all 0.3s ease',\n                        boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',\n                        width: '100%',\n                        maxWidth: '200px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        gap: '0.5rem',\n                        margin: '0 auto'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.target.style.background = 'linear-gradient(135deg, #1d4ed8 0%, #1e3a8a 100%)';\n                        e.target.style.transform = 'translateY(-2px)';\n                        e.target.style.boxShadow = '0 6px 16px rgba(59, 130, 246, 0.4)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.target.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';\n                        e.target.style.transform = 'translateY(0)';\n                        e.target.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.3)';\n                      }}\n                    >\n                      🔄 Try Again\n                    </button>\n                  </div>\n                )}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Best Design Success Modal */}\n        {showSuccessModal && (\n          <div\n            className=\"modal-overlay-best\"\n            style={{\n              position: 'fixed',\n              top: '0',\n              left: '0',\n              width: '100vw',\n              height: '100vh',\n              background: 'rgba(0, 0, 0, 0.9)',\n              backdropFilter: 'blur(20px)',\n              WebkitBackdropFilter: 'blur(20px)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              zIndex: '10000',\n              padding: '20px',\n              boxSizing: 'border-box'\n            }}\n            onClick={(e) => {\n              if (e.target === e.currentTarget) {\n                setAutoNavigateCountdown(null); // Clear countdown\n                setShowSuccessModal(false);\n              }\n            }}\n          >\n            <div\n              className=\"modal-container-best success-modal-best\"\n              style={{\n                background: 'linear-gradient(145deg, #ffffff 0%, #f0fdf4 100%)',\n                borderRadius: '24px',\n                boxShadow: '0 25px 80px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(34, 197, 94, 0.2)',\n                border: '2px solid rgba(34, 197, 94, 0.1)',\n                width: '100%',\n                maxWidth: '600px',\n                maxHeight: '95vh',\n                display: 'flex',\n                flexDirection: 'column',\n                overflow: 'hidden',\n                position: 'relative',\n                transform: 'translateZ(0)'\n              }}\n              onClick={(e) => e.stopPropagation()}\n            >\n              {/* Professional Success Header */}\n              <div style={{\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                padding: '2rem',\n                color: 'white',\n                textAlign: 'center',\n                position: 'relative',\n                borderRadius: '28px 28px 0 0',\n                flexShrink: 0\n              }}>\n                <button\n                  onClick={() => {\n                    setAutoNavigateCountdown(null); // Clear countdown\n                    setShowSuccessModal(false);\n                  }}\n                  style={{\n                    position: 'absolute',\n                    top: '16px',\n                    right: '16px',\n                    background: 'rgba(255, 255, 255, 0.2)',\n                    border: 'none',\n                    borderRadius: '50%',\n                    width: '36px',\n                    height: '36px',\n                    color: 'white',\n                    fontSize: '18px',\n                    cursor: 'pointer',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    transition: 'all 0.3s ease',\n                    zIndex: 10\n                  }}\n                  onMouseEnter={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.3)'}\n                  onMouseLeave={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.2)'}\n                >\n                  ✕\n                </button>\n\n                <div style={{\n                  fontSize: '4rem',\n                  marginBottom: '1rem',\n                  animation: 'bounce 2s infinite'\n                }}>\n                  🎉\n                </div>\n\n                <h2 style={{\n                  margin: '0',\n                  fontSize: '2rem',\n                  fontWeight: '800',\n                  textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',\n                  marginBottom: '0.5rem'\n                }}>\n                  Payment Successful!\n                </h2>\n\n                <p style={{\n                  margin: '0',\n                  fontSize: '1.1rem',\n                  opacity: '0.95',\n                  fontWeight: '500'\n                }}>\n                  Welcome to {selectedPlan?.title}! 🚀\n                </p>\n              </div>\n\n              {/* Scrollable Success Content */}\n              <div style={{\n                flex: '1',\n                overflowY: 'auto',\n                overflowX: 'hidden',\n                padding: '0'\n              }}>\n                <div style={{\n                  padding: '24px',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: '20px',\n                  minHeight: '100%'\n                }}>\n                {/* Success Status Card */}\n                <div style={{\n                  background: 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)',\n                  border: '2px solid #22c55e',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  textAlign: 'center',\n                  boxShadow: '0 4px 20px rgba(34, 197, 94, 0.2)'\n                }}>\n                  <div style={{\n                    background: 'rgba(34, 197, 94, 0.1)',\n                    borderRadius: '12px',\n                    padding: '1rem',\n                    marginBottom: '1rem'\n                  }}>\n                    <h3 style={{\n                      color: '#15803d',\n                      marginBottom: '1rem',\n                      fontSize: '1.2rem',\n                      fontWeight: '700'\n                    }}>\n                      🎯 Subscription Details\n                    </h3>\n                    <div style={{\n                      display: 'grid',\n                      gap: '0.75rem',\n                      textAlign: 'left'\n                    }}>\n                      <div style={{\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.5rem',\n                        background: 'rgba(255, 255, 255, 0.7)',\n                        borderRadius: '8px'\n                      }}>\n                        <span style={{ fontWeight: '600', color: '#374151' }}>📋 Plan:</span>\n                        <span style={{ color: '#059669', fontWeight: '700' }}>{selectedPlan?.title}</span>\n                      </div>\n                      <div style={{\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.5rem',\n                        background: 'rgba(255, 255, 255, 0.7)',\n                        borderRadius: '8px'\n                      }}>\n                        <span style={{ fontWeight: '600', color: '#374151' }}>⏰ Duration:</span>\n                        <span style={{ color: '#059669', fontWeight: '700' }}>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</span>\n                      </div>\n                      <div style={{\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.5rem',\n                        background: 'rgba(255, 255, 255, 0.7)',\n                        borderRadius: '8px'\n                      }}>\n                        <span style={{ fontWeight: '600', color: '#374151' }}>💰 Amount:</span>\n                        <span style={{ color: '#059669', fontWeight: '700' }}>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</span>\n                      </div>\n                      <div style={{\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.5rem',\n                        background: 'linear-gradient(135deg, #22c55e, #16a34a)',\n                        borderRadius: '8px',\n                        color: 'white'\n                      }}>\n                        <span style={{ fontWeight: '600' }}>💎 Status:</span>\n                        <span style={{ fontWeight: '800', fontSize: '1.1rem' }}>ACTIVE ✅</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Features Unlocked Card */}\n                <div style={{\n                  background: 'linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%)',\n                  border: '2px solid #f59e0b',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  boxShadow: '0 4px 20px rgba(245, 158, 11, 0.2)'\n                }}>\n                  <h3 style={{\n                    color: '#92400e',\n                    marginBottom: '1rem',\n                    textAlign: 'center',\n                    fontSize: '1.2rem',\n                    fontWeight: '700'\n                  }}>\n                    🚀 All Premium Features Unlocked!\n                  </h3>\n                  <div style={{\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                    gap: '1rem'\n                  }}>\n                    <div style={{\n                      background: 'rgba(255, 255, 255, 0.8)',\n                      borderRadius: '12px',\n                      padding: '1rem'\n                    }}>\n                      <div style={{ marginBottom: '0.5rem', fontSize: '0.95rem', fontWeight: '600', color: '#374151' }}>\n                        <div style={{ marginBottom: '0.25rem' }}>✅ <strong>Unlimited Quizzes</strong></div>\n                        <div style={{ marginBottom: '0.25rem' }}>🤖 <strong>AI Assistant</strong></div>\n                        <div>📚 <strong>Study Materials</strong></div>\n                      </div>\n                    </div>\n                    <div style={{\n                      background: 'rgba(255, 255, 255, 0.8)',\n                      borderRadius: '12px',\n                      padding: '1rem'\n                    }}>\n                      <div style={{ marginBottom: '0.5rem', fontSize: '0.95rem', fontWeight: '600', color: '#374151' }}>\n                        <div style={{ marginBottom: '0.25rem' }}>📊 <strong>Progress Tracking</strong></div>\n                        <div style={{ marginBottom: '0.25rem' }}>🎥 <strong>Learning Videos</strong></div>\n                        <div>💬 <strong>Forum Access</strong></div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Auto-Navigation Notice */}\n                {autoNavigateCountdown && (\n                  <div style={{\n                    background: 'linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%)',\n                    border: '2px solid #0288d1',\n                    borderRadius: '12px',\n                    padding: '1rem',\n                    textAlign: 'center',\n                    marginTop: '1rem'\n                  }}>\n                    <p style={{\n                      margin: '0',\n                      color: '#01579b',\n                      fontSize: '0.9rem',\n                      fontWeight: '600'\n                    }}>\n                      🚀 Automatically redirecting to Hub in {autoNavigateCountdown} seconds...\n                    </p>\n                  </div>\n                )}\n\n                {/* Action Buttons */}\n                <div style={{\n                  display: 'flex',\n                  gap: '1rem',\n                  justifyContent: 'center',\n                  flexWrap: 'wrap',\n                  marginTop: 'auto',\n                  paddingTop: '1rem'\n                }}>\n                  <button\n                    onClick={() => {\n                      setAutoNavigateCountdown(null); // Clear countdown\n                      setShowSuccessModal(false);\n                      window.location.href = '/user/hub';\n                    }}\n                    style={{\n                      background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                      border: 'none',\n                      color: 'white',\n                      padding: '1rem 2rem',\n                      borderRadius: '12px',\n                      fontSize: '1rem',\n                      fontWeight: '700',\n                      cursor: 'pointer',\n                      transition: 'all 0.3s ease',\n                      boxShadow: '0 4px 20px rgba(59, 130, 246, 0.3)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      minWidth: '180px',\n                      justifyContent: 'center'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.target.style.background = 'linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%)';\n                      e.target.style.transform = 'translateY(-2px)';\n                      e.target.style.boxShadow = '0 6px 25px rgba(59, 130, 246, 0.4)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.target.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';\n                      e.target.style.transform = 'translateY(0)';\n                      e.target.style.boxShadow = '0 4px 20px rgba(59, 130, 246, 0.3)';\n                    }}\n                  >\n                    🏠 Continue to Hub {autoNavigateCountdown ? `(${autoNavigateCountdown}s)` : ''}\n                  </button>\n                  <button\n                    onClick={() => {\n                      setAutoNavigateCountdown(null); // Clear countdown\n                      setShowSuccessModal(false);\n                    }}\n                    style={{\n                      background: 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)',\n                      border: '2px solid #d1d5db',\n                      color: '#374151',\n                      padding: '1rem 2rem',\n                      borderRadius: '12px',\n                      fontSize: '1rem',\n                      fontWeight: '600',\n                      cursor: 'pointer',\n                      transition: 'all 0.3s ease',\n                      boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      minWidth: '120px',\n                      justifyContent: 'center'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.target.style.background = 'linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%)';\n                      e.target.style.transform = 'translateY(-1px)';\n                      e.target.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.15)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.target.style.background = 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)';\n                      e.target.style.transform = 'translateY(0)';\n                      e.target.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';\n                    }}\n                  >\n                    ✨ Close\n                  </button>\n                </div>\n\n                {/* Congratulations Message */}\n                <div style={{\n                  marginTop: '1.5rem',\n                  padding: '1rem',\n                  background: 'rgba(34, 197, 94, 0.1)',\n                  borderRadius: '12px',\n                  textAlign: 'center',\n                  border: '1px solid rgba(34, 197, 94, 0.2)'\n                }}>\n                  <p style={{\n                    margin: '0',\n                    fontSize: '1rem',\n                    color: '#15803d',\n                    fontWeight: '600',\n                    lineHeight: '1.5'\n                  }}>\n                    🎉 Congratulations! You now have full access to all BrainWave features. Start exploring and excel in your studies! 🚀\n                  </p>\n                </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Upgrade Restriction Modal */}\n        <UpgradeRestrictionModal\n          visible={showUpgradeRestriction}\n          onClose={() => setShowUpgradeRestriction(false)}\n          currentPlan={plans.find(p => p._id === subscriptionData?.activePlan) || subscriptionData?.plan}\n          subscription={subscriptionData}\n          user={user}\n        />\n\n        {/* Subscription Expired Modal */}\n        <SubscriptionExpiredModal\n          visible={showExpiredModal}\n          onClose={() => setShowExpiredModal(false)}\n          onRenew={handleRenewSubscription}\n          subscription={subscriptionData}\n          user={user}\n          plans={plans}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default Subscription;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,OAAO,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,EAAEC,YAAY,EAAEC,MAAM,QAAQ,gBAAgB;AAC3G,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,2BAA2B;AAC1E,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,uBAAuB,MAAM,qEAAqE;AACzG,OAAOC,wBAAwB,MAAM,uEAAuE;AAC5G,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACzB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACkC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACAC,SAAS,CAAC,MAAM;IACdmC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEH,mBAAmB,CAAC;EAC9E,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;EACzB,MAAM,CAACI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4C,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoD,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM;IAAEsD;EAAK,CAAC,GAAGpD,WAAW,CAAEqD,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE;EAAiB,CAAC,GAAGtD,WAAW,CAAEqD,KAAK,IAAKA,KAAK,CAACE,YAAY,CAAC;EACvE,MAAMC,QAAQ,GAAGvD,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMwD,WAAW,GAAG,CAClB;IACEC,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,kBAAkB;IACzBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,CAC/B;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,EACD;IACEP,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,EAC9B,kBAAkB,CACnB;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,CACF;EAEDlE,SAAS,CAAC,MAAM;IACdmE,UAAU,CAAC,CAAC;IACZC,wBAAwB,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApE,SAAS,CAAC,MAAM;IACd,IAAIiC,mBAAmB,IAAII,gBAAgB,EAAE;MAC3C;MACA,MAAMgC,OAAO,GAAGC,MAAM,CAACD,OAAO;MAC9BE,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;MACvCH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACE,QAAQ,GAAG,OAAO;MACtCJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACG,KAAK,GAAG,MAAM;MAClCL,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACI,GAAG,GAAI,IAAGR,OAAQ,IAAG;IAC3C,CAAC,MAAM;MACL;MACA,MAAMA,OAAO,GAAGE,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACI,GAAG;MACvCN,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;MACjCH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACE,QAAQ,GAAG,EAAE;MACjCJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACG,KAAK,GAAG,EAAE;MAC9BL,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACI,GAAG,GAAG,EAAE;MAC5B,IAAIR,OAAO,EAAE;QACXC,MAAM,CAACQ,QAAQ,CAAC,CAAC,EAAEC,QAAQ,CAACV,OAAO,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACnD;IACF;;IAEA;IACA,OAAO,MAAM;MACXE,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;MACjCH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACE,QAAQ,GAAG,EAAE;MACjCJ,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACG,KAAK,GAAG,EAAE;MAC9BL,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACI,GAAG,GAAG,EAAE;IAC9B,CAAC;EACH,CAAC,EAAE,CAAC5C,mBAAmB,EAAEI,gBAAgB,CAAC,CAAC;;EAE3C;EACArC,SAAS,CAAC,MAAM;IACd,IAAIuD,gBAAgB,IAAIyB,qBAAqB,CAAC,CAAC,EAAE;MAC/C7C,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrDU,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM;MACLA,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC,EAAE,CAACS,gBAAgB,CAAC,CAAC;EAEtB,MAAMY,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFrC,UAAU,CAAC,IAAI,CAAC;MAChBK,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,MAAM6C,QAAQ,GAAG,MAAMtE,QAAQ,CAAC,CAAC;MACjCwB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE6C,QAAQ,CAAC;MAExC,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACjExD,QAAQ,CAACqD,QAAQ,CAACE,IAAI,CAAC;QACvBhD,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE6C,QAAQ,CAACE,IAAI,CAAC;MACnE,CAAC,MAAM,IAAIE,KAAK,CAACC,OAAO,CAACL,QAAQ,CAAC,IAAIA,QAAQ,CAACG,MAAM,GAAG,CAAC,EAAE;QACzD;QACAxD,QAAQ,CAACqD,QAAQ,CAAC;QAClB9C,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE6C,QAAQ,CAAC;MAC1D,CAAC,MAAM;QACL9C,OAAO,CAACoD,IAAI,CAAC,uCAAuC,CAAC;QACrD3D,QAAQ,CAAC8B,WAAW,CAAC;QACrBtD,OAAO,CAACoF,IAAI,CAAC,qDAAqD,CAAC;MACrE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdtD,OAAO,CAACsD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDtD,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1CR,QAAQ,CAAC8B,WAAW,CAAC;MACrBtD,OAAO,CAACsF,OAAO,CAAC,iEAAiE,CAAC;IACpF,CAAC,SAAS;MACR5D,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsC,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACF,MAAMa,QAAQ,GAAG,MAAMpE,kBAAkB,CAAC,CAAC;MAC3CsB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE6C,QAAQ,CAAC;IAChD,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdtD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC7C;EACF,CAAC;;EAED;EACA,MAAM4C,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAACzB,gBAAgB,EAAE,OAAO,IAAI;;IAElC;IACA,IAAI,CAACA,gBAAgB,CAACoC,OAAO,EAAE,OAAO,IAAI;;IAE1C;IACA,IAAIpC,gBAAgB,CAACd,aAAa,KAAK,MAAM,EAAE,OAAO,IAAI;;IAE1D;IACA,IAAIc,gBAAgB,CAACW,MAAM,KAAK,QAAQ,EAAE,OAAO,IAAI;;IAErD;IACA,MAAMyB,OAAO,GAAG,IAAIC,IAAI,CAACrC,gBAAgB,CAACoC,OAAO,CAAC;IAClD,MAAME,KAAK,GAAG,IAAID,IAAI,CAAC,CAAC;IACxBC,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5BH,OAAO,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAE9B,OAAOH,OAAO,GAAGE,KAAK;EACxB,CAAC;;EAED;EACA,MAAME,uBAAuB,GAAG,MAAOxD,YAAY,IAAK;IACtDO,mBAAmB,CAAC,KAAK,CAAC;IAC1B,MAAMkD,gBAAgB,CAACzD,YAAY,CAAC;EACtC,CAAC;;EAED;EACA,MAAM0D,0BAA0B,GAAGA,CAAA,KAAM;IACvC/D,sBAAsB,CAAC,KAAK,CAAC;IAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IACzBkB,eAAe,CAAC,KAAK,CAAC;IACtBF,sBAAsB,CAAC,IAAI,CAAC;IAC5BN,gBAAgB,CAAC,EAAE,CAAC;IACpBtC,OAAO,CAACoF,IAAI,CAAC,uDAAuD,CAAC;EACvE,CAAC;;EAED;EACA,MAAMU,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI3D,YAAY,EAAE;MAChBW,eAAe,CAAC,KAAK,CAAC;MACtBF,sBAAsB,CAAC,IAAI,CAAC;MAC5BgD,gBAAgB,CAACzD,YAAY,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAM4D,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhE,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC1CF,sBAAsB,CAAC,KAAK,CAAC;IAC7BI,mBAAmB,CAAC,IAAI,CAAC;IACzBN,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMoE,mBAAmB,GAAGA,CAAA,KAAM;IAChCjE,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7CF,sBAAsB,CAAC,IAAI,CAAC;IAC5BQ,gBAAgB,CAAC,6BAA6B,CAAC;IAC/CF,eAAe,CAACb,KAAK,CAAC,CAAC,CAAC,IAAI;MAAEiC,KAAK,EAAE,WAAW;MAAEG,eAAe,EAAE,IAAI;MAAEE,QAAQ,EAAE;IAAE,CAAC,CAAC;EACzF,CAAC;EAED,MAAM+B,gBAAgB,GAAG,MAAOK,IAAI,IAAK;IACvC;IACA,IAAI9C,gBAAgB,IAAIA,gBAAgB,CAACW,MAAM,KAAK,QAAQ,IAAIX,gBAAgB,CAACd,aAAa,KAAK,MAAM,EAAE;MACzGN,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEmB,gBAAgB,CAAC;MACzEX,yBAAyB,CAAC,IAAI,CAAC;MAC/B;IACF;IAEA,IAAI,CAACS,IAAI,CAACiD,WAAW,IAAI,CAAC,gBAAgB,CAACC,IAAI,CAAClD,IAAI,CAACiD,WAAW,CAAC,EAAE;MACjElG,OAAO,CAACqF,KAAK,CAAC,oEAAoE,CAAC;MACnF;IACF;IAEA,IAAI;MAAA,IAAAe,UAAA;MACFrE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEiE,IAAI,CAACzC,KAAK,CAAC;MACxDzB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;;MAEzD;MACAI,eAAe,CAAC6D,IAAI,CAAC;MACrBrE,iBAAiB,CAACqE,IAAI,CAAC1C,GAAG,CAAC;MAC3BzB,sBAAsB,CAAC,IAAI,CAAC;MAC5BgB,eAAe,CAAC,KAAK,CAAC;MACtBF,sBAAsB,CAAC4C,IAAI,CAACa,GAAG,CAAC,CAAC,CAAC;MAClC/D,gBAAgB,CAAC,sCAAsC,CAAC;MAExDP,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;MAEvD;MACA,MAAM,IAAIsE,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;;MAEtD;MACA,MAAME,aAAa,GAAGD,UAAU,CAAC,MAAM;QACrC1D,eAAe,CAAC,IAAI,CAAC;MACvB,CAAC,EAAE,KAAK,CAAC;MAET,MAAM4D,WAAW,GAAG;QAClBT,IAAI,EAAEA,IAAI;QACVU,MAAM,EAAE1D,IAAI,CAACM,GAAG;QAChBqD,SAAS,EAAE3D,IAAI,CAACiD,WAAW;QAC3BW,SAAS,EAAE5D,IAAI,CAAC6D,KAAK,IAAK,IAAAV,UAAA,GAAEnD,IAAI,CAAC8D,IAAI,cAAAX,UAAA,uBAATA,UAAA,CAAWY,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC3E,CAAC;MAED3E,gBAAgB,CAAC,0CAA0C,CAAC;MAC5D,MAAMuC,QAAQ,GAAG,MAAMrE,UAAU,CAACkG,WAAW,CAAC;MAE9C,IAAI7B,QAAQ,CAACC,OAAO,EAAE;QAAA,IAAAoC,cAAA;QACpB5E,gBAAgB,CAAC,wDAAwD,CAAC;QAE1EP,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE6C,QAAQ,CAAC;QAC7C9C,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE6C,QAAQ,CAACsC,QAAQ,CAAC;;QAE9C;QACAnH,OAAO,CAAC8E,OAAO,CAAC;UACdsC,OAAO,EAAG,8CAA6CnE,IAAI,CAACiD,WAAY,sCAAqC;UAC7GrC,QAAQ,EAAE,CAAC;UACXQ,KAAK,EAAE;YACLgD,SAAS,EAAE;UACb;QACF,CAAC,CAAC;;QAEF;QACA,MAAMC,cAAc,GAAGzC,QAAQ,CAACsC,QAAQ,MAAAD,cAAA,GAAIrC,QAAQ,CAACE,IAAI,cAAAmC,cAAA,uBAAbA,cAAA,CAAeC,QAAQ,KAAI,YAAY;QACnFpF,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEsF,cAAc,CAAC;QAEhFC,wBAAwB,CAACD,cAAc,CAAC;MAE1C,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAAC3C,QAAQ,CAAC7E,OAAO,IAAI,gBAAgB,CAAC;MACvD;IACF,CAAC,CAAC,OAAOqF,KAAK,EAAE;MACdtD,OAAO,CAACsD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzCvD,sBAAsB,CAAC,KAAK,CAAC;MAC7B9B,OAAO,CAACqF,KAAK,CAAC,kBAAkB,GAAGA,KAAK,CAACrF,OAAO,CAAC;MACjD4B,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3B;EACF,CAAC;;EAED,MAAM2F,wBAAwB,GAAG,MAAOE,OAAO,IAAK;IAClD1F,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEyF,OAAO,CAAC;IACzE,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,sBAAsB;IAE1B,IAAI;MACFrF,gBAAgB,CAAC,0EAA0E,CAAC;;MAE5F;MACA,IAAIsF,QAAQ,GAAG,CAAC;MAChB,MAAMC,WAAW,GAAG,GAAG,CAAC,CAAC;;MAEzB,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;QACpCF,QAAQ,EAAE;QACV7F,OAAO,CAACC,GAAG,CAAE,mCAAkC4F,QAAS,IAAGC,WAAY,aAAY,EAAEJ,OAAO,CAAC;QAE7F,IAAI;UACF,MAAMM,cAAc,GAAG,MAAMtH,kBAAkB,CAAC;YAAEgH;UAAQ,CAAC,CAAC;UAC5D1F,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE+F,cAAc,CAAC;UAC1DhG,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9CD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,CAAA+F,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE1F,aAAa,MAAK,MAAM,IAAI,CAAA0F,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEjE,MAAM,MAAK,QAAQ,CAAC;UACjH/B,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,CAAA+F,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEjE,MAAM,MAAK,WAAW,IAAI,CAAAiE,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEjD,OAAO,MAAK,IAAI,CAAC;UAE5G,IAAIiD,cAAc,KACfA,cAAc,CAAC1F,aAAa,KAAK,MAAM,IAAI0F,cAAc,CAACjE,MAAM,KAAK,QAAQ,IAC7EiE,cAAc,CAACjE,MAAM,KAAK,WAAW,IAAIiE,cAAc,CAACjD,OAAO,KAAK,IAAK,CAC3E,EAAE;YACD;YACA4C,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1BxD,QAAQ,CAAC6D,mBAAmB,CAAC,kBAAkB,EAAEL,sBAAsB,CAAC,CAAC,CAAC;YAC5E;;YAEArF,gBAAgB,CAAC,uDAAuD,CAAC;YACzEP,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;;YAEtE;YACAD,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;YACzEF,sBAAsB,CAAC,KAAK,CAAC;YAC7BI,mBAAmB,CAAC,IAAI,CAAC;YACzBN,iBAAiB,CAAC,IAAI,CAAC;YACvBG,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;YAEhD;YACAgC,wBAAwB,CAAC,CAAC;;YAE1B;YACAhE,OAAO,CAAC8E,OAAO,CAAC;cACdsC,OAAO,EAAE,sDAAsD;cAC/DvD,QAAQ,EAAE,CAAC;cACXQ,KAAK,EAAE;gBACLgD,SAAS,EAAE,MAAM;gBACjBY,QAAQ,EAAE;cACZ;YACF,CAAC,CAAC;;YAEF;YACAjF,wBAAwB,CAAC,CAAC,CAAC;YAC3B,MAAMkF,iBAAiB,GAAGC,WAAW,CAAC,MAAM;cAC1CnF,wBAAwB,CAACoF,IAAI,IAAI;gBAC/B,IAAIA,IAAI,IAAI,CAAC,EAAE;kBACbC,aAAa,CAACH,iBAAiB,CAAC;kBAChCnG,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;kBACpEE,mBAAmB,CAAC,KAAK,CAAC;kBAC1BgC,MAAM,CAACoE,QAAQ,CAACC,IAAI,GAAG,WAAW;kBAClC,OAAO,IAAI;gBACb;gBACA,OAAOH,IAAI,GAAG,CAAC;cACjB,CAAC,CAAC;YACJ,CAAC,EAAE,IAAI,CAAC;UAEV,CAAC,MAAM,IAAIR,QAAQ,IAAIC,WAAW,EAAE;YAClC;YACAH,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1BxD,QAAQ,CAAC6D,mBAAmB,CAAC,kBAAkB,EAAEL,sBAAsB,CAAC,CAAC,CAAC;YAC5E;;YAEArF,gBAAgB,CAAC,8EAA8E,CAAC;YAEhGkE,UAAU,CAAC,MAAM;cACf1E,sBAAsB,CAAC,KAAK,CAAC;cAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;cACzB5B,OAAO,CAACsF,OAAO,CAAC,0GAA0G,CAAC;YAC7H,CAAC,EAAE,IAAI,CAAC;UAEV,CAAC,MAAM;YACL;YACAhD,gBAAgB,CAAC,0EAA0E,CAAC;YAC5FkE,UAAU,CAACsB,iBAAiB,EAAE,IAAI,CAAC,CAAC,CAAC;UACvC;QAEF,CAAC,CAAC,OAAOzC,KAAK,EAAE;UACdtD,OAAO,CAACsD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;UAEnD;UACA,IAAIA,KAAK,CAACrF,OAAO,IAAIqF,KAAK,CAACrF,OAAO,CAACwI,QAAQ,CAAC,KAAK,CAAC,EAAE;YAClDzG,OAAO,CAACsD,KAAK,CAAC,2CAA2C,CAAC;YAC1DqC,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1BxD,QAAQ,CAAC6D,mBAAmB,CAAC,kBAAkB,EAAEL,sBAAsB,CAAC;YAC1E;YACA7F,sBAAsB,CAAC,KAAK,CAAC;YAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;YACzB5B,OAAO,CAACqF,KAAK,CAAC,6HAA6H,CAAC;YAC5I;UACF;UAEA,IAAIA,KAAK,CAACrF,OAAO,IAAIqF,KAAK,CAACrF,OAAO,CAACwI,QAAQ,CAAC,KAAK,CAAC,EAAE;YAClDzG,OAAO,CAACsD,KAAK,CAAC,oDAAoD,CAAC;YACnEqC,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1BxD,QAAQ,CAAC6D,mBAAmB,CAAC,kBAAkB,EAAEL,sBAAsB,CAAC;YAC1E;YACA7F,sBAAsB,CAAC,KAAK,CAAC;YAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;YACzB5B,OAAO,CAACqF,KAAK,CAAC,6CAA6C,CAAC;YAC5D;UACF;UAEA,IAAIuC,QAAQ,IAAIC,WAAW,EAAE;YAC3BH,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1BxD,QAAQ,CAAC6D,mBAAmB,CAAC,kBAAkB,EAAEL,sBAAsB,CAAC,CAAC,CAAC;YAC5E;;YACA7F,sBAAsB,CAAC,KAAK,CAAC;YAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;YACzB5B,OAAO,CAACqF,KAAK,CAAC,mFAAmF,CAAC;UACpG,CAAC,MAAM;YACL;YACAmB,UAAU,CAACsB,iBAAiB,EAAE,IAAI,CAAC;UACrC;QACF;MACF,CAAC;;MAED;MACAH,sBAAsB,GAAGA,CAAA,KAAM;QAC7B,IAAI,CAACxD,QAAQ,CAACsE,MAAM,IAAIf,SAAS,EAAE;UACjC3F,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;UAC3EM,gBAAgB,CAAC,+BAA+B,CAAC;UACjD;UACAkE,UAAU,CAAC,MAAMsB,iBAAiB,CAAC,CAAC,EAAE,GAAG,CAAC;QAC5C;MACF,CAAC;MAED3D,QAAQ,CAACuE,gBAAgB,CAAC,kBAAkB,EAAEf,sBAAsB,CAAC;;MAErE;MACAnB,UAAU,CAACsB,iBAAiB,EAAE,GAAG,CAAC,CAAC,CAAC;IAEtC,CAAC,CAAC,OAAOzC,KAAK,EAAE;MACdqC,SAAS,GAAG,KAAK,CAAC,CAAC;MACnB,IAAIC,sBAAsB,EAAE;QAC1BxD,QAAQ,CAAC6D,mBAAmB,CAAC,kBAAkB,EAAEL,sBAAsB,CAAC,CAAC,CAAC;MAC5E;;MACA7F,sBAAsB,CAAC,KAAK,CAAC;MAC7B9B,OAAO,CAACqF,KAAK,CAAC,+BAA+B,GAAGA,KAAK,CAACrF,OAAO,CAAC;MAC9D4B,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3B;EACF,CAAC;;EAED,MAAM+G,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAIxF,gBAAgB,IAAIA,gBAAgB,CAACd,aAAa,KAAK,MAAM,IAAIc,gBAAgB,CAACW,MAAM,KAAK,QAAQ,EAAE;MACzG,MAAMyB,OAAO,GAAG,IAAIC,IAAI,CAACrC,gBAAgB,CAACoC,OAAO,CAAC;MAClD,MAAMc,GAAG,GAAG,IAAIb,IAAI,CAAC,CAAC;MACtB,IAAID,OAAO,GAAGc,GAAG,EAAE;QACjB,OAAO,QAAQ;MACjB;IACF;IAEA,IAAI,CAAApD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2F,kBAAkB,MAAK,SAAS,IAAKzF,gBAAgB,IAAIA,gBAAgB,CAACW,MAAM,KAAK,SAAU,EAAE;MACzG,OAAO,SAAS;IAClB;IAEA,OAAO,MAAM;EACf,CAAC;EAED,MAAM+E,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAItD,IAAI,CAACsD,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAAChG,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEoC,OAAO,GAAE,OAAO,CAAC;IACxC,MAAMA,OAAO,GAAG,IAAIC,IAAI,CAACrC,gBAAgB,CAACoC,OAAO,CAAC;IAClD,MAAMc,GAAG,GAAG,IAAIb,IAAI,CAAC,CAAC;IACtB,MAAM4D,QAAQ,GAAG7D,OAAO,GAAGc,GAAG;IAC9B,MAAMgD,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOE,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAAC;EAC9B,CAAC;EAED,MAAMT,kBAAkB,GAAGD,qBAAqB,CAAC,CAAC;EAElD,oBACE5H,OAAA;IAAK0I,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChC3I,OAAA;MAAK0I,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErC3I,OAAA,CAAChB,MAAM,CAAC4J,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEnG,QAAQ,EAAE;QAAI,CAAE;QAC9B4F,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAG/B3I,OAAA;UACEkJ,OAAO,EAAEA,CAAA,KAAM;YACblI,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;YAC1CI,eAAe,CAACb,KAAK,CAAC,CAAC,CAAC,IAAI;cAAEiC,KAAK,EAAE,WAAW;cAAEK,QAAQ,EAAE,CAAC;cAAEF,eAAe,EAAE;YAAM,CAAC,CAAC;YACxFzB,mBAAmB,CAAC,IAAI,CAAC;UAC3B,CAAE;UACFmC,KAAK,EAAE;YACLE,QAAQ,EAAE,OAAO;YACjBE,GAAG,EAAE,MAAM;YACXyF,KAAK,EAAE,MAAM;YACbC,UAAU,EAAE,SAAS;YACrBC,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE,UAAU;YACnBC,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,SAAS;YACjBC,MAAM,EAAE;UACV,CAAE;UAAAf,QAAA,EACH;QAED;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT9J,OAAA;UAAI0I,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACxB3I,OAAA,CAACd,OAAO;YAACwJ,SAAS,EAAC;UAAY;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2BAEpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL9J,OAAA;UAAG0I,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoD;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eAGb9J,OAAA,CAAChB,MAAM,CAAC4J,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEnG,QAAQ,EAAE,GAAG;UAAEiH,KAAK,EAAE;QAAI,CAAE;QAC1CrB,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBAEhC3I,OAAA;UAAI0I,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoB;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEtDjC,kBAAkB,KAAK,QAAQ,iBAC9B7H,OAAA;UAAK0I,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvC3I,OAAA;YAAK0I,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC3I,OAAA,CAACZ,aAAa;cAACsJ,SAAS,EAAC;YAAoB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChD9J,OAAA;cAAM0I,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAmB;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACN9J,OAAA;YAAK0I,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC3I,OAAA;cAAK0I,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B3I,OAAA,CAACd,OAAO;gBAACwJ,SAAS,EAAC;cAAa;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnC9J,OAAA;gBAAA2I,QAAA,GAAM,QAAM,EAAC,CAAAvG,gBAAgB,aAAhBA,gBAAgB,wBAAA/B,qBAAA,GAAhB+B,gBAAgB,CAAE4H,UAAU,cAAA3J,qBAAA,uBAA5BA,qBAAA,CAA8BoC,KAAK,KAAI,cAAc;cAAA;gBAAAkH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACN9J,OAAA;cAAK0I,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B3I,OAAA,CAACb,aAAa;gBAACuJ,SAAS,EAAC;cAAa;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzC9J,OAAA;gBAAA2I,QAAA,GAAM,WAAS,EAACb,UAAU,CAAC1F,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEoC,OAAO,CAAC;cAAA;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACN9J,OAAA;cAAK0I,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B3I,OAAA,CAACZ,aAAa;gBAACsJ,SAAS,EAAC;cAAa;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzC9J,OAAA;gBAAA2I,QAAA,GAAM,kBAAgB,EAACP,gBAAgB,CAAC,CAAC;cAAA;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAjC,kBAAkB,KAAK,SAAS,iBAC/B7H,OAAA;UAAK0I,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxC3I,OAAA;YAAK0I,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC3I,OAAA,CAACX,aAAa;cAACqJ,SAAS,EAAC;YAAqB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjD9J,OAAA;cAAM0I,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAoB;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACN9J,OAAA;YAAK0I,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC3I,OAAA;cAAK0I,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B3I,OAAA,CAACb,aAAa;gBAACuJ,SAAS,EAAC;cAAa;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzC9J,OAAA;gBAAA2I,QAAA,GAAM,WAAS,EAACb,UAAU,CAAC1F,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEoC,OAAO,CAAC;cAAA;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACN9J,OAAA;cAAG0I,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAjC,kBAAkB,KAAK,MAAM,iBAC5B7H,OAAA;UAAK0I,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC3I,OAAA;YAAK0I,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC3I,OAAA,CAACT,MAAM;cAACmJ,SAAS,EAAC;YAAkB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvC9J,OAAA;cAAM0I,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAY;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACN9J,OAAA;YAAK0I,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnC3I,OAAA;cAAG0I,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAGb9J,OAAA,CAAChB,MAAM,CAAC4J,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEnG,QAAQ,EAAE,GAAG;UAAEiH,KAAK,EAAE;QAAI,CAAE;QAC1CrB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE3B3I,OAAA;UAAI0I,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC1Bd,kBAAkB,KAAK,QAAQ,GAC5B,sBAAsB,GACtBA,kBAAkB,KAAK,SAAS,GAC9B,4BAA4B,GAC5B;QAAqB;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEzB,CAAC,eAGL9J,OAAA;UAAKsD,KAAK,EAAE;YAAE2G,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAzB,QAAA,gBAC3F3I,OAAA;YACEkJ,OAAO,EAAEjE,mBAAoB;YAC7B3B,KAAK,EAAE;cACL8F,UAAU,EAAE,SAAS;cACrBC,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE,UAAU;cACnBC,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAE,SAAS;cACjBvC,QAAQ,EAAE;YACZ,CAAE;YAAAyB,QAAA,EACH;UAED;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9J,OAAA;YACEkJ,OAAO,EAAElE,gBAAiB;YAC1B1B,KAAK,EAAE;cACL8F,UAAU,EAAE,SAAS;cACrBC,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE,UAAU;cACnBC,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAE,SAAS;cACjBvC,QAAQ,EAAE;YACZ,CAAE;YAAAyB,QAAA,EACH;UAED;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN9J,OAAA;UAAG0I,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC5Bd,kBAAkB,KAAK,QAAQ,GAC5B,+DAA+D,GAC/DA,kBAAkB,KAAK,SAAS,GAC9B,iFAAiF,GACjF;QAA2F;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEhG,CAAC,EAEHpJ,OAAO,gBACNV,OAAA;UAAK0I,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B3I,OAAA;YAAK0I,SAAS,EAAC;UAAS;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/B9J,OAAA;YAAA2I,QAAA,EAAG;UAAgB;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,GACJtJ,KAAK,CAACyD,MAAM,KAAK,CAAC,gBACpBjE,OAAA;UAAK0I,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B3I,OAAA;YAAK0I,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvC9J,OAAA;YAAA2I,QAAA,EAAI;UAAkB;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3B9J,OAAA;YAAA2I,QAAA,EAAG;UAA6E;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpF9J,OAAA;YAAQ0I,SAAS,EAAC,aAAa;YAACQ,OAAO,EAAElG,UAAW;YAAA2F,QAAA,EAAC;UAErD;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAEN9J,OAAA;UAAK0I,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxBnI,KAAK,CAAC6J,GAAG,CAAEnF,IAAI;YAAA,IAAAoF,WAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,cAAA;YAAA,oBACdzK,OAAA,CAAChB,MAAM,CAAC4J,GAAG;cAET8B,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BjC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAErB3I,OAAA;gBAAK0I,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B3I,OAAA;kBAAI0I,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEzD,IAAI,CAACzC;gBAAK;kBAAAkH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC3C,EAAAQ,WAAA,GAAApF,IAAI,CAACzC,KAAK,cAAA6H,WAAA,uBAAVA,WAAA,CAAYpE,WAAW,CAAC,CAAC,CAACuB,QAAQ,CAAC,UAAU,CAAC,kBAC7CzH,OAAA;kBAAM0I,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAU;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN9J,OAAA;gBAAK0I,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B3I,OAAA;kBAAK0I,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5B3I,OAAA;oBAAK0I,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5B3I,OAAA;sBAAM0I,SAAS,EAAC,UAAU;sBAAAC,QAAA,EAAC;oBAAG;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,GAAAS,qBAAA,GACpCrF,IAAI,CAACtC,eAAe,cAAA2H,qBAAA,uBAApBA,qBAAA,CAAsBM,cAAc,CAAC,CAAC;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,EACL5E,IAAI,CAACvC,WAAW,GAAGuC,IAAI,CAACtC,eAAe,iBACtC5C,OAAA,CAAAE,SAAA;oBAAAyI,QAAA,gBACE3I,OAAA;sBAAM0I,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,IAAA6B,iBAAA,GAAEtF,IAAI,CAACvC,WAAW,cAAA6H,iBAAA,uBAAhBA,iBAAA,CAAkBK,cAAc,CAAC,CAAC,EAAC,MAAI;oBAAA;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChF9J,OAAA;sBAAM0I,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,GAC7BJ,IAAI,CAACuC,KAAK,CAAE,CAAC5F,IAAI,CAACvC,WAAW,GAAGuC,IAAI,CAACtC,eAAe,IAAIsC,IAAI,CAACvC,WAAW,GAAI,GAAG,CAAC,EAAC,OACpF;oBAAA;sBAAAgH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,eACP,CACH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACN9J,OAAA;kBAAK0I,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5B3I,OAAA;oBAAM0I,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAEzD,IAAI,CAACpC;kBAAQ;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,UAAM,EAAC5E,IAAI,CAACpC,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,SACjG;gBAAA;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN9J,OAAA;gBAAK0I,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAA8B,cAAA,GAC3BvF,IAAI,CAACxC,QAAQ,cAAA+H,cAAA,uBAAbA,cAAA,CAAeM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACV,GAAG,CAAC,CAACW,OAAO,EAAEC,KAAK,kBAC7CjL,OAAA;kBAAiB0I,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACvC3I,OAAA,CAACZ,aAAa;oBAACsJ,SAAS,EAAC;kBAAc;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1C9J,OAAA;oBAAA2I,QAAA,EAAOqC;kBAAO;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFdmB,KAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN9J,OAAA;gBACE0I,SAAS,EAAC,iBAAiB;gBAC3BQ,OAAO,EAAEA,CAAA,KAAMrE,gBAAgB,CAACK,IAAI,CAAE;gBACtCgG,QAAQ,EAAEtK,cAAc,KAAKsE,IAAI,CAAC1C,GAAI;gBACtCc,KAAK,EAAE;kBACL8F,UAAU,EAAE,2CAA2C;kBACvDC,KAAK,EAAE,OAAO;kBACdC,MAAM,EAAE,MAAM;kBACdE,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,aAAa;kBACtBrC,QAAQ,EAAE,MAAM;kBAChBiE,UAAU,EAAE,KAAK;kBACjB1B,MAAM,EAAE7I,cAAc,KAAKsE,IAAI,CAAC1C,GAAG,GAAG,aAAa,GAAG,SAAS;kBAC/DyG,UAAU,EAAE,eAAe;kBAC3BgB,OAAO,EAAE,MAAM;kBACfmB,UAAU,EAAE,QAAQ;kBACpBjB,cAAc,EAAE,QAAQ;kBACxBD,GAAG,EAAE,QAAQ;kBACbzG,KAAK,EAAE,MAAM;kBACbqF,OAAO,EAAElI,cAAc,KAAKsE,IAAI,CAAC1C,GAAG,GAAG,GAAG,GAAG;gBAC/C,CAAE;gBACF6I,YAAY,EAAGC,CAAC,IAAK;kBACnB,IAAI1K,cAAc,KAAKsE,IAAI,CAAC1C,GAAG,EAAE;oBAC/B8I,CAAC,CAACC,MAAM,CAACjI,KAAK,CAAC8F,UAAU,GAAG,2CAA2C;oBACvEkC,CAAC,CAACC,MAAM,CAACjI,KAAK,CAACkI,SAAS,GAAG,kBAAkB;oBAC7CF,CAAC,CAACC,MAAM,CAACjI,KAAK,CAACmI,SAAS,GAAG,oCAAoC;kBACjE;gBACF,CAAE;gBACFC,YAAY,EAAGJ,CAAC,IAAK;kBACnB,IAAI1K,cAAc,KAAKsE,IAAI,CAAC1C,GAAG,EAAE;oBAC/B8I,CAAC,CAACC,MAAM,CAACjI,KAAK,CAAC8F,UAAU,GAAG,2CAA2C;oBACvEkC,CAAC,CAACC,MAAM,CAACjI,KAAK,CAACkI,SAAS,GAAG,eAAe;oBAC1CF,CAAC,CAACC,MAAM,CAACjI,KAAK,CAACmI,SAAS,GAAG,oCAAoC;kBACjE;gBACF,CAAE;gBAAA9C,QAAA,gBAEF3I,OAAA,CAACV,YAAY;kBAACoJ,SAAS,EAAC;gBAAU;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACpClJ,cAAc,KAAKsE,IAAI,CAAC1C,GAAG,GACxB,eAAe,GACfqF,kBAAkB,KAAK,QAAQ,GAC7B,kBAAkB,GAClBA,kBAAkB,KAAK,SAAS,GAC9B,gBAAgB,GAChB,cAAc;cAAA;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CAAC;YAAA,GAtFJ5E,IAAI,CAAC1C,GAAG;cAAAmH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuFH,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,EAGZ,CAAC,CAAC5H,IAAI,CAACiD,WAAW,IAAI,CAAC,gBAAgB,CAACC,IAAI,CAAClD,IAAI,CAACiD,WAAW,CAAC,kBAC7DnF,OAAA,CAAChB,MAAM,CAAC4J,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEnG,QAAQ,EAAE,GAAG;UAAEiH,KAAK,EAAE;QAAI,CAAE;QAC1CrB,SAAS,EAAC,eAAe;QAAAC,QAAA,eAEzB3I,OAAA;UAAK0I,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B3I,OAAA,CAACX,aAAa;YAACqJ,SAAS,EAAC;UAAc;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1C9J,OAAA;YAAA2I,QAAA,gBACE3I,OAAA;cAAA2I,QAAA,EAAI;YAAqB;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9B9J,OAAA;cAAA2I,QAAA,EAAG;YAAuE;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9E9J,OAAA;cACE0I,SAAS,EAAC,kBAAkB;cAC5BQ,OAAO,EAAEA,CAAA,KAAM/F,MAAM,CAACoE,QAAQ,CAACC,IAAI,GAAG,UAAW;cAAAmB,QAAA,EAClD;YAED;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,EAGAhJ,mBAAmB,iBAClBd,OAAA;QACE0I,SAAS,EAAC,oBAAoB;QAC9BpF,KAAK,EAAE;UACLE,QAAQ,EAAE,OAAO;UACjBE,GAAG,EAAE,GAAG;UACRiI,IAAI,EAAE,GAAG;UACTlI,KAAK,EAAE,OAAO;UACdmI,MAAM,EAAE,OAAO;UACfxC,UAAU,EAAE,qBAAqB;UACjCyC,cAAc,EAAE,YAAY;UAC5BC,oBAAoB,EAAE,YAAY;UAClC7B,OAAO,EAAE,MAAM;UACfmB,UAAU,EAAE,QAAQ;UACpBjB,cAAc,EAAE,QAAQ;UACxBT,MAAM,EAAE,OAAO;UACfH,OAAO,EAAE,MAAM;UACfwC,SAAS,EAAE;QACb,CAAE;QACF7C,OAAO,EAAGoC,CAAC,IAAKA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACU,aAAa,IAAIlH,0BAA0B,CAAC,CAAE;QAAA6D,QAAA,eAE7E3I,OAAA;UACE0I,SAAS,EAAC,sBAAsB;UAChCpF,KAAK,EAAE;YACL8F,UAAU,EAAE,mDAAmD;YAC/DI,YAAY,EAAE,MAAM;YACpBiC,SAAS,EAAE,oEAAoE;YAC/EnC,MAAM,EAAE,mCAAmC;YAC3C7F,KAAK,EAAE,MAAM;YACbwI,QAAQ,EAAE,OAAO;YACjBC,SAAS,EAAE,MAAM;YACjBjC,OAAO,EAAE,MAAM;YACfkC,aAAa,EAAE,QAAQ;YACvB5I,QAAQ,EAAE,QAAQ;YAClBC,QAAQ,EAAE,UAAU;YACpBgI,SAAS,EAAE;UACb,CAAE;UACFtC,OAAO,EAAGoC,CAAC,IAAKA,CAAC,CAACc,eAAe,CAAC,CAAE;UAAAzD,QAAA,gBAGpC3I,OAAA;YAAKsD,KAAK,EAAE;cACV8F,UAAU,EAAE,mDAAmD;cAC/DG,OAAO,EAAE,QAAQ;cACjBF,KAAK,EAAE,OAAO;cACdgD,SAAS,EAAE,QAAQ;cACnB7I,QAAQ,EAAE;YACZ,CAAE;YAAAmF,QAAA,gBACA3I,OAAA;cACE0I,SAAS,EAAC,qBAAqB;cAC/BQ,OAAO,EAAEpE,0BAA2B;cACpCrC,KAAK,EAAC,sBAAsB;cAC5Ba,KAAK,EAAE;gBACLE,QAAQ,EAAE,UAAU;gBACpBE,GAAG,EAAE,MAAM;gBACXyF,KAAK,EAAE,MAAM;gBACbC,UAAU,EAAE,0BAA0B;gBACtCE,MAAM,EAAE,MAAM;gBACdE,YAAY,EAAE,KAAK;gBACnB/F,KAAK,EAAE,MAAM;gBACbmI,MAAM,EAAE,MAAM;gBACdvC,KAAK,EAAE,OAAO;gBACdnC,QAAQ,EAAE,MAAM;gBAChBuC,MAAM,EAAE,SAAS;gBACjBQ,OAAO,EAAE,MAAM;gBACfmB,UAAU,EAAE,QAAQ;gBACpBjB,cAAc,EAAE,QAAQ;gBACxBlB,UAAU,EAAE,eAAe;gBAC3BS,MAAM,EAAE;cACV,CAAE;cACF2B,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACjI,KAAK,CAAC8F,UAAU,GAAG,0BAA2B;cAC5EsC,YAAY,EAAGJ,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACjI,KAAK,CAAC8F,UAAU,GAAG,0BAA2B;cAAAT,QAAA,EAC7E;YAED;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET9J,OAAA;cAAKsD,KAAK,EAAE;gBAAE8G,YAAY,EAAE;cAAS,CAAE;cAAAzB,QAAA,eACrC3I,OAAA;gBAAK0I,SAAS,EAAC,8BAA8B;gBAACpF,KAAK,EAAE;kBACnDG,KAAK,EAAE,MAAM;kBACbmI,MAAM,EAAE,MAAM;kBACdU,MAAM,EAAE,aAAa;kBACrB9I,QAAQ,EAAE;gBACZ,CAAE;gBAAAmF,QAAA,gBACA3I,OAAA;kBAAK0I,SAAS,EAAC,iBAAiB;kBAACpF,KAAK,EAAE;oBACtCG,KAAK,EAAE,MAAM;oBACbmI,MAAM,EAAE,MAAM;oBACdtC,MAAM,EAAE,oCAAoC;oBAC5CiD,SAAS,EAAE,iBAAiB;oBAC5B/C,YAAY,EAAE,KAAK;oBACnBgD,SAAS,EAAE;kBACb;gBAAE;kBAAA7C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACT9J,OAAA;kBAAKsD,KAAK,EAAE;oBACVE,QAAQ,EAAE,UAAU;oBACpBE,GAAG,EAAE,KAAK;oBACViI,IAAI,EAAE,KAAK;oBACXH,SAAS,EAAE,uBAAuB;oBAClCtE,QAAQ,EAAE;kBACZ,CAAE;kBAAAyB,QAAA,EAAC;gBAAE;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN9J,OAAA;cAAIsD,KAAK,EAAE;gBACTgJ,MAAM,EAAE,GAAG;gBACXpF,QAAQ,EAAE,QAAQ;gBAClBiE,UAAU,EAAE,KAAK;gBACjBsB,UAAU,EAAE;cACd,CAAE;cAAA9D,QAAA,EAAC;YAEH;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL9J,OAAA;cAAGsD,KAAK,EAAE;gBACRgJ,MAAM,EAAE,cAAc;gBACtBpF,QAAQ,EAAE,QAAQ;gBAClB4B,OAAO,EAAE;cACX,CAAE;cAAAH,QAAA,EAAC;YAEH;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGN9J,OAAA;YAAKsD,KAAK,EAAE;cACVoJ,IAAI,EAAE,GAAG;cACTC,SAAS,EAAE,MAAM;cACjBC,SAAS,EAAE,QAAQ;cACnBrD,OAAO,EAAE;YACX,CAAE;YAAAZ,QAAA,eACA3I,OAAA;cAAKsD,KAAK,EAAE;gBACViG,OAAO,EAAE,MAAM;gBACfU,OAAO,EAAE,MAAM;gBACfkC,aAAa,EAAE,QAAQ;gBACvBjC,GAAG,EAAE,MAAM;gBACX2C,SAAS,EAAE;cACb,CAAE;cAAAlE,QAAA,gBAEF3I,OAAA;gBAAKsD,KAAK,EAAE;kBACV8F,UAAU,EAAE,0BAA0B;kBACtCE,MAAM,EAAE,mCAAmC;kBAC3CE,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,SAAS;kBAClB8C,SAAS,EAAE;gBACb,CAAE;gBAAA1D,QAAA,eACA3I,OAAA;kBAAGsD,KAAK,EAAE;oBACRgJ,MAAM,EAAE,GAAG;oBACXjD,KAAK,EAAE,SAAS;oBAChBnC,QAAQ,EAAE,SAAS;oBACnBiE,UAAU,EAAE;kBACd,CAAE;kBAAAxC,QAAA,EACCrH;gBAAa;kBAAAqI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGN9J,OAAA;gBAAKsD,KAAK,EAAE;kBACV8F,UAAU,EAAE,0BAA0B;kBACtCE,MAAM,EAAE,mCAAmC;kBAC3CE,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE;gBACX,CAAE;gBAAAZ,QAAA,gBACA3I,OAAA;kBAAIsD,KAAK,EAAE;oBACTgJ,MAAM,EAAE,cAAc;oBACtBjD,KAAK,EAAE,SAAS;oBAChBnC,QAAQ,EAAE,QAAQ;oBAClBiE,UAAU,EAAE;kBACd,CAAE;kBAAAxC,QAAA,EACCvH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqB;gBAAK;kBAAAkH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACL9J,OAAA;kBAAKsD,KAAK,EAAE;oBACV2G,OAAO,EAAE,MAAM;oBACfE,cAAc,EAAE,eAAe;oBAC/BiB,UAAU,EAAE,QAAQ;oBACpBlE,QAAQ,EAAE,QAAQ;oBAClBmC,KAAK,EAAE;kBACT,CAAE;kBAAAV,QAAA,gBACA3I,OAAA;oBAAA2I,QAAA,GAAM,UAAQ,eAAA3I,OAAA;sBAAA2I,QAAA,GAASvH,YAAY,aAAZA,YAAY,wBAAAd,qBAAA,GAAZc,YAAY,CAAEwB,eAAe,cAAAtC,qBAAA,uBAA7BA,qBAAA,CAA+BuK,cAAc,CAAC,CAAC,EAAC,MAAI;oBAAA;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3F9J,OAAA;oBAAA2I,QAAA,GAAM,YAAU,eAAA3I,OAAA;sBAAA2I,QAAA,GAASvH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0B,QAAQ,EAAC,QAAM,EAAC,CAAA1B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0B,QAAQ,IAAG,CAAC,GAAG,GAAG,GAAG,EAAE;oBAAA;sBAAA6G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN9J,OAAA;gBAAKsD,KAAK,EAAE;kBACV8F,UAAU,EAAE,0BAA0B;kBACtCE,MAAM,EAAE,mCAAmC;kBAC3CE,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE;gBACX,CAAE;gBAAAZ,QAAA,gBACA3I,OAAA;kBAAIsD,KAAK,EAAE;oBACTgJ,MAAM,EAAE,eAAe;oBACvBjD,KAAK,EAAE,SAAS;oBAChBnC,QAAQ,EAAE,MAAM;oBAChBiE,UAAU,EAAE,KAAK;oBACjBlB,OAAO,EAAE,MAAM;oBACfmB,UAAU,EAAE,QAAQ;oBACpBlB,GAAG,EAAE;kBACP,CAAE;kBAAAvB,QAAA,EAAC;gBAEH;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9J,OAAA;kBAAGsD,KAAK,EAAE;oBACRgJ,MAAM,EAAE,eAAe;oBACvBjD,KAAK,EAAE,SAAS;oBAChBnC,QAAQ,EAAE,QAAQ;oBAClBiE,UAAU,EAAE,KAAK;oBACjB/B,UAAU,EAAE,yBAAyB;oBACrCG,OAAO,EAAE,QAAQ;oBACjBC,YAAY,EAAE,KAAK;oBACnB6C,SAAS,EAAE;kBACb,CAAE;kBAAA1D,QAAA,GAAC,UACO,EAACzG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiD,WAAW;gBAAA;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACJ9J,OAAA;kBAAKsD,KAAK,EAAE;oBACV+F,KAAK,EAAE,SAAS;oBAChBnC,QAAQ,EAAE,SAAS;oBACnB4F,UAAU,EAAE;kBACd,CAAE;kBAAAnE,QAAA,gBACA3I,OAAA;oBAAKsD,KAAK,EAAE;sBAAE8G,YAAY,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAAoD;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnG9J,OAAA;oBAAKsD,KAAK,EAAE;sBAAE8G,YAAY,EAAE;oBAAU,CAAE;oBAAAzB,QAAA,EAAC;kBAA0C;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzF9J,OAAA;oBAAA2I,QAAA,EAAK;kBAAwC;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGHhI,YAAY,iBACX9B,OAAA;gBAAKsD,KAAK,EAAE;kBACV8F,UAAU,EAAE,yBAAyB;kBACrCE,MAAM,EAAE,kCAAkC;kBAC1CE,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,MAAM;kBACf8C,SAAS,EAAE,QAAQ;kBACnB/F,SAAS,EAAE;gBACb,CAAE;gBAAAqC,QAAA,gBACF3I,OAAA;kBAAGsD,KAAK,EAAE;oBACRgJ,MAAM,EAAE,cAAc;oBACtBjD,KAAK,EAAE,SAAS;oBAChBnC,QAAQ,EAAE,QAAQ;oBAClBiE,UAAU,EAAE;kBACd,CAAE;kBAAAxC,QAAA,EAAC;gBAEH;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ9J,OAAA;kBAAGsD,KAAK,EAAE;oBACRgJ,MAAM,EAAE,YAAY;oBACpBjD,KAAK,EAAE,SAAS;oBAChBnC,QAAQ,EAAE;kBACZ,CAAE;kBAAAyB,QAAA,EAAC;gBAEH;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ9J,OAAA;kBACE0I,SAAS,EAAC,kBAAkB;kBAC5BQ,OAAO,EAAEnE,cAAe;kBACxBzB,KAAK,EAAE;oBACL8F,UAAU,EAAE,mDAAmD;oBAC/DC,KAAK,EAAE,OAAO;oBACdC,MAAM,EAAE,MAAM;oBACdC,OAAO,EAAE,gBAAgB;oBACzBC,YAAY,EAAE,MAAM;oBACpBtC,QAAQ,EAAE,QAAQ;oBAClBiE,UAAU,EAAE,KAAK;oBACjB1B,MAAM,EAAE,SAAS;oBACjBR,UAAU,EAAE,eAAe;oBAC3BwC,SAAS,EAAE,oCAAoC;oBAC/ChI,KAAK,EAAE,MAAM;oBACbwI,QAAQ,EAAE,OAAO;oBACjBhC,OAAO,EAAE,MAAM;oBACfmB,UAAU,EAAE,QAAQ;oBACpBjB,cAAc,EAAE,QAAQ;oBACxBD,GAAG,EAAE,QAAQ;oBACboC,MAAM,EAAE;kBACV,CAAE;kBACFjB,YAAY,EAAGC,CAAC,IAAK;oBACnBA,CAAC,CAACC,MAAM,CAACjI,KAAK,CAAC8F,UAAU,GAAG,mDAAmD;oBAC/EkC,CAAC,CAACC,MAAM,CAACjI,KAAK,CAACkI,SAAS,GAAG,kBAAkB;oBAC7CF,CAAC,CAACC,MAAM,CAACjI,KAAK,CAACmI,SAAS,GAAG,oCAAoC;kBACjE,CAAE;kBACFC,YAAY,EAAGJ,CAAC,IAAK;oBACnBA,CAAC,CAACC,MAAM,CAACjI,KAAK,CAAC8F,UAAU,GAAG,mDAAmD;oBAC/EkC,CAAC,CAACC,MAAM,CAACjI,KAAK,CAACkI,SAAS,GAAG,eAAe;oBAC1CF,CAAC,CAACC,MAAM,CAACjI,KAAK,CAACmI,SAAS,GAAG,oCAAoC;kBACjE,CAAE;kBAAA9C,QAAA,EACH;gBAED;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA5I,gBAAgB,iBACflB,OAAA;QACE0I,SAAS,EAAC,oBAAoB;QAC9BpF,KAAK,EAAE;UACLE,QAAQ,EAAE,OAAO;UACjBE,GAAG,EAAE,GAAG;UACRiI,IAAI,EAAE,GAAG;UACTlI,KAAK,EAAE,OAAO;UACdmI,MAAM,EAAE,OAAO;UACfxC,UAAU,EAAE,oBAAoB;UAChCyC,cAAc,EAAE,YAAY;UAC5BC,oBAAoB,EAAE,YAAY;UAClC7B,OAAO,EAAE,MAAM;UACfmB,UAAU,EAAE,QAAQ;UACpBjB,cAAc,EAAE,QAAQ;UACxBT,MAAM,EAAE,OAAO;UACfH,OAAO,EAAE,MAAM;UACfwC,SAAS,EAAE;QACb,CAAE;QACF7C,OAAO,EAAGoC,CAAC,IAAK;UACd,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACU,aAAa,EAAE;YAChC/J,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;YAChCd,mBAAmB,CAAC,KAAK,CAAC;UAC5B;QACF,CAAE;QAAAwH,QAAA,eAEF3I,OAAA;UACE0I,SAAS,EAAC,yCAAyC;UACnDpF,KAAK,EAAE;YACL8F,UAAU,EAAE,mDAAmD;YAC/DI,YAAY,EAAE,MAAM;YACpBiC,SAAS,EAAE,kEAAkE;YAC7EnC,MAAM,EAAE,kCAAkC;YAC1C7F,KAAK,EAAE,MAAM;YACbwI,QAAQ,EAAE,OAAO;YACjBC,SAAS,EAAE,MAAM;YACjBjC,OAAO,EAAE,MAAM;YACfkC,aAAa,EAAE,QAAQ;YACvB5I,QAAQ,EAAE,QAAQ;YAClBC,QAAQ,EAAE,UAAU;YACpBgI,SAAS,EAAE;UACb,CAAE;UACFtC,OAAO,EAAGoC,CAAC,IAAKA,CAAC,CAACc,eAAe,CAAC,CAAE;UAAAzD,QAAA,gBAGpC3I,OAAA;YAAKsD,KAAK,EAAE;cACV8F,UAAU,EAAE,mDAAmD;cAC/DG,OAAO,EAAE,MAAM;cACfF,KAAK,EAAE,OAAO;cACdgD,SAAS,EAAE,QAAQ;cACnB7I,QAAQ,EAAE,UAAU;cACpBgG,YAAY,EAAE,eAAe;cAC7BuD,UAAU,EAAE;YACd,CAAE;YAAApE,QAAA,gBACA3I,OAAA;cACEkJ,OAAO,EAAEA,CAAA,KAAM;gBACbjH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;gBAChCd,mBAAmB,CAAC,KAAK,CAAC;cAC5B,CAAE;cACFmC,KAAK,EAAE;gBACLE,QAAQ,EAAE,UAAU;gBACpBE,GAAG,EAAE,MAAM;gBACXyF,KAAK,EAAE,MAAM;gBACbC,UAAU,EAAE,0BAA0B;gBACtCE,MAAM,EAAE,MAAM;gBACdE,YAAY,EAAE,KAAK;gBACnB/F,KAAK,EAAE,MAAM;gBACbmI,MAAM,EAAE,MAAM;gBACdvC,KAAK,EAAE,OAAO;gBACdnC,QAAQ,EAAE,MAAM;gBAChBuC,MAAM,EAAE,SAAS;gBACjBQ,OAAO,EAAE,MAAM;gBACfmB,UAAU,EAAE,QAAQ;gBACpBjB,cAAc,EAAE,QAAQ;gBACxBlB,UAAU,EAAE,eAAe;gBAC3BS,MAAM,EAAE;cACV,CAAE;cACF2B,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACjI,KAAK,CAAC8F,UAAU,GAAG,0BAA2B;cAC5EsC,YAAY,EAAGJ,CAAC,IAAKA,CAAC,CAACC,MAAM,CAACjI,KAAK,CAAC8F,UAAU,GAAG,0BAA2B;cAAAT,QAAA,EAC7E;YAED;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET9J,OAAA;cAAKsD,KAAK,EAAE;gBACV4D,QAAQ,EAAE,MAAM;gBAChBkD,YAAY,EAAE,MAAM;gBACpBoC,SAAS,EAAE;cACb,CAAE;cAAA7D,QAAA,EAAC;YAEH;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAEN9J,OAAA;cAAIsD,KAAK,EAAE;gBACTgJ,MAAM,EAAE,GAAG;gBACXpF,QAAQ,EAAE,MAAM;gBAChBiE,UAAU,EAAE,KAAK;gBACjBsB,UAAU,EAAE,8BAA8B;gBAC1CrC,YAAY,EAAE;cAChB,CAAE;cAAAzB,QAAA,EAAC;YAEH;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEL9J,OAAA;cAAGsD,KAAK,EAAE;gBACRgJ,MAAM,EAAE,GAAG;gBACXpF,QAAQ,EAAE,QAAQ;gBAClB4B,OAAO,EAAE,MAAM;gBACfqC,UAAU,EAAE;cACd,CAAE;cAAAxC,QAAA,GAAC,aACU,EAACvH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqB,KAAK,EAAC,gBAClC;YAAA;cAAAkH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGN9J,OAAA;YAAKsD,KAAK,EAAE;cACVoJ,IAAI,EAAE,GAAG;cACTC,SAAS,EAAE,MAAM;cACjBC,SAAS,EAAE,QAAQ;cACnBrD,OAAO,EAAE;YACX,CAAE;YAAAZ,QAAA,eACA3I,OAAA;cAAKsD,KAAK,EAAE;gBACViG,OAAO,EAAE,MAAM;gBACfU,OAAO,EAAE,MAAM;gBACfkC,aAAa,EAAE,QAAQ;gBACvBjC,GAAG,EAAE,MAAM;gBACX2C,SAAS,EAAE;cACb,CAAE;cAAAlE,QAAA,gBAEF3I,OAAA;gBAAKsD,KAAK,EAAE;kBACV8F,UAAU,EAAE,mDAAmD;kBAC/DE,MAAM,EAAE,mBAAmB;kBAC3BE,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,QAAQ;kBACjB8C,SAAS,EAAE,QAAQ;kBACnBZ,SAAS,EAAE;gBACb,CAAE;gBAAA9C,QAAA,eACA3I,OAAA;kBAAKsD,KAAK,EAAE;oBACV8F,UAAU,EAAE,wBAAwB;oBACpCI,YAAY,EAAE,MAAM;oBACpBD,OAAO,EAAE,MAAM;oBACfa,YAAY,EAAE;kBAChB,CAAE;kBAAAzB,QAAA,gBACA3I,OAAA;oBAAIsD,KAAK,EAAE;sBACT+F,KAAK,EAAE,SAAS;sBAChBe,YAAY,EAAE,MAAM;sBACpBlD,QAAQ,EAAE,QAAQ;sBAClBiE,UAAU,EAAE;oBACd,CAAE;oBAAAxC,QAAA,EAAC;kBAEH;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL9J,OAAA;oBAAKsD,KAAK,EAAE;sBACV2G,OAAO,EAAE,MAAM;sBACfC,GAAG,EAAE,SAAS;sBACdmC,SAAS,EAAE;oBACb,CAAE;oBAAA1D,QAAA,gBACA3I,OAAA;sBAAKsD,KAAK,EAAE;wBACV2G,OAAO,EAAE,MAAM;wBACfE,cAAc,EAAE,eAAe;wBAC/BiB,UAAU,EAAE,QAAQ;wBACpB7B,OAAO,EAAE,QAAQ;wBACjBH,UAAU,EAAE,0BAA0B;wBACtCI,YAAY,EAAE;sBAChB,CAAE;sBAAAb,QAAA,gBACA3I,OAAA;wBAAMsD,KAAK,EAAE;0BAAE6H,UAAU,EAAE,KAAK;0BAAE9B,KAAK,EAAE;wBAAU,CAAE;wBAAAV,QAAA,EAAC;sBAAQ;wBAAAgB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrE9J,OAAA;wBAAMsD,KAAK,EAAE;0BAAE+F,KAAK,EAAE,SAAS;0BAAE8B,UAAU,EAAE;wBAAM,CAAE;wBAAAxC,QAAA,EAAEvH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqB;sBAAK;wBAAAkH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/E,CAAC,eACN9J,OAAA;sBAAKsD,KAAK,EAAE;wBACV2G,OAAO,EAAE,MAAM;wBACfE,cAAc,EAAE,eAAe;wBAC/BiB,UAAU,EAAE,QAAQ;wBACpB7B,OAAO,EAAE,QAAQ;wBACjBH,UAAU,EAAE,0BAA0B;wBACtCI,YAAY,EAAE;sBAChB,CAAE;sBAAAb,QAAA,gBACA3I,OAAA;wBAAMsD,KAAK,EAAE;0BAAE6H,UAAU,EAAE,KAAK;0BAAE9B,KAAK,EAAE;wBAAU,CAAE;wBAAAV,QAAA,EAAC;sBAAW;wBAAAgB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACxE9J,OAAA;wBAAMsD,KAAK,EAAE;0BAAE+F,KAAK,EAAE,SAAS;0BAAE8B,UAAU,EAAE;wBAAM,CAAE;wBAAAxC,QAAA,GAAEvH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0B,QAAQ,EAAC,QAAM,EAAC,CAAA1B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0B,QAAQ,IAAG,CAAC,GAAG,GAAG,GAAG,EAAE;sBAAA;wBAAA6G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/H,CAAC,eACN9J,OAAA;sBAAKsD,KAAK,EAAE;wBACV2G,OAAO,EAAE,MAAM;wBACfE,cAAc,EAAE,eAAe;wBAC/BiB,UAAU,EAAE,QAAQ;wBACpB7B,OAAO,EAAE,QAAQ;wBACjBH,UAAU,EAAE,0BAA0B;wBACtCI,YAAY,EAAE;sBAChB,CAAE;sBAAAb,QAAA,gBACA3I,OAAA;wBAAMsD,KAAK,EAAE;0BAAE6H,UAAU,EAAE,KAAK;0BAAE9B,KAAK,EAAE;wBAAU,CAAE;wBAAAV,QAAA,EAAC;sBAAU;wBAAAgB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvE9J,OAAA;wBAAMsD,KAAK,EAAE;0BAAE+F,KAAK,EAAE,SAAS;0BAAE8B,UAAU,EAAE;wBAAM,CAAE;wBAAAxC,QAAA,GAAEvH,YAAY,aAAZA,YAAY,wBAAAb,sBAAA,GAAZa,YAAY,CAAEwB,eAAe,cAAArC,sBAAA,uBAA7BA,sBAAA,CAA+BsK,cAAc,CAAC,CAAC,EAAC,MAAI;sBAAA;wBAAAlB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/G,CAAC,eACN9J,OAAA;sBAAKsD,KAAK,EAAE;wBACV2G,OAAO,EAAE,MAAM;wBACfE,cAAc,EAAE,eAAe;wBAC/BiB,UAAU,EAAE,QAAQ;wBACpB7B,OAAO,EAAE,QAAQ;wBACjBH,UAAU,EAAE,2CAA2C;wBACvDI,YAAY,EAAE,KAAK;wBACnBH,KAAK,EAAE;sBACT,CAAE;sBAAAV,QAAA,gBACA3I,OAAA;wBAAMsD,KAAK,EAAE;0BAAE6H,UAAU,EAAE;wBAAM,CAAE;wBAAAxC,QAAA,EAAC;sBAAU;wBAAAgB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrD9J,OAAA;wBAAMsD,KAAK,EAAE;0BAAE6H,UAAU,EAAE,KAAK;0BAAEjE,QAAQ,EAAE;wBAAS,CAAE;wBAAAyB,QAAA,EAAC;sBAAQ;wBAAAgB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN9J,OAAA;gBAAKsD,KAAK,EAAE;kBACV8F,UAAU,EAAE,mDAAmD;kBAC/DE,MAAM,EAAE,mBAAmB;kBAC3BE,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,QAAQ;kBACjBkC,SAAS,EAAE;gBACb,CAAE;gBAAA9C,QAAA,gBACA3I,OAAA;kBAAIsD,KAAK,EAAE;oBACT+F,KAAK,EAAE,SAAS;oBAChBe,YAAY,EAAE,MAAM;oBACpBiC,SAAS,EAAE,QAAQ;oBACnBnF,QAAQ,EAAE,QAAQ;oBAClBiE,UAAU,EAAE;kBACd,CAAE;kBAAAxC,QAAA,EAAC;gBAEH;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL9J,OAAA;kBAAKsD,KAAK,EAAE;oBACV2G,OAAO,EAAE,MAAM;oBACf+C,mBAAmB,EAAE,sCAAsC;oBAC3D9C,GAAG,EAAE;kBACP,CAAE;kBAAAvB,QAAA,gBACA3I,OAAA;oBAAKsD,KAAK,EAAE;sBACV8F,UAAU,EAAE,0BAA0B;sBACtCI,YAAY,EAAE,MAAM;sBACpBD,OAAO,EAAE;oBACX,CAAE;oBAAAZ,QAAA,eACA3I,OAAA;sBAAKsD,KAAK,EAAE;wBAAE8G,YAAY,EAAE,QAAQ;wBAAElD,QAAQ,EAAE,SAAS;wBAAEiE,UAAU,EAAE,KAAK;wBAAE9B,KAAK,EAAE;sBAAU,CAAE;sBAAAV,QAAA,gBAC/F3I,OAAA;wBAAKsD,KAAK,EAAE;0BAAE8G,YAAY,EAAE;wBAAU,CAAE;wBAAAzB,QAAA,GAAC,SAAE,eAAA3I,OAAA;0BAAA2I,QAAA,EAAQ;wBAAiB;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnF9J,OAAA;wBAAKsD,KAAK,EAAE;0BAAE8G,YAAY,EAAE;wBAAU,CAAE;wBAAAzB,QAAA,GAAC,eAAG,eAAA3I,OAAA;0BAAA2I,QAAA,EAAQ;wBAAY;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC/E9J,OAAA;wBAAA2I,QAAA,GAAK,eAAG,eAAA3I,OAAA;0BAAA2I,QAAA,EAAQ;wBAAe;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN9J,OAAA;oBAAKsD,KAAK,EAAE;sBACV8F,UAAU,EAAE,0BAA0B;sBACtCI,YAAY,EAAE,MAAM;sBACpBD,OAAO,EAAE;oBACX,CAAE;oBAAAZ,QAAA,eACA3I,OAAA;sBAAKsD,KAAK,EAAE;wBAAE8G,YAAY,EAAE,QAAQ;wBAAElD,QAAQ,EAAE,SAAS;wBAAEiE,UAAU,EAAE,KAAK;wBAAE9B,KAAK,EAAE;sBAAU,CAAE;sBAAAV,QAAA,gBAC/F3I,OAAA;wBAAKsD,KAAK,EAAE;0BAAE8G,YAAY,EAAE;wBAAU,CAAE;wBAAAzB,QAAA,GAAC,eAAG,eAAA3I,OAAA;0BAAA2I,QAAA,EAAQ;wBAAiB;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACpF9J,OAAA;wBAAKsD,KAAK,EAAE;0BAAE8G,YAAY,EAAE;wBAAU,CAAE;wBAAAzB,QAAA,GAAC,eAAG,eAAA3I,OAAA;0BAAA2I,QAAA,EAAQ;wBAAe;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAClF9J,OAAA;wBAAA2I,QAAA,GAAK,eAAG,eAAA3I,OAAA;0BAAA2I,QAAA,EAAQ;wBAAY;0BAAAgB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL9H,qBAAqB,iBACpBhC,OAAA;gBAAKsD,KAAK,EAAE;kBACV8F,UAAU,EAAE,mDAAmD;kBAC/DE,MAAM,EAAE,mBAAmB;kBAC3BE,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,MAAM;kBACf8C,SAAS,EAAE,QAAQ;kBACnB/F,SAAS,EAAE;gBACb,CAAE;gBAAAqC,QAAA,eACA3I,OAAA;kBAAGsD,KAAK,EAAE;oBACRgJ,MAAM,EAAE,GAAG;oBACXjD,KAAK,EAAE,SAAS;oBAChBnC,QAAQ,EAAE,QAAQ;oBAClBiE,UAAU,EAAE;kBACd,CAAE;kBAAAxC,QAAA,GAAC,mDACsC,EAAC3G,qBAAqB,EAAC,aAChE;gBAAA;kBAAA2H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN,eAGD9J,OAAA;gBAAKsD,KAAK,EAAE;kBACV2G,OAAO,EAAE,MAAM;kBACfC,GAAG,EAAE,MAAM;kBACXC,cAAc,EAAE,QAAQ;kBACxB8C,QAAQ,EAAE,MAAM;kBAChB3G,SAAS,EAAE,MAAM;kBACjB4G,UAAU,EAAE;gBACd,CAAE;gBAAAvE,QAAA,gBACA3I,OAAA;kBACEkJ,OAAO,EAAEA,CAAA,KAAM;oBACbjH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;oBAChCd,mBAAmB,CAAC,KAAK,CAAC;oBAC1BgC,MAAM,CAACoE,QAAQ,CAACC,IAAI,GAAG,WAAW;kBACpC,CAAE;kBACFlE,KAAK,EAAE;oBACL8F,UAAU,EAAE,mDAAmD;oBAC/DE,MAAM,EAAE,MAAM;oBACdD,KAAK,EAAE,OAAO;oBACdE,OAAO,EAAE,WAAW;oBACpBC,YAAY,EAAE,MAAM;oBACpBtC,QAAQ,EAAE,MAAM;oBAChBiE,UAAU,EAAE,KAAK;oBACjB1B,MAAM,EAAE,SAAS;oBACjBR,UAAU,EAAE,eAAe;oBAC3BwC,SAAS,EAAE,oCAAoC;oBAC/CxB,OAAO,EAAE,MAAM;oBACfmB,UAAU,EAAE,QAAQ;oBACpBlB,GAAG,EAAE,QAAQ;oBACbiD,QAAQ,EAAE,OAAO;oBACjBhD,cAAc,EAAE;kBAClB,CAAE;kBACFkB,YAAY,EAAGC,CAAC,IAAK;oBACnBA,CAAC,CAACC,MAAM,CAACjI,KAAK,CAAC8F,UAAU,GAAG,mDAAmD;oBAC/EkC,CAAC,CAACC,MAAM,CAACjI,KAAK,CAACkI,SAAS,GAAG,kBAAkB;oBAC7CF,CAAC,CAACC,MAAM,CAACjI,KAAK,CAACmI,SAAS,GAAG,oCAAoC;kBACjE,CAAE;kBACFC,YAAY,EAAGJ,CAAC,IAAK;oBACnBA,CAAC,CAACC,MAAM,CAACjI,KAAK,CAAC8F,UAAU,GAAG,mDAAmD;oBAC/EkC,CAAC,CAACC,MAAM,CAACjI,KAAK,CAACkI,SAAS,GAAG,eAAe;oBAC1CF,CAAC,CAACC,MAAM,CAACjI,KAAK,CAACmI,SAAS,GAAG,oCAAoC;kBACjE,CAAE;kBAAA9C,QAAA,GACH,+BACoB,EAAC3G,qBAAqB,GAAI,IAAGA,qBAAsB,IAAG,GAAG,EAAE;gBAAA;kBAAA2H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC,eACT9J,OAAA;kBACEkJ,OAAO,EAAEA,CAAA,KAAM;oBACbjH,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;oBAChCd,mBAAmB,CAAC,KAAK,CAAC;kBAC5B,CAAE;kBACFmC,KAAK,EAAE;oBACL8F,UAAU,EAAE,mDAAmD;oBAC/DE,MAAM,EAAE,mBAAmB;oBAC3BD,KAAK,EAAE,SAAS;oBAChBE,OAAO,EAAE,WAAW;oBACpBC,YAAY,EAAE,MAAM;oBACpBtC,QAAQ,EAAE,MAAM;oBAChBiE,UAAU,EAAE,KAAK;oBACjB1B,MAAM,EAAE,SAAS;oBACjBR,UAAU,EAAE,eAAe;oBAC3BwC,SAAS,EAAE,+BAA+B;oBAC1CxB,OAAO,EAAE,MAAM;oBACfmB,UAAU,EAAE,QAAQ;oBACpBlB,GAAG,EAAE,QAAQ;oBACbiD,QAAQ,EAAE,OAAO;oBACjBhD,cAAc,EAAE;kBAClB,CAAE;kBACFkB,YAAY,EAAGC,CAAC,IAAK;oBACnBA,CAAC,CAACC,MAAM,CAACjI,KAAK,CAAC8F,UAAU,GAAG,mDAAmD;oBAC/EkC,CAAC,CAACC,MAAM,CAACjI,KAAK,CAACkI,SAAS,GAAG,kBAAkB;oBAC7CF,CAAC,CAACC,MAAM,CAACjI,KAAK,CAACmI,SAAS,GAAG,gCAAgC;kBAC7D,CAAE;kBACFC,YAAY,EAAGJ,CAAC,IAAK;oBACnBA,CAAC,CAACC,MAAM,CAACjI,KAAK,CAAC8F,UAAU,GAAG,mDAAmD;oBAC/EkC,CAAC,CAACC,MAAM,CAACjI,KAAK,CAACkI,SAAS,GAAG,eAAe;oBAC1CF,CAAC,CAACC,MAAM,CAACjI,KAAK,CAACmI,SAAS,GAAG,+BAA+B;kBAC5D,CAAE;kBAAA9C,QAAA,EACH;gBAED;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAGN9J,OAAA;gBAAKsD,KAAK,EAAE;kBACVgD,SAAS,EAAE,QAAQ;kBACnBiD,OAAO,EAAE,MAAM;kBACfH,UAAU,EAAE,wBAAwB;kBACpCI,YAAY,EAAE,MAAM;kBACpB6C,SAAS,EAAE,QAAQ;kBACnB/C,MAAM,EAAE;gBACV,CAAE;gBAAAX,QAAA,eACA3I,OAAA;kBAAGsD,KAAK,EAAE;oBACRgJ,MAAM,EAAE,GAAG;oBACXpF,QAAQ,EAAE,MAAM;oBAChBmC,KAAK,EAAE,SAAS;oBAChB8B,UAAU,EAAE,KAAK;oBACjB2B,UAAU,EAAE;kBACd,CAAE;kBAAAnE,QAAA,EAAC;gBAEH;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD9J,OAAA,CAACH,uBAAuB;QACtBuN,OAAO,EAAE5L,sBAAuB;QAChC6L,OAAO,EAAEA,CAAA,KAAM5L,yBAAyB,CAAC,KAAK,CAAE;QAChD6L,WAAW,EAAE9M,KAAK,CAAC+M,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChL,GAAG,MAAKJ,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE4H,UAAU,EAAC,KAAI5H,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE8C,IAAI,CAAC;QAC/F7C,YAAY,EAAED,gBAAiB;QAC/BF,IAAI,EAAEA;MAAK;QAAAyH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAGF9J,OAAA,CAACF,wBAAwB;QACvBsN,OAAO,EAAE1L,gBAAiB;QAC1B2L,OAAO,EAAEA,CAAA,KAAM1L,mBAAmB,CAAC,KAAK,CAAE;QAC1C8L,OAAO,EAAE7I,uBAAwB;QACjCvC,YAAY,EAAED,gBAAiB;QAC/BF,IAAI,EAAEA,IAAK;QACX1B,KAAK,EAAEA;MAAM;QAAAmJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1J,EAAA,CA77CID,YAAY;EAAA,QAkBCrB,WAAW,EACCA,WAAW,EACvBC,WAAW;AAAA;AAAA2O,EAAA,GApBxBvN,YAAY;AA+7ClB,eAAeA,YAAY;AAAC,IAAAuN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}