{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Subscription\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport UpgradeRestrictionModal from '../../../components/UpgradeRestrictionModal/UpgradeRestrictionModal';\nimport SubscriptionExpiredModal from '../../../components/SubscriptionExpiredModal/SubscriptionExpiredModal';\nimport './Subscription.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Subscription = () => {\n  _s();\n  var _subscriptionData$act, _selectedPlan$discoun, _selectedPlan$discoun2;\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(null); // Changed to store plan ID instead of boolean\n  const [showProcessingModal, setShowProcessingModal] = useState(false);\n\n  // Debug: Log showProcessingModal state changes\n  useEffect(() => {\n    console.log('🔍 showProcessingModal state changed to:', showProcessingModal);\n  }, [showProcessingModal]);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [paymentStatus, setPaymentStatus] = useState('');\n  const [showUpgradeRestriction, setShowUpgradeRestriction] = useState(false);\n  const [showExpiredModal, setShowExpiredModal] = useState(false);\n  const [processingStartTime, setProcessingStartTime] = useState(null);\n  const [showTryAgain, setShowTryAgain] = useState(false);\n  const [autoNavigateCountdown, setAutoNavigateCountdown] = useState(null);\n  const [modalPosition, setModalPosition] = useState({\n    top: '50%',\n    left: '50%'\n  });\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const dispatch = useDispatch();\n\n  // Fallback sample plans in case API fails\n  const samplePlans = [{\n    _id: \"basic-plan-sample\",\n    title: \"Basic Membership\",\n    features: [\"2-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\"],\n    actualPrice: 28570,\n    discountedPrice: 20000,\n    discountPercentage: 30,\n    duration: 2,\n    status: true\n  }, {\n    _id: \"premium-plan-sample\",\n    title: \"Premium Plan\",\n    features: [\"3-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\", \"Priority support\"],\n    actualPrice: 45000,\n    discountedPrice: 35000,\n    discountPercentage: 22,\n    duration: 3,\n    status: true\n  }];\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n  }, []);\n\n  // Handle body scroll lock when modals are open (simplified approach)\n  useEffect(() => {\n    if (showProcessingModal || showSuccessModal) {\n      // Simply prevent body scroll without position fixed\n      document.body.style.overflow = 'hidden';\n    } else {\n      // Restore body scroll\n      document.body.style.overflow = '';\n    }\n\n    // Cleanup on unmount\n    return () => {\n      document.body.style.overflow = '';\n    };\n  }, [showProcessingModal, showSuccessModal]);\n\n  // Enhanced scroll detection for modal content\n  useEffect(() => {\n    const detectScrollableContent = () => {\n      const modalContents = document.querySelectorAll('.modal-content');\n      modalContents.forEach(content => {\n        if (content.scrollHeight > content.clientHeight) {\n          content.classList.add('has-scroll');\n        } else {\n          content.classList.remove('has-scroll');\n        }\n      });\n    };\n\n    // Detect on modal open\n    if (showProcessingModal || showSuccessModal) {\n      // Small delay to ensure modal is rendered\n      setTimeout(detectScrollableContent, 100);\n\n      // Re-detect on window resize\n      window.addEventListener('resize', detectScrollableContent);\n      return () => {\n        window.removeEventListener('resize', detectScrollableContent);\n      };\n    }\n  }, [showProcessingModal, showSuccessModal]);\n\n  // Check for expired subscription and show modal\n  useEffect(() => {\n    if (subscriptionData && isSubscriptionExpired()) {\n      console.log('🚫 Subscription expired, showing modal');\n      setShowExpiredModal(true);\n    } else {\n      setShowExpiredModal(false);\n    }\n  }, [subscriptionData]);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      console.log('Fetching plans...');\n      const response = await getPlans();\n      console.log('Plans response:', response);\n      if (response.success && response.data && response.data.length > 0) {\n        setPlans(response.data);\n        console.log('Plans loaded successfully from API:', response.data);\n      } else if (Array.isArray(response) && response.length > 0) {\n        // Handle case where response is directly an array of plans\n        setPlans(response);\n        console.log('Plans loaded as array from API:', response);\n      } else {\n        console.warn('No plans from API, using sample plans');\n        setPlans(samplePlans);\n        message.info('Showing sample plans. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('Error loading plans from API:', error);\n      console.log('Using fallback sample plans');\n      setPlans(samplePlans);\n      message.warning('Using sample plans. Please check your connection and try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n      console.log('Current subscription:', response);\n    } catch (error) {\n      console.log('No active subscription found');\n    }\n  };\n\n  // Check if subscription is expired\n  const isSubscriptionExpired = () => {\n    if (!subscriptionData) return true;\n\n    // If no subscription data, consider expired\n    if (!subscriptionData.endDate) return true;\n\n    // If payment status is not paid, consider expired\n    if (subscriptionData.paymentStatus !== 'paid') return true;\n\n    // If status is not active, consider expired\n    if (subscriptionData.status !== 'active') return true;\n\n    // Check if end date has passed\n    const endDate = new Date(subscriptionData.endDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n    endDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    return endDate < today;\n  };\n\n  // Handle subscription renewal from expired modal\n  const handleRenewSubscription = async selectedPlan => {\n    setShowExpiredModal(false);\n    await handlePlanSelect(selectedPlan);\n  };\n\n  // Handle closing payment processing modal\n  const handleCloseProcessingModal = () => {\n    setShowProcessingModal(false);\n    setPaymentLoading(null); // Reset to null instead of false\n    setShowTryAgain(false);\n    setProcessingStartTime(null);\n    setPaymentStatus('');\n    message.info('Payment process cancelled. You can try again anytime.');\n  };\n\n  // Handle try again functionality\n  const handleTryAgain = () => {\n    if (selectedPlan) {\n      setShowTryAgain(false);\n      setProcessingStartTime(null);\n      handlePlanSelect(selectedPlan);\n    }\n  };\n\n  // Test success modal (for debugging)\n  const testSuccessModal = () => {\n    console.log('🧪 Testing success modal...');\n    setShowProcessingModal(false);\n    setShowSuccessModal(true);\n    setPaymentLoading(null);\n  };\n\n  // Test processing modal (for debugging)\n  const testProcessingModal = () => {\n    console.log('🧪 Testing processing modal...');\n    setShowProcessingModal(true);\n    setPaymentStatus('Testing processing modal...');\n    setSelectedPlan(plans[0] || {\n      title: 'Test Plan',\n      discountedPrice: 5000,\n      duration: 1\n    });\n  };\n  const handlePlanSelect = async (plan, buttonElement = null) => {\n    // Check if user already has an active subscription\n    if (subscriptionData && subscriptionData.status === 'active' && subscriptionData.paymentStatus === 'paid') {\n      console.log('🚫 User already has active subscription:', subscriptionData);\n      setShowUpgradeRestriction(true);\n      return;\n    }\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n\n    // Calculate modal position based on button position\n    if (buttonElement) {\n      const buttonRect = buttonElement.getBoundingClientRect();\n      const viewportHeight = window.innerHeight;\n      const viewportWidth = window.innerWidth;\n\n      // Position modal above the button\n      const modalTop = Math.max(50, buttonRect.top - 50); // 50px above button, minimum 50px from top\n      const modalLeft = Math.min(Math.max(50, buttonRect.left), viewportWidth - 400); // Keep within viewport\n\n      setModalPosition({\n        top: `${modalTop}px`,\n        left: `${modalLeft}px`,\n        transform: 'none' // Remove default centering\n      });\n    } else {\n      // Default center position\n      setModalPosition({\n        top: '50%',\n        left: '50%',\n        transform: 'translate(-50%, -50%)'\n      });\n    }\n    try {\n      var _user$name;\n      console.log('🚀 Starting payment for plan:', plan.title);\n      console.log('🔧 IMMEDIATELY showing processing modal...');\n\n      // IMMEDIATELY show processing modal when user chooses plan\n      setSelectedPlan(plan);\n      setPaymentLoading(plan._id);\n      setShowProcessingModal(true);\n      setShowTryAgain(false);\n      setProcessingStartTime(Date.now());\n      setPaymentStatus('🚀 Preparing your payment request...');\n      console.log('✅ Processing modal IMMEDIATELY displayed');\n\n      // Small delay to ensure modal is visible before API call\n      await new Promise(resolve => setTimeout(resolve, 200));\n\n      // Set timer for try again button (10 seconds)\n      setTimeout(() => {\n        setShowTryAgain(true);\n      }, 10000);\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      setPaymentStatus('📤 Sending payment request to ZenoPay...');\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        var _response$data;\n        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');\n        console.log('💳 Payment response:', response);\n        console.log('🆔 Order ID:', response.order_id);\n\n        // Show confirmation message to user\n        message.success({\n          content: `💳 Payment initiated! 📱 Check your phone (${user.phoneNumber}) for SMS confirmation from ZenoPay.`,\n          duration: 8,\n          style: {\n            marginTop: '20vh'\n          }\n        });\n\n        // Start checking payment status immediately\n        const orderIdToCheck = response.order_id || ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.order_id) || 'demo_order';\n        console.log('🔍 Starting payment confirmation check for order:', orderIdToCheck);\n        checkPaymentConfirmation(orderIdToCheck);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('❌ Payment failed:', error);\n      setShowProcessingModal(false);\n      message.error('Payment failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const checkPaymentConfirmation = async orderId => {\n    console.log('🚀 Starting payment confirmation check for order:', orderId);\n    let isPolling = true;\n    let handleVisibilityChange;\n    try {\n      setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n\n      // Poll payment status every 2 seconds for optimal responsiveness\n      let attempts = 0;\n      const maxAttempts = 150; // 150 attempts * 2 seconds = 5 minutes\n\n      const pollPaymentStatus = async () => {\n        attempts++;\n        console.log(`🔍 Payment status check attempt ${attempts}/${maxAttempts} for order:`, orderId);\n        try {\n          const statusResponse = await checkPaymentStatus({\n            orderId\n          });\n          console.log('📊 Payment status response:', statusResponse);\n          console.log('🔍 Checking payment conditions:');\n          console.log('  - Live payment:', (statusResponse === null || statusResponse === void 0 ? void 0 : statusResponse.paymentStatus) === 'paid' && (statusResponse === null || statusResponse === void 0 ? void 0 : statusResponse.status) === 'active');\n          console.log('  - Demo payment:', (statusResponse === null || statusResponse === void 0 ? void 0 : statusResponse.status) === 'completed' && (statusResponse === null || statusResponse === void 0 ? void 0 : statusResponse.success) === true);\n          if (statusResponse && (statusResponse.paymentStatus === 'paid' && statusResponse.status === 'active' || statusResponse.status === 'completed' && statusResponse.success === true)) {\n            // Payment confirmed immediately!\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('🎉 Payment confirmed! Activating your subscription...');\n            console.log('✅ Payment confirmed, preparing to show success modal...');\n\n            // Show success INSTANTLY - no delay\n            console.log('🔄 Setting modal states - Processing: false, Success: true');\n            setShowProcessingModal(false);\n            setShowSuccessModal(true);\n            setPaymentLoading(null);\n            console.log('✅ Success modal state set to true');\n\n            // Refresh subscription data\n            checkCurrentSubscription();\n\n            // Show immediate success message\n            message.success({\n              content: '🎉 Payment confirmed! All features are now unlocked!',\n              duration: 5,\n              style: {\n                marginTop: '20vh',\n                fontSize: '16px'\n              }\n            });\n\n            // Start countdown for auto-navigation to hub\n            setAutoNavigateCountdown(5);\n            const countdownInterval = setInterval(() => {\n              setAutoNavigateCountdown(prev => {\n                if (prev <= 1) {\n                  clearInterval(countdownInterval);\n                  console.log('🏠 Auto-navigating to hub after successful payment...');\n                  setShowSuccessModal(false);\n                  window.location.href = '/user/hub';\n                  return null;\n                }\n                return prev - 1;\n              });\n            }, 1000);\n          } else if (attempts >= maxAttempts) {\n            // Timeout - but don't fail completely\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('⏰ Still waiting for confirmation. Please complete the payment on your phone.');\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setPaymentLoading(null); // Reset to null\n              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status or try again.');\n            }, 2000);\n          } else {\n            // Continue polling - NO TIME INDICATION, just encouraging message\n            setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n            setTimeout(pollPaymentStatus, 2000); // Check every 2 seconds for better performance\n          }\n        } catch (error) {\n          console.error('Payment status check error:', error);\n\n          // Handle specific error types\n          if (error.message && error.message.includes('404')) {\n            console.error('❌ Payment status endpoint not found (404)');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Payment verification service is temporarily unavailable. Please contact support or check your subscription status manually.');\n            return;\n          }\n          if (error.message && error.message.includes('401')) {\n            console.error('❌ Authentication required for payment status check');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Please login again to check payment status.');\n            return;\n          }\n          if (attempts >= maxAttempts) {\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Unable to confirm payment status. Please check your subscription status manually.');\n          } else {\n            // Continue polling even if there's an error (unless it's a critical error)\n            setTimeout(pollPaymentStatus, 1000);\n          }\n        }\n      };\n\n      // Add visibility change listener to check immediately when user returns to tab\n      handleVisibilityChange = () => {\n        if (!document.hidden && isPolling) {\n          console.log('User returned to tab, checking payment status immediately...');\n          setPaymentStatus('🔍 Checking payment status...');\n          // Trigger immediate check\n          setTimeout(() => pollPaymentStatus(), 100);\n        }\n      };\n      document.addEventListener('visibilitychange', handleVisibilityChange);\n\n      // Start polling immediately (no delay) - check right away\n      setTimeout(pollPaymentStatus, 500); // Start checking after 0.5 seconds\n    } catch (error) {\n      isPolling = false; // Stop polling\n      if (handleVisibilityChange) {\n        document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n      }\n\n      setShowProcessingModal(false);\n      message.error('Payment confirmation failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    if ((user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'expired' || subscriptionData && subscriptionData.status === 'expired') {\n      return 'expired';\n    }\n    return 'none';\n  };\n  const formatDate = dateString => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const getDaysRemaining = () => {\n    if (!(subscriptionData !== null && subscriptionData !== void 0 && subscriptionData.endDate)) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n  const subscriptionStatus = getSubscriptionStatus();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"subscription-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subscription-container\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"subscription-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            console.log('🧪 Testing success modal...');\n            setSelectedPlan(plans[0] || {\n              title: 'Test Plan',\n              duration: 1,\n              discountedPrice: 13000\n            });\n            setShowSuccessModal(true);\n          },\n          style: {\n            position: 'fixed',\n            top: '10px',\n            right: '10px',\n            background: '#52c41a',\n            color: 'white',\n            border: 'none',\n            padding: '8px 16px',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            zIndex: 9999\n          },\n          children: \"\\uD83E\\uDDEA Test Success Modal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"page-title\",\n          children: [/*#__PURE__*/_jsxDEV(FaCrown, {\n            className: \"title-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 13\n          }, this), \"Subscription Management\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"page-subtitle\",\n          children: \"Manage your subscription and access premium features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        className: \"current-subscription\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Current Subscription\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 586,\n          columnNumber: 11\n        }, this), subscriptionStatus === 'active' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card active\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n              className: \"status-icon active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Active Subscription\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCrown, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Plan: \", (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData$act = subscriptionData.activePlan) === null || _subscriptionData$act === void 0 ? void 0 : _subscriptionData$act.title) || 'Premium Plan']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Expires: \", formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 599,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Days Remaining: \", getDaysRemaining()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 589,\n          columnNumber: 13\n        }, this), subscriptionStatus === 'expired' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card expired\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n              className: \"status-icon expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Subscription Expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Expired: \", formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"renewal-message\",\n              children: \"Your subscription has expired. Choose a new plan below to continue accessing premium features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 612,\n          columnNumber: 13\n        }, this), subscriptionStatus === 'none' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card none\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaUser, {\n              className: \"status-icon none\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Free Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"upgrade-message\",\n              children: \"You're currently using a free account. Upgrade to a premium plan to unlock all features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 630,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"available-plans\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: subscriptionStatus === 'active' ? '🚀 Upgrade Your Plan' : subscriptionStatus === 'expired' ? '🔄 Renew Your Subscription' : '🎯 Choose Your Plan'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '10px',\n            justifyContent: 'center',\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: testProcessingModal,\n            style: {\n              background: '#ff6b6b',\n              color: 'white',\n              border: 'none',\n              padding: '8px 16px',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\uD83E\\uDDEA Test Processing Modal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: testSuccessModal,\n            style: {\n              background: '#51cf66',\n              color: 'white',\n              border: 'none',\n              padding: '8px 16px',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\uD83E\\uDDEA Test Success Modal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 676,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle\",\n          children: subscriptionStatus === 'active' ? 'Upgrade to a longer plan for better value and extended access' : subscriptionStatus === 'expired' ? 'Your subscription has expired. Renew now to continue accessing premium features' : 'Select a subscription plan to unlock all premium features and start your learning journey'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 691,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 702,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading plans...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 701,\n          columnNumber: 13\n        }, this) : plans.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-plans-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-plans-icon\",\n            children: \"\\uD83D\\uDCCB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Plans Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 708,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Plans are currently being loaded. Please refresh the page or try again later.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 709,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"refresh-btn\",\n            onClick: fetchPlans,\n            children: \"\\uD83D\\uDD04 Refresh Plans\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 706,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plans-grid\",\n          children: plans.map(plan => {\n            var _plan$title, _plan$discountedPrice, _plan$actualPrice, _plan$features;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: \"plan-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"plan-title\",\n                  children: plan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 21\n                }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('standard')) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"plan-badge\",\n                  children: \"\\uD83D\\uDD25 Popular\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 726,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-pricing\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price-display\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"current-price\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"currency\",\n                      children: \"TZS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 733,\n                      columnNumber: 25\n                    }, this), (_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 732,\n                    columnNumber: 23\n                  }, this), plan.actualPrice > plan.discountedPrice && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"original-price\",\n                      children: [(_plan$actualPrice = plan.actualPrice) === null || _plan$actualPrice === void 0 ? void 0 : _plan$actualPrice.toLocaleString(), \" TZS\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 738,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"discount-badge\",\n                      children: [Math.round((plan.actualPrice - plan.discountedPrice) / plan.actualPrice * 100), \"% OFF\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 739,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"plan-duration\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"duration-highlight\",\n                    children: plan.duration\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 746,\n                    columnNumber: 23\n                  }, this), \" month\", plan.duration > 1 ? 's' : '', \" access\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 745,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-features\",\n                children: (_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.slice(0, 5).map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                    className: \"feature-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 753,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: feature\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 754,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 752,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"select-plan-btn\",\n                onClick: e => handlePlanSelect(plan, e.currentTarget),\n                disabled: paymentLoading === plan._id,\n                style: {\n                  background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '12px',\n                  padding: '1rem 1.5rem',\n                  fontSize: '1rem',\n                  fontWeight: '600',\n                  cursor: paymentLoading === plan._id ? 'not-allowed' : 'pointer',\n                  transition: 'all 0.3s ease',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: '0.5rem',\n                  width: '100%',\n                  opacity: paymentLoading === plan._id ? 0.6 : 1\n                },\n                onMouseEnter: e => {\n                  if (paymentLoading !== plan._id) {\n                    e.target.style.background = 'linear-gradient(135deg, #1d4ed8, #1e40af)';\n                    e.target.style.transform = 'translateY(-2px)';\n                    e.target.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.4)';\n                  }\n                },\n                onMouseLeave: e => {\n                  if (paymentLoading !== plan._id) {\n                    e.target.style.background = 'linear-gradient(135deg, #3b82f6, #1d4ed8)';\n                    e.target.style.transform = 'translateY(0)';\n                    e.target.style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.3)';\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(FaCreditCard, {\n                  className: \"btn-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 795,\n                  columnNumber: 21\n                }, this), paymentLoading === plan._id ? 'Processing...' : subscriptionStatus === 'active' ? 'Click to Upgrade' : subscriptionStatus === 'expired' ? 'Click to Renew' : 'Click to Pay']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 19\n              }, this)]\n            }, plan._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 715,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 645,\n        columnNumber: 9\n      }, this), (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.6\n        },\n        className: \"phone-warning\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"warning-content\",\n          children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n            className: \"warning-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Phone Number Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 822,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Please update your phone number in your profile to subscribe to a plan.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 823,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"update-phone-btn\",\n              onClick: () => window.location.href = '/profile',\n              children: \"Update Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 824,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 821,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 819,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 813,\n        columnNumber: 11\n      }, this), showProcessingModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-modal-overlay\",\n        onClick: e => e.target === e.currentTarget && handleCloseProcessingModal(),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-modal-container processing\",\n          style: {\n            position: 'absolute',\n            top: modalPosition.top,\n            left: modalPosition.left,\n            transform: modalPosition.transform || 'none',\n            margin: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"modal-close-btn\",\n            onClick: handleCloseProcessingModal,\n            \"aria-label\": \"Close modal\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M18 6L6 18M6 6L18 18\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 858,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 857,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 852,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-header processing\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"processing-icon\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"spinner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"payment-icon\",\n                width: \"32\",\n                height: \"32\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M2 12C2 8.229 2 6.343 3.172 5.172C4.343 4 6.229 4 10 4H14C17.771 4 19.657 4 20.828 5.172C22 6.343 22 8.229 22 12C22 15.771 22 17.657 20.828 18.828C19.657 20 17.771 20 14 20H10C6.229 20 4.343 20 3.172 18.828C2 17.657 2 15.771 2 12Z\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"1.5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 867,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M10 16H6\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"1.5\",\n                  strokeLinecap: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 868,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M14 16H12.5\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"1.5\",\n                  strokeLinecap: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 869,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M2 10L22 10\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"1.5\",\n                  strokeLinecap: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 870,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 866,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 864,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Processing Payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Secure transaction in progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 874,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 863,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"status-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"status-indicator processing\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 881,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"status-text\",\n                children: paymentStatus\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 880,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-info-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 887,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Amount\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 890,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : (_selectedPlan$discoun = selectedPlan.discountedPrice) === null || _selectedPlan$discoun === void 0 ? void 0 : _selectedPlan$discoun.toLocaleString(), \" TZS\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 891,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 889,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Duration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 894,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration, \" month\", (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration) > 1 ? 's' : '']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 895,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 893,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 888,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 886,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"instruction-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"instruction-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"20\",\n                  height: \"20\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M16.5562 12.9062L16.1007 13.359C16.1007 13.359 15.0181 14.4355 12.0631 11.4972C9.10812 8.55901 10.1907 7.48257 10.1907 7.48257L10.4775 7.19738C11.1841 6.49484 11.2507 5.36691 10.6342 4.54348L9.37326 2.85908C8.61028 1.83992 7.13596 1.70529 6.26145 2.57483L4.69185 4.13552C4.25823 4.56668 3.96765 5.12559 4.00289 5.74561C4.09304 7.33182 4.81071 10.7447 8.81536 14.7266C13.0621 18.9492 17.0468 19.117 18.6763 18.9651C19.1917 18.9171 19.6399 18.6546 20.0011 18.2954L21.4217 16.883C22.3806 15.9295 22.1102 14.2949 20.8833 13.628L18.9728 12.5894C18.1672 12.1515 17.1858 12.2801 16.5562 12.9062Z\",\n                    fill: \"currentColor\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 904,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 903,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Check Your Phone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 906,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 902,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"phone-number\",\n                children: user === null || user === void 0 ? void 0 : user.phoneNumber\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 908,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"instruction-steps\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step\",\n                  children: \"1. You'll receive an SMS with payment instructions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 910,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step\",\n                  children: \"2. Follow the SMS steps to confirm payment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 911,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step\",\n                  children: \"3. Complete the mobile money transaction\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 912,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 909,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 901,\n              columnNumber: 17\n            }, this), showTryAgain && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"try-again-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Taking longer than expected?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 919,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"try-again-btn\",\n                onClick: handleTryAgain,\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"16\",\n                  height: \"16\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M4 12a8 8 0 018-8V2.5\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 922,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 4L9 7L12 10\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 923,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 921,\n                  columnNumber: 23\n                }, this), \"Try Again\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 920,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 918,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 841,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 837,\n        columnNumber: 11\n      }, this), showSuccessModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"payment-modal-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-modal-container success\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"modal-close-btn\",\n            onClick: () => {\n              setAutoNavigateCountdown(null);\n              setShowSuccessModal(false);\n            },\n            \"aria-label\": \"Close modal\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"24\",\n              height: \"24\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M18 6L6 18M6 6L18 18\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 950,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 949,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 941,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-header success\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-icon\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                width: \"48\",\n                height: \"48\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z\",\n                  fill: \"#22c55e\",\n                  fillOpacity: \"0.2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 958,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M16 9L10.5 14.5L8 12\",\n                  stroke: \"#22c55e\",\n                  strokeWidth: \"2\",\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 959,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: \"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z\",\n                  stroke: \"#22c55e\",\n                  strokeWidth: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 960,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 957,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 956,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Payment Successful!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 963,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [\"Welcome to \", selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title, \"!\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 964,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 955,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modal-content\",\n            children: [autoNavigateCountdown && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"countdown-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"countdown-icon\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"20\",\n                  height: \"20\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\",\n                    strokeLinecap: \"round\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 973,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                    cx: \"12\",\n                    cy: \"12\",\n                    r: \"3\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 974,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 972,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 971,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: [\"Redirecting to Hub in \", autoNavigateCountdown, \" seconds...\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 970,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plan-summary-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Subscription Activated\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 983,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Plan\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 986,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 987,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 985,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Duration\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 990,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration, \" month\", (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration) > 1 ? 's' : '']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 991,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 989,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Amount Paid\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 994,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : (_selectedPlan$discoun2 = selectedPlan.discountedPrice) === null || _selectedPlan$discoun2 === void 0 ? void 0 : _selectedPlan$discoun2.toLocaleString(), \" TZS\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 995,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 993,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row status\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 998,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"status-badge\",\n                    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                      width: \"16\",\n                      height: \"16\",\n                      viewBox: \"0 0 24 24\",\n                      fill: \"none\",\n                      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                        d: \"M9 12L11 14L15 10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\",\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1001,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"9\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1002,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1000,\n                      columnNumber: 25\n                    }, this), \"Active\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 999,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 997,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 984,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 982,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"features-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\uD83D\\uDE80 Premium Features Unlocked\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1012,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"features-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12L11 14L15 10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1016,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1017,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1015,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Unlimited Quizzes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1019,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1014,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12L11 14L15 10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1023,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1024,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1022,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"AI Assistant\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1026,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1021,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12L11 14L15 10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1030,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1031,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1029,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Study Materials\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1033,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1028,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12L11 14L15 10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1037,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1038,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1036,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Progress Tracking\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1040,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1035,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12L11 14L15 10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1044,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1045,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1043,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Learning Videos\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1047,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1042,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    width: \"20\",\n                    height: \"20\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M9 12L11 14L15 10\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\",\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1051,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n                      cx: \"12\",\n                      cy: \"12\",\n                      r: \"9\",\n                      stroke: \"currentColor\",\n                      strokeWidth: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1052,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1050,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Forum Access\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1054,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1049,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1013,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1011,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"modal-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"primary-btn\",\n                onClick: () => {\n                  setAutoNavigateCountdown(null);\n                  setShowSuccessModal(false);\n                  window.location.href = '/user/hub';\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  width: \"20\",\n                  height: \"20\",\n                  viewBox: \"0 0 24 24\",\n                  fill: \"none\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1070,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"polyline\", {\n                    points: \"9,22 9,12 15,12 15,22\",\n                    stroke: \"currentColor\",\n                    strokeWidth: \"2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1071,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1069,\n                  columnNumber: 21\n                }, this), \"Continue to Hub \", autoNavigateCountdown ? `(${autoNavigateCountdown}s)` : '']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1061,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"secondary-btn\",\n                onClick: () => {\n                  setAutoNavigateCountdown(null);\n                  setShowSuccessModal(false);\n                },\n                children: \"Close\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1075,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1060,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 967,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 939,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 938,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(UpgradeRestrictionModal, {\n        visible: showUpgradeRestriction,\n        onClose: () => setShowUpgradeRestriction(false),\n        currentPlan: plans.find(p => p._id === (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.activePlan)) || (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.plan),\n        subscription: subscriptionData,\n        user: user\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1093,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SubscriptionExpiredModal, {\n        visible: showExpiredModal,\n        onClose: () => setShowExpiredModal(false),\n        onRenew: handleRenewSubscription,\n        subscription: subscriptionData,\n        user: user,\n        plans: plans\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1102,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 542,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 541,\n    columnNumber: 5\n  }, this);\n};\n_s(Subscription, \"CANsWiMcO/cpoC5WGNIcvE0Z+mM=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c = Subscription;\nexport default Subscription;\nvar _c;\n$RefreshReg$(_c, \"Subscription\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "motion", "message", "FaCrown", "FaCalendarAlt", "FaCheckCircle", "FaTimesCircle", "FaCreditCard", "FaUser", "getPlans", "addPayment", "checkPaymentStatus", "ShowLoading", "HideLoading", "UpgradeRestrictionModal", "SubscriptionExpiredModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Subscription", "_s", "_subscriptionData$act", "_selectedPlan$discoun", "_selectedPlan$discoun2", "plans", "setPlans", "loading", "setLoading", "paymentLoading", "setPaymentLoading", "showProcessingModal", "setShowProcessingModal", "console", "log", "showSuccessModal", "setShowSuccessModal", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "paymentStatus", "setPaymentStatus", "showUpgradeRestriction", "setShowUpgradeRestriction", "showExpiredModal", "setShowExpiredModal", "processingStartTime", "setProcessingStartTime", "showTryAgain", "setShowTryAgain", "autoNavigateCountdown", "setAutoNavigateCountdown", "modalPosition", "setModalPosition", "top", "left", "user", "state", "subscriptionData", "subscription", "dispatch", "samplePlans", "_id", "title", "features", "actualPrice", "discountedPrice", "discountPercentage", "duration", "status", "fetchPlans", "checkCurrentSubscription", "document", "body", "style", "overflow", "detectScrollableContent", "modalContents", "querySelectorAll", "for<PERSON>ach", "content", "scrollHeight", "clientHeight", "classList", "add", "remove", "setTimeout", "window", "addEventListener", "removeEventListener", "isSubscriptionExpired", "response", "success", "data", "length", "Array", "isArray", "warn", "info", "error", "warning", "endDate", "Date", "today", "setHours", "handleRenewSubscription", "handlePlanSelect", "handleCloseProcessingModal", "handleTryAgain", "testSuccessModal", "testProcessingModal", "plan", "buttonElement", "phoneNumber", "test", "buttonRect", "getBoundingClientRect", "viewportHeight", "innerHeight", "viewportWidth", "innerWidth", "modalTop", "Math", "max", "modalLeft", "min", "transform", "_user$name", "now", "Promise", "resolve", "paymentData", "userId", "userPhone", "userEmail", "email", "name", "replace", "toLowerCase", "_response$data", "order_id", "marginTop", "orderIdToCheck", "checkPaymentConfirmation", "Error", "orderId", "isPolling", "handleVisibilityChange", "attempts", "maxAttempts", "pollPaymentStatus", "statusResponse", "fontSize", "countdownInterval", "setInterval", "prev", "clearInterval", "location", "href", "includes", "hidden", "getSubscriptionStatus", "subscriptionStatus", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "getDaysRemaining", "diffTime", "diffDays", "ceil", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "onClick", "position", "right", "background", "color", "border", "padding", "borderRadius", "cursor", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "activePlan", "display", "gap", "justifyContent", "marginBottom", "map", "_plan$title", "_plan$discountedPrice", "_plan$actualPrice", "_plan$features", "whileHover", "scale", "whileTap", "toLocaleString", "round", "slice", "feature", "index", "e", "currentTarget", "disabled", "fontWeight", "alignItems", "width", "onMouseEnter", "target", "boxShadow", "onMouseLeave", "margin", "height", "viewBox", "fill", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "fillOpacity", "cx", "cy", "r", "points", "visible", "onClose", "currentPlan", "find", "p", "onRenew", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Subscription/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport UpgradeRestrictionModal from '../../../components/UpgradeRestrictionModal/UpgradeRestrictionModal';\nimport SubscriptionExpiredModal from '../../../components/SubscriptionExpiredModal/SubscriptionExpiredModal';\nimport './Subscription.css';\n\nconst Subscription = () => {\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(null); // Changed to store plan ID instead of boolean\n  const [showProcessingModal, setShowProcessingModal] = useState(false);\n\n  // Debug: Log showProcessingModal state changes\n  useEffect(() => {\n    console.log('🔍 showProcessingModal state changed to:', showProcessingModal);\n  }, [showProcessingModal]);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [paymentStatus, setPaymentStatus] = useState('');\n  const [showUpgradeRestriction, setShowUpgradeRestriction] = useState(false);\n  const [showExpiredModal, setShowExpiredModal] = useState(false);\n  const [processingStartTime, setProcessingStartTime] = useState(null);\n  const [showTryAgain, setShowTryAgain] = useState(false);\n  const [autoNavigateCountdown, setAutoNavigateCountdown] = useState(null);\n  const [modalPosition, setModalPosition] = useState({ top: '50%', left: '50%' });\n  const { user } = useSelector((state) => state.user);\n  const { subscriptionData } = useSelector((state) => state.subscription);\n  const dispatch = useDispatch();\n\n  // Fallback sample plans in case API fails\n  const samplePlans = [\n    {\n      _id: \"basic-plan-sample\",\n      title: \"Basic Membership\",\n      features: [\n        \"2-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\"\n      ],\n      actualPrice: 28570,\n      discountedPrice: 20000,\n      discountPercentage: 30,\n      duration: 2,\n      status: true\n    },\n    {\n      _id: \"premium-plan-sample\",\n      title: \"Premium Plan\",\n      features: [\n        \"3-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\",\n        \"Priority support\"\n      ],\n      actualPrice: 45000,\n      discountedPrice: 35000,\n      discountPercentage: 22,\n      duration: 3,\n      status: true\n    }\n  ];\n\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n  }, []);\n\n  // Handle body scroll lock when modals are open (simplified approach)\n  useEffect(() => {\n    if (showProcessingModal || showSuccessModal) {\n      // Simply prevent body scroll without position fixed\n      document.body.style.overflow = 'hidden';\n    } else {\n      // Restore body scroll\n      document.body.style.overflow = '';\n    }\n\n    // Cleanup on unmount\n    return () => {\n      document.body.style.overflow = '';\n    };\n  }, [showProcessingModal, showSuccessModal]);\n\n  // Enhanced scroll detection for modal content\n  useEffect(() => {\n    const detectScrollableContent = () => {\n      const modalContents = document.querySelectorAll('.modal-content');\n      modalContents.forEach(content => {\n        if (content.scrollHeight > content.clientHeight) {\n          content.classList.add('has-scroll');\n        } else {\n          content.classList.remove('has-scroll');\n        }\n      });\n    };\n\n    // Detect on modal open\n    if (showProcessingModal || showSuccessModal) {\n      // Small delay to ensure modal is rendered\n      setTimeout(detectScrollableContent, 100);\n\n      // Re-detect on window resize\n      window.addEventListener('resize', detectScrollableContent);\n\n      return () => {\n        window.removeEventListener('resize', detectScrollableContent);\n      };\n    }\n  }, [showProcessingModal, showSuccessModal]);\n\n  // Check for expired subscription and show modal\n  useEffect(() => {\n    if (subscriptionData && isSubscriptionExpired()) {\n      console.log('🚫 Subscription expired, showing modal');\n      setShowExpiredModal(true);\n    } else {\n      setShowExpiredModal(false);\n    }\n  }, [subscriptionData]);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      console.log('Fetching plans...');\n      const response = await getPlans();\n      console.log('Plans response:', response);\n\n      if (response.success && response.data && response.data.length > 0) {\n        setPlans(response.data);\n        console.log('Plans loaded successfully from API:', response.data);\n      } else if (Array.isArray(response) && response.length > 0) {\n        // Handle case where response is directly an array of plans\n        setPlans(response);\n        console.log('Plans loaded as array from API:', response);\n      } else {\n        console.warn('No plans from API, using sample plans');\n        setPlans(samplePlans);\n        message.info('Showing sample plans. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('Error loading plans from API:', error);\n      console.log('Using fallback sample plans');\n      setPlans(samplePlans);\n      message.warning('Using sample plans. Please check your connection and try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n      console.log('Current subscription:', response);\n    } catch (error) {\n      console.log('No active subscription found');\n    }\n  };\n\n  // Check if subscription is expired\n  const isSubscriptionExpired = () => {\n    if (!subscriptionData) return true;\n\n    // If no subscription data, consider expired\n    if (!subscriptionData.endDate) return true;\n\n    // If payment status is not paid, consider expired\n    if (subscriptionData.paymentStatus !== 'paid') return true;\n\n    // If status is not active, consider expired\n    if (subscriptionData.status !== 'active') return true;\n\n    // Check if end date has passed\n    const endDate = new Date(subscriptionData.endDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n    endDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    return endDate < today;\n  };\n\n  // Handle subscription renewal from expired modal\n  const handleRenewSubscription = async (selectedPlan) => {\n    setShowExpiredModal(false);\n    await handlePlanSelect(selectedPlan);\n  };\n\n  // Handle closing payment processing modal\n  const handleCloseProcessingModal = () => {\n    setShowProcessingModal(false);\n    setPaymentLoading(null); // Reset to null instead of false\n    setShowTryAgain(false);\n    setProcessingStartTime(null);\n    setPaymentStatus('');\n    message.info('Payment process cancelled. You can try again anytime.');\n  };\n\n  // Handle try again functionality\n  const handleTryAgain = () => {\n    if (selectedPlan) {\n      setShowTryAgain(false);\n      setProcessingStartTime(null);\n      handlePlanSelect(selectedPlan);\n    }\n  };\n\n  // Test success modal (for debugging)\n  const testSuccessModal = () => {\n    console.log('🧪 Testing success modal...');\n    setShowProcessingModal(false);\n    setShowSuccessModal(true);\n    setPaymentLoading(null);\n  };\n\n  // Test processing modal (for debugging)\n  const testProcessingModal = () => {\n    console.log('🧪 Testing processing modal...');\n    setShowProcessingModal(true);\n    setPaymentStatus('Testing processing modal...');\n    setSelectedPlan(plans[0] || { title: 'Test Plan', discountedPrice: 5000, duration: 1 });\n  };\n\n  const handlePlanSelect = async (plan, buttonElement = null) => {\n    // Check if user already has an active subscription\n    if (subscriptionData && subscriptionData.status === 'active' && subscriptionData.paymentStatus === 'paid') {\n      console.log('🚫 User already has active subscription:', subscriptionData);\n      setShowUpgradeRestriction(true);\n      return;\n    }\n\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n\n    // Calculate modal position based on button position\n    if (buttonElement) {\n      const buttonRect = buttonElement.getBoundingClientRect();\n      const viewportHeight = window.innerHeight;\n      const viewportWidth = window.innerWidth;\n\n      // Position modal above the button\n      const modalTop = Math.max(50, buttonRect.top - 50); // 50px above button, minimum 50px from top\n      const modalLeft = Math.min(Math.max(50, buttonRect.left), viewportWidth - 400); // Keep within viewport\n\n      setModalPosition({\n        top: `${modalTop}px`,\n        left: `${modalLeft}px`,\n        transform: 'none' // Remove default centering\n      });\n    } else {\n      // Default center position\n      setModalPosition({\n        top: '50%',\n        left: '50%',\n        transform: 'translate(-50%, -50%)'\n      });\n    }\n\n    try {\n      console.log('🚀 Starting payment for plan:', plan.title);\n      console.log('🔧 IMMEDIATELY showing processing modal...');\n\n      // IMMEDIATELY show processing modal when user chooses plan\n      setSelectedPlan(plan);\n      setPaymentLoading(plan._id);\n      setShowProcessingModal(true);\n      setShowTryAgain(false);\n      setProcessingStartTime(Date.now());\n      setPaymentStatus('🚀 Preparing your payment request...');\n\n      console.log('✅ Processing modal IMMEDIATELY displayed');\n\n      // Small delay to ensure modal is visible before API call\n      await new Promise(resolve => setTimeout(resolve, 200));\n\n      // Set timer for try again button (10 seconds)\n      setTimeout(() => {\n        setShowTryAgain(true);\n      }, 10000);\n\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n\n      setPaymentStatus('📤 Sending payment request to ZenoPay...');\n      const response = await addPayment(paymentData);\n\n      if (response.success) {\n        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');\n\n        console.log('💳 Payment response:', response);\n        console.log('🆔 Order ID:', response.order_id);\n\n        // Show confirmation message to user\n        message.success({\n          content: `💳 Payment initiated! 📱 Check your phone (${user.phoneNumber}) for SMS confirmation from ZenoPay.`,\n          duration: 8,\n          style: {\n            marginTop: '20vh',\n          }\n        });\n\n        // Start checking payment status immediately\n        const orderIdToCheck = response.order_id || response.data?.order_id || 'demo_order';\n        console.log('🔍 Starting payment confirmation check for order:', orderIdToCheck);\n\n        checkPaymentConfirmation(orderIdToCheck);\n\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('❌ Payment failed:', error);\n      setShowProcessingModal(false);\n      message.error('Payment failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const checkPaymentConfirmation = async (orderId) => {\n    console.log('🚀 Starting payment confirmation check for order:', orderId);\n    let isPolling = true;\n    let handleVisibilityChange;\n\n    try {\n      setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n\n      // Poll payment status every 2 seconds for optimal responsiveness\n      let attempts = 0;\n      const maxAttempts = 150; // 150 attempts * 2 seconds = 5 minutes\n\n      const pollPaymentStatus = async () => {\n        attempts++;\n        console.log(`🔍 Payment status check attempt ${attempts}/${maxAttempts} for order:`, orderId);\n\n        try {\n          const statusResponse = await checkPaymentStatus({ orderId });\n          console.log('📊 Payment status response:', statusResponse);\n          console.log('🔍 Checking payment conditions:');\n          console.log('  - Live payment:', statusResponse?.paymentStatus === 'paid' && statusResponse?.status === 'active');\n          console.log('  - Demo payment:', statusResponse?.status === 'completed' && statusResponse?.success === true);\n\n          if (statusResponse && (\n            (statusResponse.paymentStatus === 'paid' && statusResponse.status === 'active') ||\n            (statusResponse.status === 'completed' && statusResponse.success === true)\n          )) {\n            // Payment confirmed immediately!\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('🎉 Payment confirmed! Activating your subscription...');\n            console.log('✅ Payment confirmed, preparing to show success modal...');\n\n            // Show success INSTANTLY - no delay\n            console.log('🔄 Setting modal states - Processing: false, Success: true');\n            setShowProcessingModal(false);\n            setShowSuccessModal(true);\n            setPaymentLoading(null);\n            console.log('✅ Success modal state set to true');\n\n            // Refresh subscription data\n            checkCurrentSubscription();\n\n            // Show immediate success message\n            message.success({\n              content: '🎉 Payment confirmed! All features are now unlocked!',\n              duration: 5,\n              style: {\n                marginTop: '20vh',\n                fontSize: '16px'\n              }\n            });\n\n            // Start countdown for auto-navigation to hub\n            setAutoNavigateCountdown(5);\n            const countdownInterval = setInterval(() => {\n              setAutoNavigateCountdown(prev => {\n                if (prev <= 1) {\n                  clearInterval(countdownInterval);\n                  console.log('🏠 Auto-navigating to hub after successful payment...');\n                  setShowSuccessModal(false);\n                  window.location.href = '/user/hub';\n                  return null;\n                }\n                return prev - 1;\n              });\n            }, 1000);\n\n          } else if (attempts >= maxAttempts) {\n            // Timeout - but don't fail completely\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('⏰ Still waiting for confirmation. Please complete the payment on your phone.');\n\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setPaymentLoading(null); // Reset to null\n              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status or try again.');\n            }, 2000);\n\n          } else {\n            // Continue polling - NO TIME INDICATION, just encouraging message\n            setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n            setTimeout(pollPaymentStatus, 2000); // Check every 2 seconds for better performance\n          }\n\n        } catch (error) {\n          console.error('Payment status check error:', error);\n\n          // Handle specific error types\n          if (error.message && error.message.includes('404')) {\n            console.error('❌ Payment status endpoint not found (404)');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Payment verification service is temporarily unavailable. Please contact support or check your subscription status manually.');\n            return;\n          }\n\n          if (error.message && error.message.includes('401')) {\n            console.error('❌ Authentication required for payment status check');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Please login again to check payment status.');\n            return;\n          }\n\n          if (attempts >= maxAttempts) {\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Unable to confirm payment status. Please check your subscription status manually.');\n          } else {\n            // Continue polling even if there's an error (unless it's a critical error)\n            setTimeout(pollPaymentStatus, 1000);\n          }\n        }\n      };\n\n      // Add visibility change listener to check immediately when user returns to tab\n      handleVisibilityChange = () => {\n        if (!document.hidden && isPolling) {\n          console.log('User returned to tab, checking payment status immediately...');\n          setPaymentStatus('🔍 Checking payment status...');\n          // Trigger immediate check\n          setTimeout(() => pollPaymentStatus(), 100);\n        }\n      };\n\n      document.addEventListener('visibilitychange', handleVisibilityChange);\n\n      // Start polling immediately (no delay) - check right away\n      setTimeout(pollPaymentStatus, 500); // Start checking after 0.5 seconds\n\n    } catch (error) {\n      isPolling = false; // Stop polling\n      if (handleVisibilityChange) {\n        document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n      }\n      setShowProcessingModal(false);\n      message.error('Payment confirmation failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    \n    if (user?.subscriptionStatus === 'expired' || (subscriptionData && subscriptionData.status === 'expired')) {\n      return 'expired';\n    }\n    \n    return 'none';\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const getDaysRemaining = () => {\n    if (!subscriptionData?.endDate) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n\n  const subscriptionStatus = getSubscriptionStatus();\n\n  return (\n    <div className=\"subscription-page\">\n      <div className=\"subscription-container\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"subscription-header\"\n        >\n          {/* Debug button - remove in production */}\n          <button\n            onClick={() => {\n              console.log('🧪 Testing success modal...');\n              setSelectedPlan(plans[0] || { title: 'Test Plan', duration: 1, discountedPrice: 13000 });\n              setShowSuccessModal(true);\n            }}\n            style={{\n              position: 'fixed',\n              top: '10px',\n              right: '10px',\n              background: '#52c41a',\n              color: 'white',\n              border: 'none',\n              padding: '8px 16px',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              zIndex: 9999\n            }}\n          >\n            🧪 Test Success Modal\n          </button>\n          <h1 className=\"page-title\">\n            <FaCrown className=\"title-icon\" />\n            Subscription Management\n          </h1>\n          <p className=\"page-subtitle\">Manage your subscription and access premium features</p>\n        </motion.div>\n\n        {/* Current Subscription Status */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"current-subscription\"\n        >\n          <h2 className=\"section-title\">Current Subscription</h2>\n          \n          {subscriptionStatus === 'active' && (\n            <div className=\"subscription-card active\">\n              <div className=\"subscription-status\">\n                <FaCheckCircle className=\"status-icon active\" />\n                <span className=\"status-text\">Active Subscription</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCrown className=\"detail-icon\" />\n                  <span>Plan: {subscriptionData?.activePlan?.title || 'Premium Plan'}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expires: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCheckCircle className=\"detail-icon\" />\n                  <span>Days Remaining: {getDaysRemaining()}</span>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'expired' && (\n            <div className=\"subscription-card expired\">\n              <div className=\"subscription-status\">\n                <FaTimesCircle className=\"status-icon expired\" />\n                <span className=\"status-text\">Subscription Expired</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expired: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <p className=\"renewal-message\">\n                  Your subscription has expired. Choose a new plan below to continue accessing premium features.\n                </p>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'none' && (\n            <div className=\"subscription-card none\">\n              <div className=\"subscription-status\">\n                <FaUser className=\"status-icon none\" />\n                <span className=\"status-text\">Free Account</span>\n              </div>\n              <div className=\"subscription-details\">\n                <p className=\"upgrade-message\">\n                  You're currently using a free account. Upgrade to a premium plan to unlock all features.\n                </p>\n              </div>\n            </div>\n          )}\n        </motion.div>\n\n        {/* Available Plans */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"available-plans\"\n        >\n          <h2 className=\"section-title\">\n            {subscriptionStatus === 'active'\n              ? '🚀 Upgrade Your Plan'\n              : subscriptionStatus === 'expired'\n                ? '🔄 Renew Your Subscription'\n                : '🎯 Choose Your Plan'\n            }\n          </h2>\n\n          {/* Temporary Test Buttons */}\n          <div style={{ display: 'flex', gap: '10px', justifyContent: 'center', marginBottom: '20px' }}>\n            <button\n              onClick={testProcessingModal}\n              style={{\n                background: '#ff6b6b',\n                color: 'white',\n                border: 'none',\n                padding: '8px 16px',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              🧪 Test Processing Modal\n            </button>\n            <button\n              onClick={testSuccessModal}\n              style={{\n                background: '#51cf66',\n                color: 'white',\n                border: 'none',\n                padding: '8px 16px',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              🧪 Test Success Modal\n            </button>\n          </div>\n          <p className=\"section-subtitle\">\n            {subscriptionStatus === 'active'\n              ? 'Upgrade to a longer plan for better value and extended access'\n              : subscriptionStatus === 'expired'\n                ? 'Your subscription has expired. Renew now to continue accessing premium features'\n                : 'Select a subscription plan to unlock all premium features and start your learning journey'\n            }\n          </p>\n          \n          {loading ? (\n            <div className=\"loading-state\">\n              <div className=\"spinner\"></div>\n              <p>Loading plans...</p>\n            </div>\n          ) : plans.length === 0 ? (\n            <div className=\"no-plans-state\">\n              <div className=\"no-plans-icon\">📋</div>\n              <h3>No Plans Available</h3>\n              <p>Plans are currently being loaded. Please refresh the page or try again later.</p>\n              <button className=\"refresh-btn\" onClick={fetchPlans}>\n                🔄 Refresh Plans\n              </button>\n            </div>\n          ) : (\n            <div className=\"plans-grid\">\n              {plans.map((plan) => (\n                <motion.div\n                  key={plan._id}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  className=\"plan-card\"\n                >\n                  <div className=\"plan-header\">\n                    <h3 className=\"plan-title\">{plan.title}</h3>\n                    {plan.title?.toLowerCase().includes('standard') && (\n                      <span className=\"plan-badge\">🔥 Popular</span>\n                    )}\n                  </div>\n                  \n                  <div className=\"plan-pricing\">\n                    <div className=\"price-display\">\n                      <div className=\"current-price\">\n                        <span className=\"currency\">TZS</span>\n                        {plan.discountedPrice?.toLocaleString()}\n                      </div>\n                      {plan.actualPrice > plan.discountedPrice && (\n                        <>\n                          <span className=\"original-price\">{plan.actualPrice?.toLocaleString()} TZS</span>\n                          <span className=\"discount-badge\">\n                            {Math.round(((plan.actualPrice - plan.discountedPrice) / plan.actualPrice) * 100)}% OFF\n                          </span>\n                        </>\n                      )}\n                    </div>\n                    <div className=\"plan-duration\">\n                      <span className=\"duration-highlight\">{plan.duration}</span> month{plan.duration > 1 ? 's' : ''} access\n                    </div>\n                  </div>\n\n                  <div className=\"plan-features\">\n                    {plan.features?.slice(0, 5).map((feature, index) => (\n                      <div key={index} className=\"feature-item\">\n                        <FaCheckCircle className=\"feature-icon\" />\n                        <span>{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n\n                  <button\n                    className=\"select-plan-btn\"\n                    onClick={(e) => handlePlanSelect(plan, e.currentTarget)}\n                    disabled={paymentLoading === plan._id}\n                    style={{\n                      background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '12px',\n                      padding: '1rem 1.5rem',\n                      fontSize: '1rem',\n                      fontWeight: '600',\n                      cursor: paymentLoading === plan._id ? 'not-allowed' : 'pointer',\n                      transition: 'all 0.3s ease',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      gap: '0.5rem',\n                      width: '100%',\n                      opacity: paymentLoading === plan._id ? 0.6 : 1\n                    }}\n                    onMouseEnter={(e) => {\n                      if (paymentLoading !== plan._id) {\n                        e.target.style.background = 'linear-gradient(135deg, #1d4ed8, #1e40af)';\n                        e.target.style.transform = 'translateY(-2px)';\n                        e.target.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.4)';\n                      }\n                    }}\n                    onMouseLeave={(e) => {\n                      if (paymentLoading !== plan._id) {\n                        e.target.style.background = 'linear-gradient(135deg, #3b82f6, #1d4ed8)';\n                        e.target.style.transform = 'translateY(0)';\n                        e.target.style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.3)';\n                      }\n                    }}\n                  >\n                    <FaCreditCard className=\"btn-icon\" />\n                    {paymentLoading === plan._id\n                      ? 'Processing...'\n                      : subscriptionStatus === 'active'\n                        ? 'Click to Upgrade'\n                        : subscriptionStatus === 'expired'\n                          ? 'Click to Renew'\n                          : 'Click to Pay'\n                    }\n                  </button>\n                </motion.div>\n              ))}\n            </div>\n          )}\n        </motion.div>\n\n        {/* Phone Number Warning */}\n        {(!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            className=\"phone-warning\"\n          >\n            <div className=\"warning-content\">\n              <FaTimesCircle className=\"warning-icon\" />\n              <div>\n                <h4>Phone Number Required</h4>\n                <p>Please update your phone number in your profile to subscribe to a plan.</p>\n                <button \n                  className=\"update-phone-btn\"\n                  onClick={() => window.location.href = '/profile'}\n                >\n                  Update Phone Number\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Professional Payment Processing Modal */}\n        {showProcessingModal && (\n          <div\n            className=\"payment-modal-overlay\"\n            onClick={(e) => e.target === e.currentTarget && handleCloseProcessingModal()}\n          >\n            <div\n              className=\"payment-modal-container processing\"\n              style={{\n                position: 'absolute',\n                top: modalPosition.top,\n                left: modalPosition.left,\n                transform: modalPosition.transform || 'none',\n                margin: 0\n              }}\n            >\n              {/* Close Button */}\n              <button\n                className=\"modal-close-btn\"\n                onClick={handleCloseProcessingModal}\n                aria-label=\"Close modal\"\n              >\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                  <path d=\"M18 6L6 18M6 6L18 18\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                </svg>\n              </button>\n\n              {/* Header */}\n              <div className=\"modal-header processing\">\n                <div className=\"processing-icon\">\n                  <div className=\"spinner\"></div>\n                  <svg className=\"payment-icon\" width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"none\">\n                    <path d=\"M2 12C2 8.229 2 6.343 3.172 5.172C4.343 4 6.229 4 10 4H14C17.771 4 19.657 4 20.828 5.172C22 6.343 22 8.229 22 12C22 15.771 22 17.657 20.828 18.828C19.657 20 17.771 20 14 20H10C6.229 20 4.343 20 3.172 18.828C2 17.657 2 15.771 2 12Z\" stroke=\"currentColor\" strokeWidth=\"1.5\"/>\n                    <path d=\"M10 16H6\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\"/>\n                    <path d=\"M14 16H12.5\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\"/>\n                    <path d=\"M2 10L22 10\" stroke=\"currentColor\" strokeWidth=\"1.5\" strokeLinecap=\"round\"/>\n                  </svg>\n                </div>\n                <h2>Processing Payment</h2>\n                <p>Secure transaction in progress</p>\n              </div>\n\n              {/* Content */}\n              <div className=\"modal-content\">\n                {/* Status */}\n                <div className=\"status-card\">\n                  <div className=\"status-indicator processing\"></div>\n                  <p className=\"status-text\">{paymentStatus}</p>\n                </div>\n\n                {/* Plan Info */}\n                <div className=\"plan-info-card\">\n                  <h3>{selectedPlan?.title}</h3>\n                  <div className=\"plan-details\">\n                    <div className=\"detail-row\">\n                      <span>Amount</span>\n                      <strong>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</strong>\n                    </div>\n                    <div className=\"detail-row\">\n                      <span>Duration</span>\n                      <strong>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</strong>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Phone Instructions */}\n                <div className=\"instruction-card\">\n                  <div className=\"instruction-header\">\n                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M16.5562 12.9062L16.1007 13.359C16.1007 13.359 15.0181 14.4355 12.0631 11.4972C9.10812 8.55901 10.1907 7.48257 10.1907 7.48257L10.4775 7.19738C11.1841 6.49484 11.2507 5.36691 10.6342 4.54348L9.37326 2.85908C8.61028 1.83992 7.13596 1.70529 6.26145 2.57483L4.69185 4.13552C4.25823 4.56668 3.96765 5.12559 4.00289 5.74561C4.09304 7.33182 4.81071 10.7447 8.81536 14.7266C13.0621 18.9492 17.0468 19.117 18.6763 18.9651C19.1917 18.9171 19.6399 18.6546 20.0011 18.2954L21.4217 16.883C22.3806 15.9295 22.1102 14.2949 20.8833 13.628L18.9728 12.5894C18.1672 12.1515 17.1858 12.2801 16.5562 12.9062Z\" fill=\"currentColor\"/>\n                    </svg>\n                    <span>Check Your Phone</span>\n                  </div>\n                  <div className=\"phone-number\">{user?.phoneNumber}</div>\n                  <div className=\"instruction-steps\">\n                    <div className=\"step\">1. You'll receive an SMS with payment instructions</div>\n                    <div className=\"step\">2. Follow the SMS steps to confirm payment</div>\n                    <div className=\"step\">3. Complete the mobile money transaction</div>\n                  </div>\n                </div>\n\n                {/* Try Again */}\n                {showTryAgain && (\n                  <div className=\"try-again-card\">\n                    <p>Taking longer than expected?</p>\n                    <button className=\"try-again-btn\" onClick={handleTryAgain}>\n                      <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M4 12a8 8 0 018-8V2.5\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                        <path d=\"M12 4L9 7L12 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                      </svg>\n                      Try Again\n                    </button>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n\n\n        {/* Professional Success Modal */}\n        {showSuccessModal && (\n          <div className=\"payment-modal-overlay\">\n            <div className=\"payment-modal-container success\">\n              {/* Close Button */}\n              <button\n                className=\"modal-close-btn\"\n                onClick={() => {\n                  setAutoNavigateCountdown(null);\n                  setShowSuccessModal(false);\n                }}\n                aria-label=\"Close modal\"\n              >\n                <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\">\n                  <path d=\"M18 6L6 18M6 6L18 18\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                </svg>\n              </button>\n\n              {/* Header */}\n              <div className=\"modal-header success\">\n                <div className=\"success-icon\">\n                  <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\">\n                    <path d=\"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z\" fill=\"#22c55e\" fillOpacity=\"0.2\"/>\n                    <path d=\"M16 9L10.5 14.5L8 12\" stroke=\"#22c55e\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                    <path d=\"M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z\" stroke=\"#22c55e\" strokeWidth=\"2\"/>\n                  </svg>\n                </div>\n                <h2>Payment Successful!</h2>\n                <p>Welcome to {selectedPlan?.title}!</p>\n              </div>\n              {/* Content */}\n              <div className=\"modal-content\">\n                {/* Auto-Navigation Notice */}\n                {autoNavigateCountdown && (\n                  <div className=\"countdown-card\">\n                    <div className=\"countdown-icon\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M12 2V6M12 18V22M4.93 4.93L7.76 7.76M16.24 16.24L19.07 19.07M2 12H6M18 12H22M4.93 19.07L7.76 16.24M16.24 7.76L19.07 4.93\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                    </div>\n                    <p>Redirecting to Hub in {autoNavigateCountdown} seconds...</p>\n                  </div>\n                )}\n\n                {/* Plan Summary */}\n                <div className=\"plan-summary-card\">\n                  <h3>Subscription Activated</h3>\n                  <div className=\"plan-details\">\n                    <div className=\"detail-row\">\n                      <span>Plan</span>\n                      <strong>{selectedPlan?.title}</strong>\n                    </div>\n                    <div className=\"detail-row\">\n                      <span>Duration</span>\n                      <strong>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</strong>\n                    </div>\n                    <div className=\"detail-row\">\n                      <span>Amount Paid</span>\n                      <strong>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</strong>\n                    </div>\n                    <div className=\"detail-row status\">\n                      <span>Status</span>\n                      <div className=\"status-badge\">\n                        <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                          <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                          <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                        </svg>\n                        Active\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Features Unlocked */}\n                <div className=\"features-card\">\n                  <h3>🚀 Premium Features Unlocked</h3>\n                  <div className=\"features-grid\">\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Unlimited Quizzes</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>AI Assistant</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Study Materials</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Progress Tracking</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Learning Videos</span>\n                    </div>\n                    <div className=\"feature-item\">\n                      <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                        <path d=\"M9 12L11 14L15 10\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                        <circle cx=\"12\" cy=\"12\" r=\"9\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      </svg>\n                      <span>Forum Access</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Action Buttons */}\n                <div className=\"modal-actions\">\n                  <button\n                    className=\"primary-btn\"\n                    onClick={() => {\n                      setAutoNavigateCountdown(null);\n                      setShowSuccessModal(false);\n                      window.location.href = '/user/hub';\n                    }}\n                  >\n                    <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                      <path d=\"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                      <polyline points=\"9,22 9,12 15,12 15,22\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n                    </svg>\n                    Continue to Hub {autoNavigateCountdown ? `(${autoNavigateCountdown}s)` : ''}\n                  </button>\n                  <button\n                    className=\"secondary-btn\"\n                    onClick={() => {\n                      setAutoNavigateCountdown(null);\n                      setShowSuccessModal(false);\n                    }}\n                  >\n                    Close\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n\n\n        {/* Upgrade Restriction Modal */}\n        <UpgradeRestrictionModal\n          visible={showUpgradeRestriction}\n          onClose={() => setShowUpgradeRestriction(false)}\n          currentPlan={plans.find(p => p._id === subscriptionData?.activePlan) || subscriptionData?.plan}\n          subscription={subscriptionData}\n          user={user}\n        />\n\n        {/* Subscription Expired Modal */}\n        <SubscriptionExpiredModal\n          visible={showExpiredModal}\n          onClose={() => setShowExpiredModal(false)}\n          onRenew={handleRenewSubscription}\n          subscription={subscriptionData}\n          user={user}\n          plans={plans}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default Subscription;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,OAAO,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,EAAEC,YAAY,EAAEC,MAAM,QAAQ,gBAAgB;AAC3G,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,2BAA2B;AAC1E,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,uBAAuB,MAAM,qEAAqE;AACzG,OAAOC,wBAAwB,MAAM,uEAAuE;AAC5G,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACzB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACkC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACAC,SAAS,CAAC,MAAM;IACdmC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEH,mBAAmB,CAAC;EAC9E,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;EACzB,MAAM,CAACI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4C,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoD,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC;IAAEwD,GAAG,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAM,CAAC,CAAC;EAC/E,MAAM;IAAEC;EAAK,CAAC,GAAGxD,WAAW,CAAEyD,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE;EAAiB,CAAC,GAAG1D,WAAW,CAAEyD,KAAK,IAAKA,KAAK,CAACE,YAAY,CAAC;EACvE,MAAMC,QAAQ,GAAG3D,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM4D,WAAW,GAAG,CAClB;IACEC,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,kBAAkB;IACzBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,CAC/B;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,EACD;IACEP,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,EAC9B,kBAAkB,CACnB;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,CACF;EAEDtE,SAAS,CAAC,MAAM;IACduE,UAAU,CAAC,CAAC;IACZC,wBAAwB,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxE,SAAS,CAAC,MAAM;IACd,IAAIiC,mBAAmB,IAAII,gBAAgB,EAAE;MAC3C;MACAoC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC,CAAC,MAAM;MACL;MACAH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;IACnC;;IAEA;IACA,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;IACnC,CAAC;EACH,CAAC,EAAE,CAAC3C,mBAAmB,EAAEI,gBAAgB,CAAC,CAAC;;EAE3C;EACArC,SAAS,CAAC,MAAM;IACd,MAAM6E,uBAAuB,GAAGA,CAAA,KAAM;MACpC,MAAMC,aAAa,GAAGL,QAAQ,CAACM,gBAAgB,CAAC,gBAAgB,CAAC;MACjED,aAAa,CAACE,OAAO,CAACC,OAAO,IAAI;QAC/B,IAAIA,OAAO,CAACC,YAAY,GAAGD,OAAO,CAACE,YAAY,EAAE;UAC/CF,OAAO,CAACG,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;QACrC,CAAC,MAAM;UACLJ,OAAO,CAACG,SAAS,CAACE,MAAM,CAAC,YAAY,CAAC;QACxC;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,IAAIrD,mBAAmB,IAAII,gBAAgB,EAAE;MAC3C;MACAkD,UAAU,CAACV,uBAAuB,EAAE,GAAG,CAAC;;MAExC;MACAW,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEZ,uBAAuB,CAAC;MAE1D,OAAO,MAAM;QACXW,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEb,uBAAuB,CAAC;MAC/D,CAAC;IACH;EACF,CAAC,EAAE,CAAC5C,mBAAmB,EAAEI,gBAAgB,CAAC,CAAC;;EAE3C;EACArC,SAAS,CAAC,MAAM;IACd,IAAI2D,gBAAgB,IAAIgC,qBAAqB,CAAC,CAAC,EAAE;MAC/CxD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrDU,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM;MACLA,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC,EAAE,CAACa,gBAAgB,CAAC,CAAC;EAEtB,MAAMY,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFzC,UAAU,CAAC,IAAI,CAAC;MAChBK,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,MAAMwD,QAAQ,GAAG,MAAMjF,QAAQ,CAAC,CAAC;MACjCwB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEwD,QAAQ,CAAC;MAExC,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACjEnE,QAAQ,CAACgE,QAAQ,CAACE,IAAI,CAAC;QACvB3D,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEwD,QAAQ,CAACE,IAAI,CAAC;MACnE,CAAC,MAAM,IAAIE,KAAK,CAACC,OAAO,CAACL,QAAQ,CAAC,IAAIA,QAAQ,CAACG,MAAM,GAAG,CAAC,EAAE;QACzD;QACAnE,QAAQ,CAACgE,QAAQ,CAAC;QAClBzD,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEwD,QAAQ,CAAC;MAC1D,CAAC,MAAM;QACLzD,OAAO,CAAC+D,IAAI,CAAC,uCAAuC,CAAC;QACrDtE,QAAQ,CAACkC,WAAW,CAAC;QACrB1D,OAAO,CAAC+F,IAAI,CAAC,qDAAqD,CAAC;MACrE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdjE,OAAO,CAACiE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDjE,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1CR,QAAQ,CAACkC,WAAW,CAAC;MACrB1D,OAAO,CAACiG,OAAO,CAAC,iEAAiE,CAAC;IACpF,CAAC,SAAS;MACRvE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0C,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACF,MAAMoB,QAAQ,GAAG,MAAM/E,kBAAkB,CAAC,CAAC;MAC3CsB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEwD,QAAQ,CAAC;IAChD,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdjE,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC7C;EACF,CAAC;;EAED;EACA,MAAMuD,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAAChC,gBAAgB,EAAE,OAAO,IAAI;;IAElC;IACA,IAAI,CAACA,gBAAgB,CAAC2C,OAAO,EAAE,OAAO,IAAI;;IAE1C;IACA,IAAI3C,gBAAgB,CAAClB,aAAa,KAAK,MAAM,EAAE,OAAO,IAAI;;IAE1D;IACA,IAAIkB,gBAAgB,CAACW,MAAM,KAAK,QAAQ,EAAE,OAAO,IAAI;;IAErD;IACA,MAAMgC,OAAO,GAAG,IAAIC,IAAI,CAAC5C,gBAAgB,CAAC2C,OAAO,CAAC;IAClD,MAAME,KAAK,GAAG,IAAID,IAAI,CAAC,CAAC;IACxBC,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5BH,OAAO,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAE9B,OAAOH,OAAO,GAAGE,KAAK;EACxB,CAAC;;EAED;EACA,MAAME,uBAAuB,GAAG,MAAOnE,YAAY,IAAK;IACtDO,mBAAmB,CAAC,KAAK,CAAC;IAC1B,MAAM6D,gBAAgB,CAACpE,YAAY,CAAC;EACtC,CAAC;;EAED;EACA,MAAMqE,0BAA0B,GAAGA,CAAA,KAAM;IACvC1E,sBAAsB,CAAC,KAAK,CAAC;IAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IACzBkB,eAAe,CAAC,KAAK,CAAC;IACtBF,sBAAsB,CAAC,IAAI,CAAC;IAC5BN,gBAAgB,CAAC,EAAE,CAAC;IACpBtC,OAAO,CAAC+F,IAAI,CAAC,uDAAuD,CAAC;EACvE,CAAC;;EAED;EACA,MAAMU,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAItE,YAAY,EAAE;MAChBW,eAAe,CAAC,KAAK,CAAC;MACtBF,sBAAsB,CAAC,IAAI,CAAC;MAC5B2D,gBAAgB,CAACpE,YAAY,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAMuE,gBAAgB,GAAGA,CAAA,KAAM;IAC7B3E,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC1CF,sBAAsB,CAAC,KAAK,CAAC;IAC7BI,mBAAmB,CAAC,IAAI,CAAC;IACzBN,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM+E,mBAAmB,GAAGA,CAAA,KAAM;IAChC5E,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7CF,sBAAsB,CAAC,IAAI,CAAC;IAC5BQ,gBAAgB,CAAC,6BAA6B,CAAC;IAC/CF,eAAe,CAACb,KAAK,CAAC,CAAC,CAAC,IAAI;MAAEqC,KAAK,EAAE,WAAW;MAAEG,eAAe,EAAE,IAAI;MAAEE,QAAQ,EAAE;IAAE,CAAC,CAAC;EACzF,CAAC;EAED,MAAMsC,gBAAgB,GAAG,MAAAA,CAAOK,IAAI,EAAEC,aAAa,GAAG,IAAI,KAAK;IAC7D;IACA,IAAItD,gBAAgB,IAAIA,gBAAgB,CAACW,MAAM,KAAK,QAAQ,IAAIX,gBAAgB,CAAClB,aAAa,KAAK,MAAM,EAAE;MACzGN,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEuB,gBAAgB,CAAC;MACzEf,yBAAyB,CAAC,IAAI,CAAC;MAC/B;IACF;IAEA,IAAI,CAACa,IAAI,CAACyD,WAAW,IAAI,CAAC,gBAAgB,CAACC,IAAI,CAAC1D,IAAI,CAACyD,WAAW,CAAC,EAAE;MACjE9G,OAAO,CAACgG,KAAK,CAAC,oEAAoE,CAAC;MACnF;IACF;;IAEA;IACA,IAAIa,aAAa,EAAE;MACjB,MAAMG,UAAU,GAAGH,aAAa,CAACI,qBAAqB,CAAC,CAAC;MACxD,MAAMC,cAAc,GAAG9B,MAAM,CAAC+B,WAAW;MACzC,MAAMC,aAAa,GAAGhC,MAAM,CAACiC,UAAU;;MAEvC;MACA,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAER,UAAU,CAAC7D,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;MACpD,MAAMsE,SAAS,GAAGF,IAAI,CAACG,GAAG,CAACH,IAAI,CAACC,GAAG,CAAC,EAAE,EAAER,UAAU,CAAC5D,IAAI,CAAC,EAAEgE,aAAa,GAAG,GAAG,CAAC,CAAC,CAAC;;MAEhFlE,gBAAgB,CAAC;QACfC,GAAG,EAAG,GAAEmE,QAAS,IAAG;QACpBlE,IAAI,EAAG,GAAEqE,SAAU,IAAG;QACtBE,SAAS,EAAE,MAAM,CAAC;MACpB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAzE,gBAAgB,CAAC;QACfC,GAAG,EAAE,KAAK;QACVC,IAAI,EAAE,KAAK;QACXuE,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;IAEA,IAAI;MAAA,IAAAC,UAAA;MACF7F,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE4E,IAAI,CAAChD,KAAK,CAAC;MACxD7B,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;;MAEzD;MACAI,eAAe,CAACwE,IAAI,CAAC;MACrBhF,iBAAiB,CAACgF,IAAI,CAACjD,GAAG,CAAC;MAC3B7B,sBAAsB,CAAC,IAAI,CAAC;MAC5BgB,eAAe,CAAC,KAAK,CAAC;MACtBF,sBAAsB,CAACuD,IAAI,CAAC0B,GAAG,CAAC,CAAC,CAAC;MAClCvF,gBAAgB,CAAC,sCAAsC,CAAC;MAExDP,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;MAEvD;MACA,MAAM,IAAI8F,OAAO,CAACC,OAAO,IAAI5C,UAAU,CAAC4C,OAAO,EAAE,GAAG,CAAC,CAAC;;MAEtD;MACA5C,UAAU,CAAC,MAAM;QACfrC,eAAe,CAAC,IAAI,CAAC;MACvB,CAAC,EAAE,KAAK,CAAC;MAET,MAAMkF,WAAW,GAAG;QAClBpB,IAAI,EAAEA,IAAI;QACVqB,MAAM,EAAE5E,IAAI,CAACM,GAAG;QAChBuE,SAAS,EAAE7E,IAAI,CAACyD,WAAW;QAC3BqB,SAAS,EAAE9E,IAAI,CAAC+E,KAAK,IAAK,IAAAR,UAAA,GAAEvE,IAAI,CAACgF,IAAI,cAAAT,UAAA,uBAATA,UAAA,CAAWU,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC3E,CAAC;MAEDjG,gBAAgB,CAAC,0CAA0C,CAAC;MAC5D,MAAMkD,QAAQ,GAAG,MAAMhF,UAAU,CAACwH,WAAW,CAAC;MAE9C,IAAIxC,QAAQ,CAACC,OAAO,EAAE;QAAA,IAAA+C,cAAA;QACpBlG,gBAAgB,CAAC,wDAAwD,CAAC;QAE1EP,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEwD,QAAQ,CAAC;QAC7CzD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEwD,QAAQ,CAACiD,QAAQ,CAAC;;QAE9C;QACAzI,OAAO,CAACyF,OAAO,CAAC;UACdZ,OAAO,EAAG,8CAA6CxB,IAAI,CAACyD,WAAY,sCAAqC;UAC7G7C,QAAQ,EAAE,CAAC;UACXM,KAAK,EAAE;YACLmE,SAAS,EAAE;UACb;QACF,CAAC,CAAC;;QAEF;QACA,MAAMC,cAAc,GAAGnD,QAAQ,CAACiD,QAAQ,MAAAD,cAAA,GAAIhD,QAAQ,CAACE,IAAI,cAAA8C,cAAA,uBAAbA,cAAA,CAAeC,QAAQ,KAAI,YAAY;QACnF1G,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE2G,cAAc,CAAC;QAEhFC,wBAAwB,CAACD,cAAc,CAAC;MAE1C,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAACrD,QAAQ,CAACxF,OAAO,IAAI,gBAAgB,CAAC;MACvD;IACF,CAAC,CAAC,OAAOgG,KAAK,EAAE;MACdjE,OAAO,CAACiE,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzClE,sBAAsB,CAAC,KAAK,CAAC;MAC7B9B,OAAO,CAACgG,KAAK,CAAC,kBAAkB,GAAGA,KAAK,CAAChG,OAAO,CAAC;MACjD4B,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3B;EACF,CAAC;;EAED,MAAMgH,wBAAwB,GAAG,MAAOE,OAAO,IAAK;IAClD/G,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE8G,OAAO,CAAC;IACzE,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,sBAAsB;IAE1B,IAAI;MACF1G,gBAAgB,CAAC,0EAA0E,CAAC;;MAE5F;MACA,IAAI2G,QAAQ,GAAG,CAAC;MAChB,MAAMC,WAAW,GAAG,GAAG,CAAC,CAAC;;MAEzB,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;QACpCF,QAAQ,EAAE;QACVlH,OAAO,CAACC,GAAG,CAAE,mCAAkCiH,QAAS,IAAGC,WAAY,aAAY,EAAEJ,OAAO,CAAC;QAE7F,IAAI;UACF,MAAMM,cAAc,GAAG,MAAM3I,kBAAkB,CAAC;YAAEqI;UAAQ,CAAC,CAAC;UAC5D/G,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEoH,cAAc,CAAC;UAC1DrH,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9CD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,CAAAoH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE/G,aAAa,MAAK,MAAM,IAAI,CAAA+G,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAElF,MAAM,MAAK,QAAQ,CAAC;UACjHnC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,CAAAoH,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAElF,MAAM,MAAK,WAAW,IAAI,CAAAkF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE3D,OAAO,MAAK,IAAI,CAAC;UAE5G,IAAI2D,cAAc,KACfA,cAAc,CAAC/G,aAAa,KAAK,MAAM,IAAI+G,cAAc,CAAClF,MAAM,KAAK,QAAQ,IAC7EkF,cAAc,CAAClF,MAAM,KAAK,WAAW,IAAIkF,cAAc,CAAC3D,OAAO,KAAK,IAAK,CAC3E,EAAE;YACD;YACAsD,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1B3E,QAAQ,CAACiB,mBAAmB,CAAC,kBAAkB,EAAE0D,sBAAsB,CAAC,CAAC,CAAC;YAC5E;;YAEA1G,gBAAgB,CAAC,uDAAuD,CAAC;YACzEP,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;;YAEtE;YACAD,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;YACzEF,sBAAsB,CAAC,KAAK,CAAC;YAC7BI,mBAAmB,CAAC,IAAI,CAAC;YACzBN,iBAAiB,CAAC,IAAI,CAAC;YACvBG,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;YAEhD;YACAoC,wBAAwB,CAAC,CAAC;;YAE1B;YACApE,OAAO,CAACyF,OAAO,CAAC;cACdZ,OAAO,EAAE,sDAAsD;cAC/DZ,QAAQ,EAAE,CAAC;cACXM,KAAK,EAAE;gBACLmE,SAAS,EAAE,MAAM;gBACjBW,QAAQ,EAAE;cACZ;YACF,CAAC,CAAC;;YAEF;YACArG,wBAAwB,CAAC,CAAC,CAAC;YAC3B,MAAMsG,iBAAiB,GAAGC,WAAW,CAAC,MAAM;cAC1CvG,wBAAwB,CAACwG,IAAI,IAAI;gBAC/B,IAAIA,IAAI,IAAI,CAAC,EAAE;kBACbC,aAAa,CAACH,iBAAiB,CAAC;kBAChCvH,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;kBACpEE,mBAAmB,CAAC,KAAK,CAAC;kBAC1BkD,MAAM,CAACsE,QAAQ,CAACC,IAAI,GAAG,WAAW;kBAClC,OAAO,IAAI;gBACb;gBACA,OAAOH,IAAI,GAAG,CAAC;cACjB,CAAC,CAAC;YACJ,CAAC,EAAE,IAAI,CAAC;UAEV,CAAC,MAAM,IAAIP,QAAQ,IAAIC,WAAW,EAAE;YAClC;YACAH,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1B3E,QAAQ,CAACiB,mBAAmB,CAAC,kBAAkB,EAAE0D,sBAAsB,CAAC,CAAC,CAAC;YAC5E;;YAEA1G,gBAAgB,CAAC,8EAA8E,CAAC;YAEhG6C,UAAU,CAAC,MAAM;cACfrD,sBAAsB,CAAC,KAAK,CAAC;cAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;cACzB5B,OAAO,CAACiG,OAAO,CAAC,0GAA0G,CAAC;YAC7H,CAAC,EAAE,IAAI,CAAC;UAEV,CAAC,MAAM;YACL;YACA3D,gBAAgB,CAAC,0EAA0E,CAAC;YAC5F6C,UAAU,CAACgE,iBAAiB,EAAE,IAAI,CAAC,CAAC,CAAC;UACvC;QAEF,CAAC,CAAC,OAAOnD,KAAK,EAAE;UACdjE,OAAO,CAACiE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;UAEnD;UACA,IAAIA,KAAK,CAAChG,OAAO,IAAIgG,KAAK,CAAChG,OAAO,CAAC4J,QAAQ,CAAC,KAAK,CAAC,EAAE;YAClD7H,OAAO,CAACiE,KAAK,CAAC,2CAA2C,CAAC;YAC1D+C,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1B3E,QAAQ,CAACiB,mBAAmB,CAAC,kBAAkB,EAAE0D,sBAAsB,CAAC;YAC1E;YACAlH,sBAAsB,CAAC,KAAK,CAAC;YAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;YACzB5B,OAAO,CAACgG,KAAK,CAAC,6HAA6H,CAAC;YAC5I;UACF;UAEA,IAAIA,KAAK,CAAChG,OAAO,IAAIgG,KAAK,CAAChG,OAAO,CAAC4J,QAAQ,CAAC,KAAK,CAAC,EAAE;YAClD7H,OAAO,CAACiE,KAAK,CAAC,oDAAoD,CAAC;YACnE+C,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1B3E,QAAQ,CAACiB,mBAAmB,CAAC,kBAAkB,EAAE0D,sBAAsB,CAAC;YAC1E;YACAlH,sBAAsB,CAAC,KAAK,CAAC;YAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;YACzB5B,OAAO,CAACgG,KAAK,CAAC,6CAA6C,CAAC;YAC5D;UACF;UAEA,IAAIiD,QAAQ,IAAIC,WAAW,EAAE;YAC3BH,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1B3E,QAAQ,CAACiB,mBAAmB,CAAC,kBAAkB,EAAE0D,sBAAsB,CAAC,CAAC,CAAC;YAC5E;;YACAlH,sBAAsB,CAAC,KAAK,CAAC;YAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;YACzB5B,OAAO,CAACgG,KAAK,CAAC,mFAAmF,CAAC;UACpG,CAAC,MAAM;YACL;YACAb,UAAU,CAACgE,iBAAiB,EAAE,IAAI,CAAC;UACrC;QACF;MACF,CAAC;;MAED;MACAH,sBAAsB,GAAGA,CAAA,KAAM;QAC7B,IAAI,CAAC3E,QAAQ,CAACwF,MAAM,IAAId,SAAS,EAAE;UACjChH,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;UAC3EM,gBAAgB,CAAC,+BAA+B,CAAC;UACjD;UACA6C,UAAU,CAAC,MAAMgE,iBAAiB,CAAC,CAAC,EAAE,GAAG,CAAC;QAC5C;MACF,CAAC;MAED9E,QAAQ,CAACgB,gBAAgB,CAAC,kBAAkB,EAAE2D,sBAAsB,CAAC;;MAErE;MACA7D,UAAU,CAACgE,iBAAiB,EAAE,GAAG,CAAC,CAAC,CAAC;IAEtC,CAAC,CAAC,OAAOnD,KAAK,EAAE;MACd+C,SAAS,GAAG,KAAK,CAAC,CAAC;MACnB,IAAIC,sBAAsB,EAAE;QAC1B3E,QAAQ,CAACiB,mBAAmB,CAAC,kBAAkB,EAAE0D,sBAAsB,CAAC,CAAC,CAAC;MAC5E;;MACAlH,sBAAsB,CAAC,KAAK,CAAC;MAC7B9B,OAAO,CAACgG,KAAK,CAAC,+BAA+B,GAAGA,KAAK,CAAChG,OAAO,CAAC;MAC9D4B,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3B;EACF,CAAC;;EAED,MAAMkI,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAIvG,gBAAgB,IAAIA,gBAAgB,CAAClB,aAAa,KAAK,MAAM,IAAIkB,gBAAgB,CAACW,MAAM,KAAK,QAAQ,EAAE;MACzG,MAAMgC,OAAO,GAAG,IAAIC,IAAI,CAAC5C,gBAAgB,CAAC2C,OAAO,CAAC;MAClD,MAAM2B,GAAG,GAAG,IAAI1B,IAAI,CAAC,CAAC;MACtB,IAAID,OAAO,GAAG2B,GAAG,EAAE;QACjB,OAAO,QAAQ;MACjB;IACF;IAEA,IAAI,CAAAxE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0G,kBAAkB,MAAK,SAAS,IAAKxG,gBAAgB,IAAIA,gBAAgB,CAACW,MAAM,KAAK,SAAU,EAAE;MACzG,OAAO,SAAS;IAClB;IAEA,OAAO,MAAM;EACf,CAAC;EAED,MAAM8F,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAI9D,IAAI,CAAC8D,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAAC/G,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAE2C,OAAO,GAAE,OAAO,CAAC;IACxC,MAAMA,OAAO,GAAG,IAAIC,IAAI,CAAC5C,gBAAgB,CAAC2C,OAAO,CAAC;IAClD,MAAM2B,GAAG,GAAG,IAAI1B,IAAI,CAAC,CAAC;IACtB,MAAMoE,QAAQ,GAAGrE,OAAO,GAAG2B,GAAG;IAC9B,MAAM2C,QAAQ,GAAGjD,IAAI,CAACkD,IAAI,CAACF,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOhD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEgD,QAAQ,CAAC;EAC9B,CAAC;EAED,MAAMT,kBAAkB,GAAGD,qBAAqB,CAAC,CAAC;EAElD,oBACE/I,OAAA;IAAK2J,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChC5J,OAAA;MAAK2J,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErC5J,OAAA,CAAChB,MAAM,CAAC6K,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEhH,QAAQ,EAAE;QAAI,CAAE;QAC9ByG,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAG/B5J,OAAA;UACEmK,OAAO,EAAEA,CAAA,KAAM;YACbnJ,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;YAC1CI,eAAe,CAACb,KAAK,CAAC,CAAC,CAAC,IAAI;cAAEqC,KAAK,EAAE,WAAW;cAAEK,QAAQ,EAAE,CAAC;cAAEF,eAAe,EAAE;YAAM,CAAC,CAAC;YACxF7B,mBAAmB,CAAC,IAAI,CAAC;UAC3B,CAAE;UACFqC,KAAK,EAAE;YACL4G,QAAQ,EAAE,OAAO;YACjBhI,GAAG,EAAE,MAAM;YACXiI,KAAK,EAAE,MAAM;YACbC,UAAU,EAAE,SAAS;YACrBC,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE,UAAU;YACnBC,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,SAAS;YACjBC,MAAM,EAAE;UACV,CAAE;UAAAhB,QAAA,EACH;QAED;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThL,OAAA;UAAI2J,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACxB5J,OAAA,CAACd,OAAO;YAACyK,SAAS,EAAC;UAAY;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2BAEpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLhL,OAAA;UAAG2J,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoD;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eAGbhL,OAAA,CAAChB,MAAM,CAAC6K,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEhH,QAAQ,EAAE,GAAG;UAAE+H,KAAK,EAAE;QAAI,CAAE;QAC1CtB,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBAEhC5J,OAAA;UAAI2J,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoB;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEtDhC,kBAAkB,KAAK,QAAQ,iBAC9BhJ,OAAA;UAAK2J,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvC5J,OAAA;YAAK2J,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC5J,OAAA,CAACZ,aAAa;cAACuK,SAAS,EAAC;YAAoB;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDhL,OAAA;cAAM2J,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAmB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNhL,OAAA;YAAK2J,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC5J,OAAA;cAAK2J,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B5J,OAAA,CAACd,OAAO;gBAACyK,SAAS,EAAC;cAAa;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnChL,OAAA;gBAAA4J,QAAA,GAAM,QAAM,EAAC,CAAApH,gBAAgB,aAAhBA,gBAAgB,wBAAAnC,qBAAA,GAAhBmC,gBAAgB,CAAE0I,UAAU,cAAA7K,qBAAA,uBAA5BA,qBAAA,CAA8BwC,KAAK,KAAI,cAAc;cAAA;gBAAAgI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACNhL,OAAA;cAAK2J,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B5J,OAAA,CAACb,aAAa;gBAACwK,SAAS,EAAC;cAAa;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzChL,OAAA;gBAAA4J,QAAA,GAAM,WAAS,EAACX,UAAU,CAACzG,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2C,OAAO,CAAC;cAAA;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNhL,OAAA;cAAK2J,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B5J,OAAA,CAACZ,aAAa;gBAACuK,SAAS,EAAC;cAAa;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzChL,OAAA;gBAAA4J,QAAA,GAAM,kBAAgB,EAACL,gBAAgB,CAAC,CAAC;cAAA;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAhC,kBAAkB,KAAK,SAAS,iBAC/BhJ,OAAA;UAAK2J,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxC5J,OAAA;YAAK2J,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC5J,OAAA,CAACX,aAAa;cAACsK,SAAS,EAAC;YAAqB;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDhL,OAAA;cAAM2J,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAoB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACNhL,OAAA;YAAK2J,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC5J,OAAA;cAAK2J,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B5J,OAAA,CAACb,aAAa;gBAACwK,SAAS,EAAC;cAAa;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzChL,OAAA;gBAAA4J,QAAA,GAAM,WAAS,EAACX,UAAU,CAACzG,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2C,OAAO,CAAC;cAAA;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNhL,OAAA;cAAG2J,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAhC,kBAAkB,KAAK,MAAM,iBAC5BhJ,OAAA;UAAK2J,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrC5J,OAAA;YAAK2J,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC5J,OAAA,CAACT,MAAM;cAACoK,SAAS,EAAC;YAAkB;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvChL,OAAA;cAAM2J,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAY;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACNhL,OAAA;YAAK2J,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnC5J,OAAA;cAAG2J,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAGbhL,OAAA,CAAChB,MAAM,CAAC6K,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEhH,QAAQ,EAAE,GAAG;UAAE+H,KAAK,EAAE;QAAI,CAAE;QAC1CtB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE3B5J,OAAA;UAAI2J,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC1BZ,kBAAkB,KAAK,QAAQ,GAC5B,sBAAsB,GACtBA,kBAAkB,KAAK,SAAS,GAC9B,4BAA4B,GAC5B;QAAqB;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEzB,CAAC,eAGLhL,OAAA;UAAKwD,KAAK,EAAE;YAAE2H,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAA1B,QAAA,gBAC3F5J,OAAA;YACEmK,OAAO,EAAEvE,mBAAoB;YAC7BpC,KAAK,EAAE;cACL8G,UAAU,EAAE,SAAS;cACrBC,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE,UAAU;cACnBC,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAE,SAAS;cACjBrC,QAAQ,EAAE;YACZ,CAAE;YAAAsB,QAAA,EACH;UAED;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThL,OAAA;YACEmK,OAAO,EAAExE,gBAAiB;YAC1BnC,KAAK,EAAE;cACL8G,UAAU,EAAE,SAAS;cACrBC,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE,UAAU;cACnBC,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAE,SAAS;cACjBrC,QAAQ,EAAE;YACZ,CAAE;YAAAsB,QAAA,EACH;UAED;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNhL,OAAA;UAAG2J,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC5BZ,kBAAkB,KAAK,QAAQ,GAC5B,+DAA+D,GAC/DA,kBAAkB,KAAK,SAAS,GAC9B,iFAAiF,GACjF;QAA2F;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEhG,CAAC,EAEHtK,OAAO,gBACNV,OAAA;UAAK2J,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B5J,OAAA;YAAK2J,SAAS,EAAC;UAAS;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/BhL,OAAA;YAAA4J,QAAA,EAAG;UAAgB;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,GACJxK,KAAK,CAACoE,MAAM,KAAK,CAAC,gBACpB5E,OAAA;UAAK2J,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7B5J,OAAA;YAAK2J,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvChL,OAAA;YAAA4J,QAAA,EAAI;UAAkB;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BhL,OAAA;YAAA4J,QAAA,EAAG;UAA6E;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpFhL,OAAA;YAAQ2J,SAAS,EAAC,aAAa;YAACQ,OAAO,EAAE/G,UAAW;YAAAwG,QAAA,EAAC;UAErD;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAENhL,OAAA;UAAK2J,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxBpJ,KAAK,CAAC+K,GAAG,CAAE1F,IAAI;YAAA,IAAA2F,WAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,cAAA;YAAA,oBACd3L,OAAA,CAAChB,MAAM,CAAC6K,GAAG;cAET+B,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BlC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAErB5J,OAAA;gBAAK2J,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B5J,OAAA;kBAAI2J,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAE/D,IAAI,CAAChD;gBAAK;kBAAAgI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC3C,EAAAQ,WAAA,GAAA3F,IAAI,CAAChD,KAAK,cAAA2I,WAAA,uBAAVA,WAAA,CAAYhE,WAAW,CAAC,CAAC,CAACqB,QAAQ,CAAC,UAAU,CAAC,kBAC7C7I,OAAA;kBAAM2J,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAU;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAENhL,OAAA;gBAAK2J,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B5J,OAAA;kBAAK2J,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5B5J,OAAA;oBAAK2J,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5B5J,OAAA;sBAAM2J,SAAS,EAAC,UAAU;sBAAAC,QAAA,EAAC;oBAAG;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,GAAAS,qBAAA,GACpC5F,IAAI,CAAC7C,eAAe,cAAAyI,qBAAA,uBAApBA,qBAAA,CAAsBM,cAAc,CAAC,CAAC;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,EACLnF,IAAI,CAAC9C,WAAW,GAAG8C,IAAI,CAAC7C,eAAe,iBACtChD,OAAA,CAAAE,SAAA;oBAAA0J,QAAA,gBACE5J,OAAA;sBAAM2J,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,IAAA8B,iBAAA,GAAE7F,IAAI,CAAC9C,WAAW,cAAA2I,iBAAA,uBAAhBA,iBAAA,CAAkBK,cAAc,CAAC,CAAC,EAAC,MAAI;oBAAA;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChFhL,OAAA;sBAAM2J,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,GAC7BpD,IAAI,CAACwF,KAAK,CAAE,CAACnG,IAAI,CAAC9C,WAAW,GAAG8C,IAAI,CAAC7C,eAAe,IAAI6C,IAAI,CAAC9C,WAAW,GAAI,GAAG,CAAC,EAAC,OACpF;oBAAA;sBAAA8H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,eACP,CACH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACNhL,OAAA;kBAAK2J,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5B5J,OAAA;oBAAM2J,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAE/D,IAAI,CAAC3C;kBAAQ;oBAAA2H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,UAAM,EAACnF,IAAI,CAAC3C,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,SACjG;gBAAA;kBAAA2H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENhL,OAAA;gBAAK2J,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAA+B,cAAA,GAC3B9F,IAAI,CAAC/C,QAAQ,cAAA6I,cAAA,uBAAbA,cAAA,CAAeM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACV,GAAG,CAAC,CAACW,OAAO,EAAEC,KAAK,kBAC7CnM,OAAA;kBAAiB2J,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACvC5J,OAAA,CAACZ,aAAa;oBAACuK,SAAS,EAAC;kBAAc;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1ChL,OAAA;oBAAA4J,QAAA,EAAOsC;kBAAO;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFdmB,KAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENhL,OAAA;gBACE2J,SAAS,EAAC,iBAAiB;gBAC3BQ,OAAO,EAAGiC,CAAC,IAAK5G,gBAAgB,CAACK,IAAI,EAAEuG,CAAC,CAACC,aAAa,CAAE;gBACxDC,QAAQ,EAAE1L,cAAc,KAAKiF,IAAI,CAACjD,GAAI;gBACtCY,KAAK,EAAE;kBACL8G,UAAU,EAAE,2CAA2C;kBACvDC,KAAK,EAAE,OAAO;kBACdC,MAAM,EAAE,MAAM;kBACdE,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,aAAa;kBACtBnC,QAAQ,EAAE,MAAM;kBAChBiE,UAAU,EAAE,KAAK;kBACjB5B,MAAM,EAAE/J,cAAc,KAAKiF,IAAI,CAACjD,GAAG,GAAG,aAAa,GAAG,SAAS;kBAC/DsH,UAAU,EAAE,eAAe;kBAC3BiB,OAAO,EAAE,MAAM;kBACfqB,UAAU,EAAE,QAAQ;kBACpBnB,cAAc,EAAE,QAAQ;kBACxBD,GAAG,EAAE,QAAQ;kBACbqB,KAAK,EAAE,MAAM;kBACb1C,OAAO,EAAEnJ,cAAc,KAAKiF,IAAI,CAACjD,GAAG,GAAG,GAAG,GAAG;gBAC/C,CAAE;gBACF8J,YAAY,EAAGN,CAAC,IAAK;kBACnB,IAAIxL,cAAc,KAAKiF,IAAI,CAACjD,GAAG,EAAE;oBAC/BwJ,CAAC,CAACO,MAAM,CAACnJ,KAAK,CAAC8G,UAAU,GAAG,2CAA2C;oBACvE8B,CAAC,CAACO,MAAM,CAACnJ,KAAK,CAACoD,SAAS,GAAG,kBAAkB;oBAC7CwF,CAAC,CAACO,MAAM,CAACnJ,KAAK,CAACoJ,SAAS,GAAG,oCAAoC;kBACjE;gBACF,CAAE;gBACFC,YAAY,EAAGT,CAAC,IAAK;kBACnB,IAAIxL,cAAc,KAAKiF,IAAI,CAACjD,GAAG,EAAE;oBAC/BwJ,CAAC,CAACO,MAAM,CAACnJ,KAAK,CAAC8G,UAAU,GAAG,2CAA2C;oBACvE8B,CAAC,CAACO,MAAM,CAACnJ,KAAK,CAACoD,SAAS,GAAG,eAAe;oBAC1CwF,CAAC,CAACO,MAAM,CAACnJ,KAAK,CAACoJ,SAAS,GAAG,oCAAoC;kBACjE;gBACF,CAAE;gBAAAhD,QAAA,gBAEF5J,OAAA,CAACV,YAAY;kBAACqK,SAAS,EAAC;gBAAU;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACpCpK,cAAc,KAAKiF,IAAI,CAACjD,GAAG,GACxB,eAAe,GACfoG,kBAAkB,KAAK,QAAQ,GAC7B,kBAAkB,GAClBA,kBAAkB,KAAK,SAAS,GAC9B,gBAAgB,GAChB,cAAc;cAAA;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CAAC;YAAA,GAtFJnF,IAAI,CAACjD,GAAG;cAAAiI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuFH,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,EAGZ,CAAC,CAAC1I,IAAI,CAACyD,WAAW,IAAI,CAAC,gBAAgB,CAACC,IAAI,CAAC1D,IAAI,CAACyD,WAAW,CAAC,kBAC7D/F,OAAA,CAAChB,MAAM,CAAC6K,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEhH,QAAQ,EAAE,GAAG;UAAE+H,KAAK,EAAE;QAAI,CAAE;QAC1CtB,SAAS,EAAC,eAAe;QAAAC,QAAA,eAEzB5J,OAAA;UAAK2J,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B5J,OAAA,CAACX,aAAa;YAACsK,SAAS,EAAC;UAAc;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1ChL,OAAA;YAAA4J,QAAA,gBACE5J,OAAA;cAAA4J,QAAA,EAAI;YAAqB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9BhL,OAAA;cAAA4J,QAAA,EAAG;YAAuE;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9EhL,OAAA;cACE2J,SAAS,EAAC,kBAAkB;cAC5BQ,OAAO,EAAEA,CAAA,KAAM9F,MAAM,CAACsE,QAAQ,CAACC,IAAI,GAAG,UAAW;cAAAgB,QAAA,EAClD;YAED;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,EAGAlK,mBAAmB,iBAClBd,OAAA;QACE2J,SAAS,EAAC,uBAAuB;QACjCQ,OAAO,EAAGiC,CAAC,IAAKA,CAAC,CAACO,MAAM,KAAKP,CAAC,CAACC,aAAa,IAAI5G,0BAA0B,CAAC,CAAE;QAAAmE,QAAA,eAE7E5J,OAAA;UACE2J,SAAS,EAAC,oCAAoC;UAC9CnG,KAAK,EAAE;YACL4G,QAAQ,EAAE,UAAU;YACpBhI,GAAG,EAAEF,aAAa,CAACE,GAAG;YACtBC,IAAI,EAAEH,aAAa,CAACG,IAAI;YACxBuE,SAAS,EAAE1E,aAAa,CAAC0E,SAAS,IAAI,MAAM;YAC5CkG,MAAM,EAAE;UACV,CAAE;UAAAlD,QAAA,gBAGF5J,OAAA;YACE2J,SAAS,EAAC,iBAAiB;YAC3BQ,OAAO,EAAE1E,0BAA2B;YACpC,cAAW,aAAa;YAAAmE,QAAA,eAExB5J,OAAA;cAAKyM,KAAK,EAAC,IAAI;cAACM,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAAArD,QAAA,eACzD5J,OAAA;gBAAMkN,CAAC,EAAC,sBAAsB;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC;cAAO;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAGThL,OAAA;YAAK2J,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtC5J,OAAA;cAAK2J,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B5J,OAAA;gBAAK2J,SAAS,EAAC;cAAS;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/BhL,OAAA;gBAAK2J,SAAS,EAAC,cAAc;gBAAC8C,KAAK,EAAC,IAAI;gBAACM,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAArD,QAAA,gBAClF5J,OAAA;kBAAMkN,CAAC,EAAC,wOAAwO;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAK;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC1RhL,OAAA;kBAAMkN,CAAC,EAAC,UAAU;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,KAAK;kBAACC,aAAa,EAAC;gBAAO;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAClFhL,OAAA;kBAAMkN,CAAC,EAAC,aAAa;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,KAAK;kBAACC,aAAa,EAAC;gBAAO;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACrFhL,OAAA;kBAAMkN,CAAC,EAAC,aAAa;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC,KAAK;kBAACC,aAAa,EAAC;gBAAO;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNhL,OAAA;cAAA4J,QAAA,EAAI;YAAkB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3BhL,OAAA;cAAA4J,QAAA,EAAG;YAA8B;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eAGNhL,OAAA;YAAK2J,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAE5B5J,OAAA;cAAK2J,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B5J,OAAA;gBAAK2J,SAAS,EAAC;cAA6B;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnDhL,OAAA;gBAAG2J,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAEtI;cAAa;gBAAAuJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eAGNhL,OAAA;cAAK2J,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B5J,OAAA;gBAAA4J,QAAA,EAAKxI,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEyB;cAAK;gBAAAgI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC9BhL,OAAA;gBAAK2J,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B5J,OAAA;kBAAK2J,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB5J,OAAA;oBAAA4J,QAAA,EAAM;kBAAM;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnBhL,OAAA;oBAAA4J,QAAA,GAASxI,YAAY,aAAZA,YAAY,wBAAAd,qBAAA,GAAZc,YAAY,CAAE4B,eAAe,cAAA1C,qBAAA,uBAA7BA,qBAAA,CAA+ByL,cAAc,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,eACNhL,OAAA;kBAAK2J,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB5J,OAAA;oBAAA4J,QAAA,EAAM;kBAAQ;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrBhL,OAAA;oBAAA4J,QAAA,GAASxI,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE8B,QAAQ,EAAC,QAAM,EAAC,CAAA9B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE8B,QAAQ,IAAG,CAAC,GAAG,GAAG,GAAG,EAAE;kBAAA;oBAAA2H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNhL,OAAA;cAAK2J,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B5J,OAAA;gBAAK2J,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjC5J,OAAA;kBAAKyM,KAAK,EAAC,IAAI;kBAACM,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAArD,QAAA,eACzD5J,OAAA;oBAAMkN,CAAC,EAAC,8kBAA8kB;oBAACD,IAAI,EAAC;kBAAc;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzmB,CAAC,eACNhL,OAAA;kBAAA4J,QAAA,EAAM;gBAAgB;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eACNhL,OAAA;gBAAK2J,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEtH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyD;cAAW;gBAAA8E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvDhL,OAAA;gBAAK2J,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC5J,OAAA;kBAAK2J,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAkD;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC9EhL,OAAA;kBAAK2J,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAA0C;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtEhL,OAAA;kBAAK2J,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAC;gBAAwC;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAGLlJ,YAAY,iBACX9B,OAAA;cAAK2J,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B5J,OAAA;gBAAA4J,QAAA,EAAG;cAA4B;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnChL,OAAA;gBAAQ2J,SAAS,EAAC,eAAe;gBAACQ,OAAO,EAAEzE,cAAe;gBAAAkE,QAAA,gBACxD5J,OAAA;kBAAKyM,KAAK,EAAC,IAAI;kBAACM,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAArD,QAAA,gBACzD5J,OAAA;oBAAMkN,CAAC,EAAC,uBAAuB;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,aAAa,EAAC;kBAAO;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC7FhL,OAAA;oBAAMkN,CAAC,EAAC,iBAAiB;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC;kBAAO;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3G,CAAC,aAER;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAKA9J,gBAAgB,iBACflB,OAAA;QAAK2J,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eACpC5J,OAAA;UAAK2J,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAE9C5J,OAAA;YACE2J,SAAS,EAAC,iBAAiB;YAC3BQ,OAAO,EAAEA,CAAA,KAAM;cACblI,wBAAwB,CAAC,IAAI,CAAC;cAC9Bd,mBAAmB,CAAC,KAAK,CAAC;YAC5B,CAAE;YACF,cAAW,aAAa;YAAAyI,QAAA,eAExB5J,OAAA;cAAKyM,KAAK,EAAC,IAAI;cAACM,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAAArD,QAAA,eACzD5J,OAAA;gBAAMkN,CAAC,EAAC,sBAAsB;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC;cAAO;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAGThL,OAAA;YAAK2J,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC5J,OAAA;cAAK2J,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B5J,OAAA;gBAAKyM,KAAK,EAAC,IAAI;gBAACM,MAAM,EAAC,IAAI;gBAACC,OAAO,EAAC,WAAW;gBAACC,IAAI,EAAC,MAAM;gBAAArD,QAAA,gBACzD5J,OAAA;kBAAMkN,CAAC,EAAC,mHAAmH;kBAACD,IAAI,EAAC,SAAS;kBAACM,WAAW,EAAC;gBAAK;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC9JhL,OAAA;kBAAMkN,CAAC,EAAC,sBAAsB;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC,GAAG;kBAACC,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC;gBAAO;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eAC9GhL,OAAA;kBAAMkN,CAAC,EAAC,mHAAmH;kBAACC,MAAM,EAAC,SAAS;kBAACC,WAAW,EAAC;gBAAG;kBAAAvC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3J;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNhL,OAAA;cAAA4J,QAAA,EAAI;YAAmB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BhL,OAAA;cAAA4J,QAAA,GAAG,aAAW,EAACxI,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEyB,KAAK,EAAC,GAAC;YAAA;cAAAgI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eAENhL,OAAA;YAAK2J,SAAS,EAAC,eAAe;YAAAC,QAAA,GAE3B5H,qBAAqB,iBACpBhC,OAAA;cAAK2J,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B5J,OAAA;gBAAK2J,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAC7B5J,OAAA;kBAAKyM,KAAK,EAAC,IAAI;kBAACM,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAArD,QAAA,gBACzD5J,OAAA;oBAAMkN,CAAC,EAAC,0HAA0H;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC,GAAG;oBAACC,aAAa,EAAC;kBAAO;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAChMhL,OAAA;oBAAQwN,EAAE,EAAC,IAAI;oBAACC,EAAE,EAAC,IAAI;oBAACC,CAAC,EAAC,GAAG;oBAACP,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC;kBAAG;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhL,OAAA;gBAAA4J,QAAA,GAAG,wBAAsB,EAAC5H,qBAAqB,EAAC,aAAW;cAAA;gBAAA6I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CACN,eAGDhL,OAAA;cAAK2J,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC5J,OAAA;gBAAA4J,QAAA,EAAI;cAAsB;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BhL,OAAA;gBAAK2J,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B5J,OAAA;kBAAK2J,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB5J,OAAA;oBAAA4J,QAAA,EAAM;kBAAI;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjBhL,OAAA;oBAAA4J,QAAA,EAASxI,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEyB;kBAAK;oBAAAgI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACNhL,OAAA;kBAAK2J,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB5J,OAAA;oBAAA4J,QAAA,EAAM;kBAAQ;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACrBhL,OAAA;oBAAA4J,QAAA,GAASxI,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE8B,QAAQ,EAAC,QAAM,EAAC,CAAA9B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE8B,QAAQ,IAAG,CAAC,GAAG,GAAG,GAAG,EAAE;kBAAA;oBAAA2H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC,eACNhL,OAAA;kBAAK2J,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB5J,OAAA;oBAAA4J,QAAA,EAAM;kBAAW;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxBhL,OAAA;oBAAA4J,QAAA,GAASxI,YAAY,aAAZA,YAAY,wBAAAb,sBAAA,GAAZa,YAAY,CAAE4B,eAAe,cAAAzC,sBAAA,uBAA7BA,sBAAA,CAA+BwL,cAAc,CAAC,CAAC,EAAC,MAAI;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,eACNhL,OAAA;kBAAK2J,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC5J,OAAA;oBAAA4J,QAAA,EAAM;kBAAM;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnBhL,OAAA;oBAAK2J,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3B5J,OAAA;sBAAKyM,KAAK,EAAC,IAAI;sBAACM,MAAM,EAAC,IAAI;sBAACC,OAAO,EAAC,WAAW;sBAACC,IAAI,EAAC,MAAM;sBAAArD,QAAA,gBACzD5J,OAAA;wBAAMkN,CAAC,EAAC,mBAAmB;wBAACC,MAAM,EAAC,cAAc;wBAACC,WAAW,EAAC,GAAG;wBAACC,aAAa,EAAC,OAAO;wBAACC,cAAc,EAAC;sBAAO;wBAAAzC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC,eAChHhL,OAAA;wBAAQwN,EAAE,EAAC,IAAI;wBAACC,EAAE,EAAC,IAAI;wBAACC,CAAC,EAAC,GAAG;wBAACP,MAAM,EAAC,cAAc;wBAACC,WAAW,EAAC;sBAAG;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClE,CAAC,UAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNhL,OAAA;cAAK2J,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B5J,OAAA;gBAAA4J,QAAA,EAAI;cAA4B;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACrChL,OAAA;gBAAK2J,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B5J,OAAA;kBAAK2J,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B5J,OAAA;oBAAKyM,KAAK,EAAC,IAAI;oBAACM,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAArD,QAAA,gBACzD5J,OAAA;sBAAMkN,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHhL,OAAA;sBAAQwN,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACP,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNhL,OAAA;oBAAA4J,QAAA,EAAM;kBAAiB;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACNhL,OAAA;kBAAK2J,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B5J,OAAA;oBAAKyM,KAAK,EAAC,IAAI;oBAACM,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAArD,QAAA,gBACzD5J,OAAA;sBAAMkN,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHhL,OAAA;sBAAQwN,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACP,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNhL,OAAA;oBAAA4J,QAAA,EAAM;kBAAY;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACNhL,OAAA;kBAAK2J,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B5J,OAAA;oBAAKyM,KAAK,EAAC,IAAI;oBAACM,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAArD,QAAA,gBACzD5J,OAAA;sBAAMkN,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHhL,OAAA;sBAAQwN,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACP,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNhL,OAAA;oBAAA4J,QAAA,EAAM;kBAAe;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACNhL,OAAA;kBAAK2J,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B5J,OAAA;oBAAKyM,KAAK,EAAC,IAAI;oBAACM,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAArD,QAAA,gBACzD5J,OAAA;sBAAMkN,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHhL,OAAA;sBAAQwN,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACP,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNhL,OAAA;oBAAA4J,QAAA,EAAM;kBAAiB;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACNhL,OAAA;kBAAK2J,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B5J,OAAA;oBAAKyM,KAAK,EAAC,IAAI;oBAACM,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAArD,QAAA,gBACzD5J,OAAA;sBAAMkN,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHhL,OAAA;sBAAQwN,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACP,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNhL,OAAA;oBAAA4J,QAAA,EAAM;kBAAe;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACNhL,OAAA;kBAAK2J,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B5J,OAAA;oBAAKyM,KAAK,EAAC,IAAI;oBAACM,MAAM,EAAC,IAAI;oBAACC,OAAO,EAAC,WAAW;oBAACC,IAAI,EAAC,MAAM;oBAAArD,QAAA,gBACzD5J,OAAA;sBAAMkN,CAAC,EAAC,mBAAmB;sBAACC,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC,GAAG;sBAACC,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC;oBAAO;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC,eAChHhL,OAAA;sBAAQwN,EAAE,EAAC,IAAI;sBAACC,EAAE,EAAC,IAAI;sBAACC,CAAC,EAAC,GAAG;sBAACP,MAAM,EAAC,cAAc;sBAACC,WAAW,EAAC;oBAAG;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACNhL,OAAA;oBAAA4J,QAAA,EAAM;kBAAY;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNhL,OAAA;cAAK2J,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC5B5J,OAAA;gBACE2J,SAAS,EAAC,aAAa;gBACvBQ,OAAO,EAAEA,CAAA,KAAM;kBACblI,wBAAwB,CAAC,IAAI,CAAC;kBAC9Bd,mBAAmB,CAAC,KAAK,CAAC;kBAC1BkD,MAAM,CAACsE,QAAQ,CAACC,IAAI,GAAG,WAAW;gBACpC,CAAE;gBAAAgB,QAAA,gBAEF5J,OAAA;kBAAKyM,KAAK,EAAC,IAAI;kBAACM,MAAM,EAAC,IAAI;kBAACC,OAAO,EAAC,WAAW;kBAACC,IAAI,EAAC,MAAM;kBAAArD,QAAA,gBACzD5J,OAAA;oBAAMkN,CAAC,EAAC,8KAA8K;oBAACC,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC;kBAAG;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC,eAC9NhL,OAAA;oBAAU2N,MAAM,EAAC,uBAAuB;oBAACR,MAAM,EAAC,cAAc;oBAACC,WAAW,EAAC;kBAAG;oBAAAvC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC,oBACU,EAAChJ,qBAAqB,GAAI,IAAGA,qBAAsB,IAAG,GAAG,EAAE;cAAA;gBAAA6I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACThL,OAAA;gBACE2J,SAAS,EAAC,eAAe;gBACzBQ,OAAO,EAAEA,CAAA,KAAM;kBACblI,wBAAwB,CAAC,IAAI,CAAC;kBAC9Bd,mBAAmB,CAAC,KAAK,CAAC;gBAC5B,CAAE;gBAAAyI,QAAA,EACH;cAED;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAKDhL,OAAA,CAACH,uBAAuB;QACtB+N,OAAO,EAAEpM,sBAAuB;QAChCqM,OAAO,EAAEA,CAAA,KAAMpM,yBAAyB,CAAC,KAAK,CAAE;QAChDqM,WAAW,EAAEtN,KAAK,CAACuN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpL,GAAG,MAAKJ,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE0I,UAAU,EAAC,KAAI1I,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEqD,IAAI,CAAC;QAC/FpD,YAAY,EAAED,gBAAiB;QAC/BF,IAAI,EAAEA;MAAK;QAAAuI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAGFhL,OAAA,CAACF,wBAAwB;QACvB8N,OAAO,EAAElM,gBAAiB;QAC1BmM,OAAO,EAAEA,CAAA,KAAMlM,mBAAmB,CAAC,KAAK,CAAE;QAC1CsM,OAAO,EAAE1I,uBAAwB;QACjC9C,YAAY,EAAED,gBAAiB;QAC/BF,IAAI,EAAEA,IAAK;QACX9B,KAAK,EAAEA;MAAM;QAAAqK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5K,EAAA,CA5kCID,YAAY;EAAA,QAmBCrB,WAAW,EACCA,WAAW,EACvBC,WAAW;AAAA;AAAAmP,EAAA,GArBxB/N,YAAY;AA8kClB,eAAeA,YAAY;AAAC,IAAA+N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}