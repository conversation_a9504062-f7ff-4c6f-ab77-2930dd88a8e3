{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Subscription\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport UpgradeRestrictionModal from '../../../components/UpgradeRestrictionModal/UpgradeRestrictionModal';\nimport SubscriptionExpiredModal from '../../../components/SubscriptionExpiredModal/SubscriptionExpiredModal';\nimport './Subscription.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Subscription = () => {\n  _s();\n  var _subscriptionData$act, _selectedPlan$discoun, _selectedPlan$discoun2;\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(null); // Changed to store plan ID instead of boolean\n  const [showProcessingModal, setShowProcessingModal] = useState(false);\n\n  // Debug: Log showProcessingModal state changes\n  useEffect(() => {\n    console.log('🔍 showProcessingModal state changed to:', showProcessingModal);\n  }, [showProcessingModal]);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [paymentStatus, setPaymentStatus] = useState('');\n  const [showUpgradeRestriction, setShowUpgradeRestriction] = useState(false);\n  const [showExpiredModal, setShowExpiredModal] = useState(false);\n  const [processingStartTime, setProcessingStartTime] = useState(null);\n  const [showTryAgain, setShowTryAgain] = useState(false);\n  const [autoNavigateCountdown, setAutoNavigateCountdown] = useState(null);\n  const {\n    user\n  } = useSelector(state => state.user);\n  const {\n    subscriptionData\n  } = useSelector(state => state.subscription);\n  const dispatch = useDispatch();\n\n  // Fallback sample plans in case API fails\n  const samplePlans = [{\n    _id: \"basic-plan-sample\",\n    title: \"Basic Membership\",\n    features: [\"2-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\"],\n    actualPrice: 28570,\n    discountedPrice: 20000,\n    discountPercentage: 30,\n    duration: 2,\n    status: true\n  }, {\n    _id: \"premium-plan-sample\",\n    title: \"Premium Plan\",\n    features: [\"3-month full access\", \"Unlimited quizzes\", \"Personalized profile\", \"AI chat for instant help\", \"Forum for student discussions\", \"Study notes\", \"Past papers\", \"Books\", \"Learning videos\", \"Track progress with rankings\", \"Priority support\"],\n    actualPrice: 45000,\n    discountedPrice: 35000,\n    discountPercentage: 22,\n    duration: 3,\n    status: true\n  }];\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n  }, []);\n\n  // Handle body scroll lock when modals are open (simplified approach)\n  useEffect(() => {\n    if (showProcessingModal || showSuccessModal) {\n      // Simply prevent body scroll without position fixed\n      document.body.style.overflow = 'hidden';\n    } else {\n      // Restore body scroll\n      document.body.style.overflow = '';\n    }\n\n    // Cleanup on unmount\n    return () => {\n      document.body.style.overflow = '';\n    };\n  }, [showProcessingModal, showSuccessModal]);\n\n  // Check for expired subscription and show modal\n  useEffect(() => {\n    if (subscriptionData && isSubscriptionExpired()) {\n      console.log('🚫 Subscription expired, showing modal');\n      setShowExpiredModal(true);\n    } else {\n      setShowExpiredModal(false);\n    }\n  }, [subscriptionData]);\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      console.log('Fetching plans...');\n      const response = await getPlans();\n      console.log('Plans response:', response);\n      if (response.success && response.data && response.data.length > 0) {\n        setPlans(response.data);\n        console.log('Plans loaded successfully from API:', response.data);\n      } else if (Array.isArray(response) && response.length > 0) {\n        // Handle case where response is directly an array of plans\n        setPlans(response);\n        console.log('Plans loaded as array from API:', response);\n      } else {\n        console.warn('No plans from API, using sample plans');\n        setPlans(samplePlans);\n        message.info('Showing sample plans. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('Error loading plans from API:', error);\n      console.log('Using fallback sample plans');\n      setPlans(samplePlans);\n      message.warning('Using sample plans. Please check your connection and try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n      console.log('Current subscription:', response);\n    } catch (error) {\n      console.log('No active subscription found');\n    }\n  };\n\n  // Check if subscription is expired\n  const isSubscriptionExpired = () => {\n    if (!subscriptionData) return true;\n\n    // If no subscription data, consider expired\n    if (!subscriptionData.endDate) return true;\n\n    // If payment status is not paid, consider expired\n    if (subscriptionData.paymentStatus !== 'paid') return true;\n\n    // If status is not active, consider expired\n    if (subscriptionData.status !== 'active') return true;\n\n    // Check if end date has passed\n    const endDate = new Date(subscriptionData.endDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n    endDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    return endDate < today;\n  };\n\n  // Handle subscription renewal from expired modal\n  const handleRenewSubscription = async selectedPlan => {\n    setShowExpiredModal(false);\n    await handlePlanSelect(selectedPlan);\n  };\n\n  // Handle closing payment processing modal\n  const handleCloseProcessingModal = () => {\n    setShowProcessingModal(false);\n    setPaymentLoading(null); // Reset to null instead of false\n    setShowTryAgain(false);\n    setProcessingStartTime(null);\n    setPaymentStatus('');\n    message.info('Payment process cancelled. You can try again anytime.');\n  };\n\n  // Handle try again functionality\n  const handleTryAgain = () => {\n    if (selectedPlan) {\n      setShowTryAgain(false);\n      setProcessingStartTime(null);\n      handlePlanSelect(selectedPlan);\n    }\n  };\n\n  // Test success modal (for debugging)\n  const testSuccessModal = () => {\n    console.log('🧪 Testing success modal...');\n    setShowProcessingModal(false);\n    setShowSuccessModal(true);\n    setPaymentLoading(null);\n  };\n\n  // Test processing modal (for debugging)\n  const testProcessingModal = () => {\n    console.log('🧪 Testing processing modal...');\n    setShowProcessingModal(true);\n    setPaymentStatus('Testing processing modal...');\n    setSelectedPlan(plans[0] || {\n      title: 'Test Plan',\n      discountedPrice: 5000,\n      duration: 1\n    });\n  };\n  const handlePlanSelect = async plan => {\n    // Check if user already has an active subscription\n    if (subscriptionData && subscriptionData.status === 'active' && subscriptionData.paymentStatus === 'paid') {\n      console.log('🚫 User already has active subscription:', subscriptionData);\n      setShowUpgradeRestriction(true);\n      return;\n    }\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n    try {\n      var _user$name;\n      console.log('🚀 Starting payment for plan:', plan.title);\n      console.log('🔧 IMMEDIATELY showing processing modal...');\n\n      // IMMEDIATELY show processing modal when user chooses plan\n      setSelectedPlan(plan);\n      setPaymentLoading(plan._id);\n      setShowProcessingModal(true);\n      setShowTryAgain(false);\n      setProcessingStartTime(Date.now());\n      setPaymentStatus('🚀 Preparing your payment request...');\n      console.log('✅ Processing modal IMMEDIATELY displayed');\n\n      // Small delay to ensure modal is visible before API call\n      await new Promise(resolve => setTimeout(resolve, 200));\n\n      // Set timer for try again button (10 seconds)\n      const tryAgainTimer = setTimeout(() => {\n        setShowTryAgain(true);\n      }, 10000);\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${(_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n      setPaymentStatus('📤 Sending payment request to ZenoPay...');\n      const response = await addPayment(paymentData);\n      if (response.success) {\n        var _response$data;\n        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');\n        console.log('💳 Payment response:', response);\n        console.log('🆔 Order ID:', response.order_id);\n\n        // Show confirmation message to user\n        message.success({\n          content: `💳 Payment initiated! 📱 Check your phone (${user.phoneNumber}) for SMS confirmation from ZenoPay.`,\n          duration: 8,\n          style: {\n            marginTop: '20vh'\n          }\n        });\n\n        // Start checking payment status immediately\n        const orderIdToCheck = response.order_id || ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.order_id) || 'demo_order';\n        console.log('🔍 Starting payment confirmation check for order:', orderIdToCheck);\n        checkPaymentConfirmation(orderIdToCheck);\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('❌ Payment failed:', error);\n      setShowProcessingModal(false);\n      message.error('Payment failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const checkPaymentConfirmation = async orderId => {\n    console.log('🚀 Starting payment confirmation check for order:', orderId);\n    let isPolling = true;\n    let handleVisibilityChange;\n    try {\n      setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n\n      // Poll payment status every 2 seconds for optimal responsiveness\n      let attempts = 0;\n      const maxAttempts = 150; // 150 attempts * 2 seconds = 5 minutes\n\n      const pollPaymentStatus = async () => {\n        attempts++;\n        console.log(`🔍 Payment status check attempt ${attempts}/${maxAttempts} for order:`, orderId);\n        try {\n          const statusResponse = await checkPaymentStatus({\n            orderId\n          });\n          console.log('📊 Payment status response:', statusResponse);\n          console.log('🔍 Checking payment conditions:');\n          console.log('  - Live payment:', (statusResponse === null || statusResponse === void 0 ? void 0 : statusResponse.paymentStatus) === 'paid' && (statusResponse === null || statusResponse === void 0 ? void 0 : statusResponse.status) === 'active');\n          console.log('  - Demo payment:', (statusResponse === null || statusResponse === void 0 ? void 0 : statusResponse.status) === 'completed' && (statusResponse === null || statusResponse === void 0 ? void 0 : statusResponse.success) === true);\n          if (statusResponse && (statusResponse.paymentStatus === 'paid' && statusResponse.status === 'active' || statusResponse.status === 'completed' && statusResponse.success === true)) {\n            // Payment confirmed immediately!\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('🎉 Payment confirmed! Activating your subscription...');\n            console.log('✅ Payment confirmed, preparing to show success modal...');\n\n            // Show success INSTANTLY - no delay\n            console.log('🔄 Setting modal states - Processing: false, Success: true');\n            setShowProcessingModal(false);\n            setShowSuccessModal(true);\n            setPaymentLoading(null);\n            console.log('✅ Success modal state set to true');\n\n            // Refresh subscription data\n            checkCurrentSubscription();\n\n            // Show immediate success message\n            message.success({\n              content: '🎉 Payment confirmed! All features are now unlocked!',\n              duration: 5,\n              style: {\n                marginTop: '20vh',\n                fontSize: '16px'\n              }\n            });\n\n            // Start countdown for auto-navigation to hub\n            setAutoNavigateCountdown(5);\n            const countdownInterval = setInterval(() => {\n              setAutoNavigateCountdown(prev => {\n                if (prev <= 1) {\n                  clearInterval(countdownInterval);\n                  console.log('🏠 Auto-navigating to hub after successful payment...');\n                  setShowSuccessModal(false);\n                  window.location.href = '/user/hub';\n                  return null;\n                }\n                return prev - 1;\n              });\n            }, 1000);\n          } else if (attempts >= maxAttempts) {\n            // Timeout - but don't fail completely\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('⏰ Still waiting for confirmation. Please complete the payment on your phone.');\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setPaymentLoading(null); // Reset to null\n              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status or try again.');\n            }, 2000);\n          } else {\n            // Continue polling - NO TIME INDICATION, just encouraging message\n            setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n            setTimeout(pollPaymentStatus, 2000); // Check every 2 seconds for better performance\n          }\n        } catch (error) {\n          console.error('Payment status check error:', error);\n\n          // Handle specific error types\n          if (error.message && error.message.includes('404')) {\n            console.error('❌ Payment status endpoint not found (404)');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Payment verification service is temporarily unavailable. Please contact support or check your subscription status manually.');\n            return;\n          }\n          if (error.message && error.message.includes('401')) {\n            console.error('❌ Authentication required for payment status check');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Please login again to check payment status.');\n            return;\n          }\n          if (attempts >= maxAttempts) {\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Unable to confirm payment status. Please check your subscription status manually.');\n          } else {\n            // Continue polling even if there's an error (unless it's a critical error)\n            setTimeout(pollPaymentStatus, 1000);\n          }\n        }\n      };\n\n      // Add visibility change listener to check immediately when user returns to tab\n      handleVisibilityChange = () => {\n        if (!document.hidden && isPolling) {\n          console.log('User returned to tab, checking payment status immediately...');\n          setPaymentStatus('🔍 Checking payment status...');\n          // Trigger immediate check\n          setTimeout(() => pollPaymentStatus(), 100);\n        }\n      };\n      document.addEventListener('visibilitychange', handleVisibilityChange);\n\n      // Start polling immediately (no delay) - check right away\n      setTimeout(pollPaymentStatus, 500); // Start checking after 0.5 seconds\n    } catch (error) {\n      isPolling = false; // Stop polling\n      if (handleVisibilityChange) {\n        document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n      }\n\n      setShowProcessingModal(false);\n      message.error('Payment confirmation failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    if ((user === null || user === void 0 ? void 0 : user.subscriptionStatus) === 'expired' || subscriptionData && subscriptionData.status === 'expired') {\n      return 'expired';\n    }\n    return 'none';\n  };\n  const formatDate = dateString => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n  const getDaysRemaining = () => {\n    if (!(subscriptionData !== null && subscriptionData !== void 0 && subscriptionData.endDate)) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n  const subscriptionStatus = getSubscriptionStatus();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"subscription-page\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"subscription-container\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: -20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"subscription-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            console.log('🧪 Testing success modal...');\n            setSelectedPlan(plans[0] || {\n              title: 'Test Plan',\n              duration: 1,\n              discountedPrice: 13000\n            });\n            setShowSuccessModal(true);\n          },\n          style: {\n            position: 'fixed',\n            top: '10px',\n            right: '10px',\n            background: '#52c41a',\n            color: 'white',\n            border: 'none',\n            padding: '8px 16px',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            zIndex: 9999\n          },\n          children: \"\\uD83E\\uDDEA Test Success Modal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"page-title\",\n          children: [/*#__PURE__*/_jsxDEV(FaCrown, {\n            className: \"title-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 521,\n            columnNumber: 13\n          }, this), \"Subscription Management\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"page-subtitle\",\n          children: \"Manage your subscription and access premium features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        className: \"current-subscription\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Current Subscription\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 11\n        }, this), subscriptionStatus === 'active' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card active\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n              className: \"status-icon active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 539,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Active Subscription\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 538,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCrown, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 544,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Plan: \", (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData$act = subscriptionData.activePlan) === null || _subscriptionData$act === void 0 ? void 0 : _subscriptionData$act.title) || 'Premium Plan']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Expires: \", formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 552,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Days Remaining: \", getDaysRemaining()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 13\n        }, this), subscriptionStatus === 'expired' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card expired\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n              className: \"status-icon expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Subscription Expired\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-item\",\n              children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                className: \"detail-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Expired: \", formatDate(subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.endDate)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"renewal-message\",\n              children: \"Your subscription has expired. Choose a new plan below to continue accessing premium features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 570,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 13\n        }, this), subscriptionStatus === 'none' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"subscription-card none\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-status\",\n            children: [/*#__PURE__*/_jsxDEV(FaUser, {\n              className: \"status-icon none\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 580,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"status-text\",\n              children: \"Free Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"subscription-details\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"upgrade-message\",\n              children: \"You're currently using a free account. Upgrade to a premium plan to unlock all features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 578,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"available-plans\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: subscriptionStatus === 'active' ? '🚀 Upgrade Your Plan' : subscriptionStatus === 'expired' ? '🔄 Renew Your Subscription' : '🎯 Choose Your Plan'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 599,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: '10px',\n            justifyContent: 'center',\n            marginBottom: '20px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: testProcessingModal,\n            style: {\n              background: '#ff6b6b',\n              color: 'white',\n              border: 'none',\n              padding: '8px 16px',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\uD83E\\uDDEA Test Processing Modal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: testSuccessModal,\n            style: {\n              background: '#51cf66',\n              color: 'white',\n              border: 'none',\n              padding: '8px 16px',\n              borderRadius: '6px',\n              cursor: 'pointer',\n              fontSize: '12px'\n            },\n            children: \"\\uD83E\\uDDEA Test Success Modal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle\",\n          children: subscriptionStatus === 'active' ? 'Upgrade to a longer plan for better value and extended access' : subscriptionStatus === 'expired' ? 'Your subscription has expired. Renew now to continue accessing premium features' : 'Select a subscription plan to unlock all premium features and start your learning journey'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 11\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading plans...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 13\n        }, this) : plans.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-plans-state\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"no-plans-icon\",\n            children: \"\\uD83D\\uDCCB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"No Plans Available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Plans are currently being loaded. Please refresh the page or try again later.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"refresh-btn\",\n            onClick: fetchPlans,\n            children: \"\\uD83D\\uDD04 Refresh Plans\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 654,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plans-grid\",\n          children: plans.map(plan => {\n            var _plan$title, _plan$discountedPrice, _plan$actualPrice, _plan$features;\n            return /*#__PURE__*/_jsxDEV(motion.div, {\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              className: \"plan-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"plan-title\",\n                  children: plan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 672,\n                  columnNumber: 21\n                }, this), ((_plan$title = plan.title) === null || _plan$title === void 0 ? void 0 : _plan$title.toLowerCase().includes('standard')) && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"plan-badge\",\n                  children: \"\\uD83D\\uDD25 Popular\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 674,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-pricing\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"price-display\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"current-price\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"currency\",\n                      children: \"TZS\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 681,\n                      columnNumber: 25\n                    }, this), (_plan$discountedPrice = plan.discountedPrice) === null || _plan$discountedPrice === void 0 ? void 0 : _plan$discountedPrice.toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 680,\n                    columnNumber: 23\n                  }, this), plan.actualPrice > plan.discountedPrice && /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"original-price\",\n                      children: [(_plan$actualPrice = plan.actualPrice) === null || _plan$actualPrice === void 0 ? void 0 : _plan$actualPrice.toLocaleString(), \" TZS\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 686,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"discount-badge\",\n                      children: [Math.round((plan.actualPrice - plan.discountedPrice) / plan.actualPrice * 100), \"% OFF\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 687,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 679,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"plan-duration\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"duration-highlight\",\n                    children: plan.duration\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 694,\n                    columnNumber: 23\n                  }, this), \" month\", plan.duration > 1 ? 's' : '', \" access\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 693,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 678,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"plan-features\",\n                children: (_plan$features = plan.features) === null || _plan$features === void 0 ? void 0 : _plan$features.slice(0, 5).map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-item\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCheckCircle, {\n                    className: \"feature-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 701,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: feature\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 702,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 700,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"select-plan-btn\",\n                onClick: () => handlePlanSelect(plan),\n                disabled: paymentLoading === plan._id,\n                style: {\n                  background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '12px',\n                  padding: '1rem 1.5rem',\n                  fontSize: '1rem',\n                  fontWeight: '600',\n                  cursor: paymentLoading === plan._id ? 'not-allowed' : 'pointer',\n                  transition: 'all 0.3s ease',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  gap: '0.5rem',\n                  width: '100%',\n                  opacity: paymentLoading === plan._id ? 0.6 : 1\n                },\n                onMouseEnter: e => {\n                  if (paymentLoading !== plan._id) {\n                    e.target.style.background = 'linear-gradient(135deg, #1d4ed8, #1e40af)';\n                    e.target.style.transform = 'translateY(-2px)';\n                    e.target.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.4)';\n                  }\n                },\n                onMouseLeave: e => {\n                  if (paymentLoading !== plan._id) {\n                    e.target.style.background = 'linear-gradient(135deg, #3b82f6, #1d4ed8)';\n                    e.target.style.transform = 'translateY(0)';\n                    e.target.style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.3)';\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(FaCreditCard, {\n                  className: \"btn-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 21\n                }, this), paymentLoading === plan._id ? 'Processing...' : subscriptionStatus === 'active' ? 'Click to Upgrade' : subscriptionStatus === 'expired' ? 'Click to Renew' : 'Click to Pay']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 707,\n                columnNumber: 19\n              }, this)]\n            }, plan._id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 665,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 593,\n        columnNumber: 9\n      }, this), (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.6\n        },\n        className: \"phone-warning\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"warning-content\",\n          children: [/*#__PURE__*/_jsxDEV(FaTimesCircle, {\n            className: \"warning-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 768,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Phone Number Required\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 770,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Please update your phone number in your profile to subscribe to a plan.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 771,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"update-phone-btn\",\n              onClick: () => window.location.href = '/profile',\n              children: \"Update Phone Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 772,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 769,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 767,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 761,\n        columnNumber: 11\n      }, this), showProcessingModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-overlay-best\",\n        style: {\n          position: 'fixed',\n          top: '0',\n          left: '0',\n          width: '100vw',\n          height: '100vh',\n          background: 'rgba(0, 0, 0, 0.4)',\n          // Lighter overlay\n          backdropFilter: 'blur(8px)',\n          WebkitBackdropFilter: 'blur(8px)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: '10000',\n          padding: '20px',\n          boxSizing: 'border-box',\n          overflowY: 'auto' // Enable scrolling\n        },\n\n        onClick: e => e.target === e.currentTarget && handleCloseProcessingModal(),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-container-best\",\n          style: {\n            background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',\n            borderRadius: '20px',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1)',\n            border: '1px solid rgba(59, 130, 246, 0.1)',\n            width: '100%',\n            maxWidth: '600px',\n            // Larger modal\n            maxHeight: '85vh',\n            // More height\n            display: 'flex',\n            flexDirection: 'column',\n            overflow: 'hidden',\n            position: 'relative',\n            transform: 'translateZ(0)',\n            margin: 'auto' // Center in scrollable area\n          },\n\n          onClick: e => e.stopPropagation(),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n              padding: '1.5rem',\n              color: 'white',\n              textAlign: 'center',\n              position: 'relative'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"payment-modal-close\",\n              onClick: handleCloseProcessingModal,\n              title: \"Close payment window\",\n              style: {\n                position: 'absolute',\n                top: '12px',\n                right: '12px',\n                background: 'rgba(255, 255, 255, 0.2)',\n                border: 'none',\n                borderRadius: '50%',\n                width: '32px',\n                height: '32px',\n                color: 'white',\n                fontSize: '16px',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                transition: 'all 0.3s ease',\n                zIndex: 10\n              },\n              onMouseEnter: e => e.target.style.background = 'rgba(255, 255, 255, 0.3)',\n              onMouseLeave: e => e.target.style.background = 'rgba(255, 255, 255, 0.2)',\n              children: \"\\u2715\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 833,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '0.5rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"payment-processing-animation\",\n                style: {\n                  width: '60px',\n                  height: '60px',\n                  margin: '0 auto 1rem',\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"payment-spinner\",\n                  style: {\n                    width: '100%',\n                    height: '100%',\n                    border: '3px solid rgba(255, 255, 255, 0.3)',\n                    borderTop: '3px solid white',\n                    borderRadius: '50%',\n                    animation: 'spin 1s linear infinite'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 868,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: 'translate(-50%, -50%)',\n                    fontSize: '20px'\n                  },\n                  children: \"\\uD83D\\uDCB3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 876,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 862,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 861,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: '0',\n                fontSize: '1.4rem',\n                fontWeight: '700',\n                textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'\n              },\n              children: \"Processing Payment\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 886,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0.5rem 0 0 0',\n                fontSize: '0.9rem',\n                opacity: '0.9'\n              },\n              children: \"Secure transaction in progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 894,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 826,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: '1',\n              overflowY: 'auto',\n              overflowX: 'hidden',\n              padding: '0'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '20px',\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '16px',\n                minHeight: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(59, 130, 246, 0.05)',\n                  border: '1px solid rgba(59, 130, 246, 0.2)',\n                  borderRadius: '10px',\n                  padding: '0.75rem',\n                  textAlign: 'center'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: '0',\n                    color: '#1e40af',\n                    fontSize: '0.95rem',\n                    fontWeight: '500'\n                  },\n                  children: paymentStatus\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 925,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 918,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(16, 185, 129, 0.05)',\n                  border: '1px solid rgba(16, 185, 129, 0.2)',\n                  borderRadius: '10px',\n                  padding: '0.75rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 0.5rem 0',\n                    color: '#065f46',\n                    fontSize: '1.1rem',\n                    fontWeight: '600'\n                  },\n                  children: selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 942,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    fontSize: '0.9rem',\n                    color: '#047857'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Amount: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : (_selectedPlan$discoun = selectedPlan.discountedPrice) === null || _selectedPlan$discoun === void 0 ? void 0 : _selectedPlan$discoun.toLocaleString(), \" TZS\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 957,\n                      columnNumber: 35\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 957,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [\"Duration: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration, \" month\", (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration) > 1 ? 's' : '']\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 958,\n                      columnNumber: 37\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 958,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 950,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 936,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(245, 158, 11, 0.05)',\n                  border: '1px solid rgba(245, 158, 11, 0.2)',\n                  borderRadius: '10px',\n                  padding: '0.75rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  style: {\n                    margin: '0 0 0.75rem 0',\n                    color: '#92400e',\n                    fontSize: '1rem',\n                    fontWeight: '600',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  },\n                  children: \"\\uD83D\\uDCF1 Check Your Phone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 969,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: '0 0 0.75rem 0',\n                    color: '#b45309',\n                    fontSize: '0.9rem',\n                    fontWeight: '500',\n                    background: 'rgba(245, 158, 11, 0.1)',\n                    padding: '0.5rem',\n                    borderRadius: '8px',\n                    textAlign: 'center'\n                  },\n                  children: [\"Number: \", user === null || user === void 0 ? void 0 : user.phoneNumber]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 980,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#a16207',\n                    fontSize: '0.85rem',\n                    lineHeight: '1.5'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: '0.25rem'\n                    },\n                    children: \"1. You will receive an SMS with payment instructions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 997,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: '0.25rem'\n                    },\n                    children: \"2. Follow the SMS steps to confirm payment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 998,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: \"3. Complete the mobile money transaction\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 999,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 992,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 963,\n                columnNumber: 17\n              }, this), showTryAgain && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(239, 68, 68, 0.05)',\n                  border: '1px solid rgba(239, 68, 68, 0.2)',\n                  borderRadius: '12px',\n                  padding: '16px',\n                  textAlign: 'center',\n                  marginTop: '8px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: '0 0 0.5rem 0',\n                    color: '#dc2626',\n                    fontSize: '0.9rem',\n                    fontWeight: '600'\n                  },\n                  children: \"\\u26A0\\uFE0F Taking longer than expected?\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1013,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: '0 0 1rem 0',\n                    color: '#b91c1c',\n                    fontSize: '0.8rem'\n                  },\n                  children: \"If you haven't received SMS or facing connection issues:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1021,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"try-again-button\",\n                  onClick: handleTryAgain,\n                  style: {\n                    background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                    color: 'white',\n                    border: 'none',\n                    padding: '0.75rem 1.5rem',\n                    borderRadius: '10px',\n                    fontSize: '0.9rem',\n                    fontWeight: '600',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',\n                    width: '100%',\n                    maxWidth: '200px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    gap: '0.5rem',\n                    margin: '0 auto'\n                  },\n                  onMouseEnter: e => {\n                    e.target.style.background = 'linear-gradient(135deg, #1d4ed8 0%, #1e3a8a 100%)';\n                    e.target.style.transform = 'translateY(-2px)';\n                    e.target.style.boxShadow = '0 6px 16px rgba(59, 130, 246, 0.4)';\n                  },\n                  onMouseLeave: e => {\n                    e.target.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';\n                    e.target.style.transform = 'translateY(0)';\n                    e.target.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.3)';\n                  },\n                  children: \"\\uD83D\\uDD04 Try Again\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1028,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1005,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 910,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 904,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 806,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 785,\n        columnNumber: 11\n      }, this), showSuccessModal && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-overlay-best\",\n        style: {\n          position: 'fixed',\n          top: '0',\n          left: '0',\n          width: '100vw',\n          height: '100vh',\n          background: 'rgba(0, 0, 0, 0.9)',\n          backdropFilter: 'blur(20px)',\n          WebkitBackdropFilter: 'blur(20px)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: '10000',\n          padding: '20px',\n          boxSizing: 'border-box'\n        },\n        onClick: e => {\n          if (e.target === e.currentTarget) {\n            setAutoNavigateCountdown(null); // Clear countdown\n            setShowSuccessModal(false);\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-container-best success-modal-best\",\n          style: {\n            background: 'linear-gradient(145deg, #ffffff 0%, #f0fdf4 100%)',\n            borderRadius: '24px',\n            boxShadow: '0 25px 80px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(34, 197, 94, 0.2)',\n            border: '2px solid rgba(34, 197, 94, 0.1)',\n            width: '100%',\n            maxWidth: '600px',\n            maxHeight: '95vh',\n            display: 'flex',\n            flexDirection: 'column',\n            overflow: 'hidden',\n            position: 'relative',\n            transform: 'translateZ(0)'\n          },\n          onClick: e => e.stopPropagation(),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n              padding: '2rem',\n              color: 'white',\n              textAlign: 'center',\n              position: 'relative',\n              borderRadius: '28px 28px 0 0',\n              flexShrink: 0\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setAutoNavigateCountdown(null); // Clear countdown\n                setShowSuccessModal(false);\n              },\n              style: {\n                position: 'absolute',\n                top: '16px',\n                right: '16px',\n                background: 'rgba(255, 255, 255, 0.2)',\n                border: 'none',\n                borderRadius: '50%',\n                width: '36px',\n                height: '36px',\n                color: 'white',\n                fontSize: '18px',\n                cursor: 'pointer',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                transition: 'all 0.3s ease',\n                zIndex: 10\n              },\n              onMouseEnter: e => e.target.style.background = 'rgba(255, 255, 255, 0.3)',\n              onMouseLeave: e => e.target.style.background = 'rgba(255, 255, 255, 0.2)',\n              children: \"\\u2715\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1126,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '4rem',\n                marginBottom: '1rem',\n                animation: 'bounce 2s infinite'\n              },\n              children: \"\\uD83C\\uDF89\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1155,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                margin: '0',\n                fontSize: '2rem',\n                fontWeight: '800',\n                textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',\n                marginBottom: '0.5rem'\n              },\n              children: \"Payment Successful!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1163,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                margin: '0',\n                fontSize: '1.1rem',\n                opacity: '0.95',\n                fontWeight: '500'\n              },\n              children: [\"Welcome to \", selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title, \"! \\uD83D\\uDE80\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1173,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1117,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: '1',\n              overflowY: 'auto',\n              overflowX: 'hidden',\n              padding: '0'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '24px',\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '20px',\n                minHeight: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)',\n                  border: '2px solid #22c55e',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  textAlign: 'center',\n                  boxShadow: '0 4px 20px rgba(34, 197, 94, 0.2)'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    background: 'rgba(34, 197, 94, 0.1)',\n                    borderRadius: '12px',\n                    padding: '1rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    style: {\n                      color: '#15803d',\n                      marginBottom: '1rem',\n                      fontSize: '1.2rem',\n                      fontWeight: '700'\n                    },\n                    children: \"\\uD83C\\uDFAF Subscription Details\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1212,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'grid',\n                      gap: '0.75rem',\n                      textAlign: 'left'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.5rem',\n                        background: 'rgba(255, 255, 255, 0.7)',\n                        borderRadius: '8px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          fontWeight: '600',\n                          color: '#374151'\n                        },\n                        children: \"\\uD83D\\uDCCB Plan:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1233,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: '#059669',\n                          fontWeight: '700'\n                        },\n                        children: selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.title\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1234,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1225,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.5rem',\n                        background: 'rgba(255, 255, 255, 0.7)',\n                        borderRadius: '8px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          fontWeight: '600',\n                          color: '#374151'\n                        },\n                        children: \"\\u23F0 Duration:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1244,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: '#059669',\n                          fontWeight: '700'\n                        },\n                        children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration, \" month\", (selectedPlan === null || selectedPlan === void 0 ? void 0 : selectedPlan.duration) > 1 ? 's' : '']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1245,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1236,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.5rem',\n                        background: 'rgba(255, 255, 255, 0.7)',\n                        borderRadius: '8px'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          fontWeight: '600',\n                          color: '#374151'\n                        },\n                        children: \"\\uD83D\\uDCB0 Amount:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1255,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          color: '#059669',\n                          fontWeight: '700'\n                        },\n                        children: [selectedPlan === null || selectedPlan === void 0 ? void 0 : (_selectedPlan$discoun2 = selectedPlan.discountedPrice) === null || _selectedPlan$discoun2 === void 0 ? void 0 : _selectedPlan$discoun2.toLocaleString(), \" TZS\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1256,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1247,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.5rem',\n                        background: 'linear-gradient(135deg, #22c55e, #16a34a)',\n                        borderRadius: '8px',\n                        color: 'white'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          fontWeight: '600'\n                        },\n                        children: \"\\uD83D\\uDC8E Status:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1267,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          fontWeight: '800',\n                          fontSize: '1.1rem'\n                        },\n                        children: \"ACTIVE \\u2705\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1268,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1258,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1220,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1206,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1198,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%)',\n                  border: '2px solid #f59e0b',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  boxShadow: '0 4px 20px rgba(245, 158, 11, 0.2)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    color: '#92400e',\n                    marginBottom: '1rem',\n                    textAlign: 'center',\n                    fontSize: '1.2rem',\n                    fontWeight: '700'\n                  },\n                  children: \"\\uD83D\\uDE80 All Premium Features Unlocked!\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1282,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                    gap: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: 'rgba(255, 255, 255, 0.8)',\n                      borderRadius: '12px',\n                      padding: '1rem'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginBottom: '0.5rem',\n                        fontSize: '0.95rem',\n                        fontWeight: '600',\n                        color: '#374151'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          marginBottom: '0.25rem'\n                        },\n                        children: [\"\\u2705 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Unlimited Quizzes\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1302,\n                          columnNumber: 68\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1302,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          marginBottom: '0.25rem'\n                        },\n                        children: [\"\\uD83E\\uDD16 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"AI Assistant\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1303,\n                          columnNumber: 69\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1303,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [\"\\uD83D\\uDCDA \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Study Materials\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1304,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1304,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1301,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1296,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: 'rgba(255, 255, 255, 0.8)',\n                      borderRadius: '12px',\n                      padding: '1rem'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginBottom: '0.5rem',\n                        fontSize: '0.95rem',\n                        fontWeight: '600',\n                        color: '#374151'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          marginBottom: '0.25rem'\n                        },\n                        children: [\"\\uD83D\\uDCCA \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Progress Tracking\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1313,\n                          columnNumber: 69\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1313,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          marginBottom: '0.25rem'\n                        },\n                        children: [\"\\uD83C\\uDFA5 \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Learning Videos\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1314,\n                          columnNumber: 69\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1314,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [\"\\uD83D\\uDCAC \", /*#__PURE__*/_jsxDEV(\"strong\", {\n                          children: \"Forum Access\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1315,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1315,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1312,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1307,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1291,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1275,\n                columnNumber: 17\n              }, this), autoNavigateCountdown && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%)',\n                  border: '2px solid #0288d1',\n                  borderRadius: '12px',\n                  padding: '1rem',\n                  textAlign: 'center',\n                  marginTop: '1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: '0',\n                    color: '#01579b',\n                    fontSize: '0.9rem',\n                    fontWeight: '600'\n                  },\n                  children: [\"\\uD83D\\uDE80 Automatically redirecting to Hub in \", autoNavigateCountdown, \" seconds...\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1331,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1323,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  gap: '1rem',\n                  justifyContent: 'center',\n                  flexWrap: 'wrap',\n                  marginTop: 'auto',\n                  paddingTop: '1rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setAutoNavigateCountdown(null); // Clear countdown\n                    setShowSuccessModal(false);\n                    window.location.href = '/user/hub';\n                  },\n                  style: {\n                    background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                    border: 'none',\n                    color: 'white',\n                    padding: '1rem 2rem',\n                    borderRadius: '12px',\n                    fontSize: '1rem',\n                    fontWeight: '700',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    boxShadow: '0 4px 20px rgba(59, 130, 246, 0.3)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    minWidth: '180px',\n                    justifyContent: 'center'\n                  },\n                  onMouseEnter: e => {\n                    e.target.style.background = 'linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%)';\n                    e.target.style.transform = 'translateY(-2px)';\n                    e.target.style.boxShadow = '0 6px 25px rgba(59, 130, 246, 0.4)';\n                  },\n                  onMouseLeave: e => {\n                    e.target.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';\n                    e.target.style.transform = 'translateY(0)';\n                    e.target.style.boxShadow = '0 4px 20px rgba(59, 130, 246, 0.3)';\n                  },\n                  children: [\"\\uD83C\\uDFE0 Continue to Hub \", autoNavigateCountdown ? `(${autoNavigateCountdown}s)` : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1351,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setAutoNavigateCountdown(null); // Clear countdown\n                    setShowSuccessModal(false);\n                  },\n                  style: {\n                    background: 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)',\n                    border: '2px solid #d1d5db',\n                    color: '#374151',\n                    padding: '1rem 2rem',\n                    borderRadius: '12px',\n                    fontSize: '1rem',\n                    fontWeight: '600',\n                    cursor: 'pointer',\n                    transition: 'all 0.3s ease',\n                    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem',\n                    minWidth: '120px',\n                    justifyContent: 'center'\n                  },\n                  onMouseEnter: e => {\n                    e.target.style.background = 'linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%)';\n                    e.target.style.transform = 'translateY(-1px)';\n                    e.target.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.15)';\n                  },\n                  onMouseLeave: e => {\n                    e.target.style.background = 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)';\n                    e.target.style.transform = 'translateY(0)';\n                    e.target.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';\n                  },\n                  children: \"\\u2728 Close\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1387,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1343,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: '1.5rem',\n                  padding: '1rem',\n                  background: 'rgba(34, 197, 94, 0.1)',\n                  borderRadius: '12px',\n                  textAlign: 'center',\n                  border: '1px solid rgba(34, 197, 94, 0.2)'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: '0',\n                    fontSize: '1rem',\n                    color: '#15803d',\n                    fontWeight: '600',\n                    lineHeight: '1.5'\n                  },\n                  children: \"\\uD83C\\uDF89 Congratulations! You now have full access to all BrainWave features. Start exploring and excel in your studies! \\uD83D\\uDE80\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1433,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1425,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1190,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1184,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1098,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1073,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(UpgradeRestrictionModal, {\n        visible: showUpgradeRestriction,\n        onClose: () => setShowUpgradeRestriction(false),\n        currentPlan: plans.find(p => p._id === (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.activePlan)) || (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.plan),\n        subscription: subscriptionData,\n        user: user\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1450,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(SubscriptionExpiredModal, {\n        visible: showExpiredModal,\n        onClose: () => setShowExpiredModal(false),\n        onRenew: handleRenewSubscription,\n        subscription: subscriptionData,\n        user: user,\n        plans: plans\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1459,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 490,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 489,\n    columnNumber: 5\n  }, this);\n};\n_s(Subscription, \"CFfbvS/cIqXwYgbj+uGA0/ZAfD4=\", false, function () {\n  return [useSelector, useSelector, useDispatch];\n});\n_c = Subscription;\nexport default Subscription;\nvar _c;\n$RefreshReg$(_c, \"Subscription\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useDispatch", "motion", "message", "FaCrown", "FaCalendarAlt", "FaCheckCircle", "FaTimesCircle", "FaCreditCard", "FaUser", "getPlans", "addPayment", "checkPaymentStatus", "ShowLoading", "HideLoading", "UpgradeRestrictionModal", "SubscriptionExpiredModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Subscription", "_s", "_subscriptionData$act", "_selectedPlan$discoun", "_selectedPlan$discoun2", "plans", "setPlans", "loading", "setLoading", "paymentLoading", "setPaymentLoading", "showProcessingModal", "setShowProcessingModal", "console", "log", "showSuccessModal", "setShowSuccessModal", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "paymentStatus", "setPaymentStatus", "showUpgradeRestriction", "setShowUpgradeRestriction", "showExpiredModal", "setShowExpiredModal", "processingStartTime", "setProcessingStartTime", "showTryAgain", "setShowTryAgain", "autoNavigateCountdown", "setAutoNavigateCountdown", "user", "state", "subscriptionData", "subscription", "dispatch", "samplePlans", "_id", "title", "features", "actualPrice", "discountedPrice", "discountPercentage", "duration", "status", "fetchPlans", "checkCurrentSubscription", "document", "body", "style", "overflow", "isSubscriptionExpired", "response", "success", "data", "length", "Array", "isArray", "warn", "info", "error", "warning", "endDate", "Date", "today", "setHours", "handleRenewSubscription", "handlePlanSelect", "handleCloseProcessingModal", "handleTryAgain", "testSuccessModal", "testProcessingModal", "plan", "phoneNumber", "test", "_user$name", "now", "Promise", "resolve", "setTimeout", "tryAgainTimer", "paymentData", "userId", "userPhone", "userEmail", "email", "name", "replace", "toLowerCase", "_response$data", "order_id", "content", "marginTop", "orderIdToCheck", "checkPaymentConfirmation", "Error", "orderId", "isPolling", "handleVisibilityChange", "attempts", "maxAttempts", "pollPaymentStatus", "statusResponse", "removeEventListener", "fontSize", "countdownInterval", "setInterval", "prev", "clearInterval", "window", "location", "href", "includes", "hidden", "addEventListener", "getSubscriptionStatus", "subscriptionStatus", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "getDaysRemaining", "diffTime", "diffDays", "Math", "ceil", "max", "className", "children", "div", "initial", "opacity", "y", "animate", "transition", "onClick", "position", "top", "right", "background", "color", "border", "padding", "borderRadius", "cursor", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "activePlan", "display", "gap", "justifyContent", "marginBottom", "map", "_plan$title", "_plan$discountedPrice", "_plan$actualPrice", "_plan$features", "whileHover", "scale", "whileTap", "toLocaleString", "round", "slice", "feature", "index", "disabled", "fontWeight", "alignItems", "width", "onMouseEnter", "e", "target", "transform", "boxShadow", "onMouseLeave", "left", "height", "<PERSON><PERSON>ilter", "WebkitBackdropFilter", "boxSizing", "overflowY", "currentTarget", "max<PERSON><PERSON><PERSON>", "maxHeight", "flexDirection", "margin", "stopPropagation", "textAlign", "borderTop", "animation", "textShadow", "flex", "overflowX", "minHeight", "lineHeight", "flexShrink", "gridTemplateColumns", "flexWrap", "paddingTop", "min<PERSON><PERSON><PERSON>", "visible", "onClose", "currentPlan", "find", "p", "onRenew", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Subscription/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { motion } from 'framer-motion';\nimport { message } from 'antd';\nimport { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';\nimport { getPlans } from '../../../apicalls/plans';\nimport { addPayment, checkPaymentStatus } from '../../../apicalls/payment';\nimport { ShowLoading, HideLoading } from '../../../redux/loaderSlice';\nimport UpgradeRestrictionModal from '../../../components/UpgradeRestrictionModal/UpgradeRestrictionModal';\nimport SubscriptionExpiredModal from '../../../components/SubscriptionExpiredModal/SubscriptionExpiredModal';\nimport './Subscription.css';\n\nconst Subscription = () => {\n  const [plans, setPlans] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [paymentLoading, setPaymentLoading] = useState(null); // Changed to store plan ID instead of boolean\n  const [showProcessingModal, setShowProcessingModal] = useState(false);\n\n  // Debug: Log showProcessingModal state changes\n  useEffect(() => {\n    console.log('🔍 showProcessingModal state changed to:', showProcessingModal);\n  }, [showProcessingModal]);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [paymentStatus, setPaymentStatus] = useState('');\n  const [showUpgradeRestriction, setShowUpgradeRestriction] = useState(false);\n  const [showExpiredModal, setShowExpiredModal] = useState(false);\n  const [processingStartTime, setProcessingStartTime] = useState(null);\n  const [showTryAgain, setShowTryAgain] = useState(false);\n  const [autoNavigateCountdown, setAutoNavigateCountdown] = useState(null);\n  const { user } = useSelector((state) => state.user);\n  const { subscriptionData } = useSelector((state) => state.subscription);\n  const dispatch = useDispatch();\n\n  // Fallback sample plans in case API fails\n  const samplePlans = [\n    {\n      _id: \"basic-plan-sample\",\n      title: \"Basic Membership\",\n      features: [\n        \"2-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\"\n      ],\n      actualPrice: 28570,\n      discountedPrice: 20000,\n      discountPercentage: 30,\n      duration: 2,\n      status: true\n    },\n    {\n      _id: \"premium-plan-sample\",\n      title: \"Premium Plan\",\n      features: [\n        \"3-month full access\",\n        \"Unlimited quizzes\",\n        \"Personalized profile\",\n        \"AI chat for instant help\",\n        \"Forum for student discussions\",\n        \"Study notes\",\n        \"Past papers\",\n        \"Books\",\n        \"Learning videos\",\n        \"Track progress with rankings\",\n        \"Priority support\"\n      ],\n      actualPrice: 45000,\n      discountedPrice: 35000,\n      discountPercentage: 22,\n      duration: 3,\n      status: true\n    }\n  ];\n\n  useEffect(() => {\n    fetchPlans();\n    checkCurrentSubscription();\n  }, []);\n\n  // Handle body scroll lock when modals are open (simplified approach)\n  useEffect(() => {\n    if (showProcessingModal || showSuccessModal) {\n      // Simply prevent body scroll without position fixed\n      document.body.style.overflow = 'hidden';\n    } else {\n      // Restore body scroll\n      document.body.style.overflow = '';\n    }\n\n    // Cleanup on unmount\n    return () => {\n      document.body.style.overflow = '';\n    };\n  }, [showProcessingModal, showSuccessModal]);\n\n  // Check for expired subscription and show modal\n  useEffect(() => {\n    if (subscriptionData && isSubscriptionExpired()) {\n      console.log('🚫 Subscription expired, showing modal');\n      setShowExpiredModal(true);\n    } else {\n      setShowExpiredModal(false);\n    }\n  }, [subscriptionData]);\n\n  const fetchPlans = async () => {\n    try {\n      setLoading(true);\n      console.log('Fetching plans...');\n      const response = await getPlans();\n      console.log('Plans response:', response);\n\n      if (response.success && response.data && response.data.length > 0) {\n        setPlans(response.data);\n        console.log('Plans loaded successfully from API:', response.data);\n      } else if (Array.isArray(response) && response.length > 0) {\n        // Handle case where response is directly an array of plans\n        setPlans(response);\n        console.log('Plans loaded as array from API:', response);\n      } else {\n        console.warn('No plans from API, using sample plans');\n        setPlans(samplePlans);\n        message.info('Showing sample plans. Please check your connection.');\n      }\n    } catch (error) {\n      console.error('Error loading plans from API:', error);\n      console.log('Using fallback sample plans');\n      setPlans(samplePlans);\n      message.warning('Using sample plans. Please check your connection and try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const checkCurrentSubscription = async () => {\n    try {\n      const response = await checkPaymentStatus();\n      console.log('Current subscription:', response);\n    } catch (error) {\n      console.log('No active subscription found');\n    }\n  };\n\n  // Check if subscription is expired\n  const isSubscriptionExpired = () => {\n    if (!subscriptionData) return true;\n\n    // If no subscription data, consider expired\n    if (!subscriptionData.endDate) return true;\n\n    // If payment status is not paid, consider expired\n    if (subscriptionData.paymentStatus !== 'paid') return true;\n\n    // If status is not active, consider expired\n    if (subscriptionData.status !== 'active') return true;\n\n    // Check if end date has passed\n    const endDate = new Date(subscriptionData.endDate);\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n    endDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    return endDate < today;\n  };\n\n  // Handle subscription renewal from expired modal\n  const handleRenewSubscription = async (selectedPlan) => {\n    setShowExpiredModal(false);\n    await handlePlanSelect(selectedPlan);\n  };\n\n  // Handle closing payment processing modal\n  const handleCloseProcessingModal = () => {\n    setShowProcessingModal(false);\n    setPaymentLoading(null); // Reset to null instead of false\n    setShowTryAgain(false);\n    setProcessingStartTime(null);\n    setPaymentStatus('');\n    message.info('Payment process cancelled. You can try again anytime.');\n  };\n\n  // Handle try again functionality\n  const handleTryAgain = () => {\n    if (selectedPlan) {\n      setShowTryAgain(false);\n      setProcessingStartTime(null);\n      handlePlanSelect(selectedPlan);\n    }\n  };\n\n  // Test success modal (for debugging)\n  const testSuccessModal = () => {\n    console.log('🧪 Testing success modal...');\n    setShowProcessingModal(false);\n    setShowSuccessModal(true);\n    setPaymentLoading(null);\n  };\n\n  // Test processing modal (for debugging)\n  const testProcessingModal = () => {\n    console.log('🧪 Testing processing modal...');\n    setShowProcessingModal(true);\n    setPaymentStatus('Testing processing modal...');\n    setSelectedPlan(plans[0] || { title: 'Test Plan', discountedPrice: 5000, duration: 1 });\n  };\n\n  const handlePlanSelect = async (plan) => {\n    // Check if user already has an active subscription\n    if (subscriptionData && subscriptionData.status === 'active' && subscriptionData.paymentStatus === 'paid') {\n      console.log('🚫 User already has active subscription:', subscriptionData);\n      setShowUpgradeRestriction(true);\n      return;\n    }\n\n    if (!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) {\n      message.error('Please update your phone number in your profile before subscribing');\n      return;\n    }\n\n    try {\n      console.log('🚀 Starting payment for plan:', plan.title);\n      console.log('🔧 IMMEDIATELY showing processing modal...');\n\n      // IMMEDIATELY show processing modal when user chooses plan\n      setSelectedPlan(plan);\n      setPaymentLoading(plan._id);\n      setShowProcessingModal(true);\n      setShowTryAgain(false);\n      setProcessingStartTime(Date.now());\n      setPaymentStatus('🚀 Preparing your payment request...');\n\n      console.log('✅ Processing modal IMMEDIATELY displayed');\n\n      // Small delay to ensure modal is visible before API call\n      await new Promise(resolve => setTimeout(resolve, 200));\n\n      // Set timer for try again button (10 seconds)\n      const tryAgainTimer = setTimeout(() => {\n        setShowTryAgain(true);\n      }, 10000);\n\n      const paymentData = {\n        plan: plan,\n        userId: user._id,\n        userPhone: user.phoneNumber,\n        userEmail: user.email || `${user.name?.replace(/\\s+/g, '').toLowerCase()}@brainwave.temp`\n      };\n\n      setPaymentStatus('📤 Sending payment request to ZenoPay...');\n      const response = await addPayment(paymentData);\n\n      if (response.success) {\n        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');\n\n        console.log('💳 Payment response:', response);\n        console.log('🆔 Order ID:', response.order_id);\n\n        // Show confirmation message to user\n        message.success({\n          content: `💳 Payment initiated! 📱 Check your phone (${user.phoneNumber}) for SMS confirmation from ZenoPay.`,\n          duration: 8,\n          style: {\n            marginTop: '20vh',\n          }\n        });\n\n        // Start checking payment status immediately\n        const orderIdToCheck = response.order_id || response.data?.order_id || 'demo_order';\n        console.log('🔍 Starting payment confirmation check for order:', orderIdToCheck);\n\n        checkPaymentConfirmation(orderIdToCheck);\n\n      } else {\n        throw new Error(response.message || 'Payment failed');\n      }\n    } catch (error) {\n      console.error('❌ Payment failed:', error);\n      setShowProcessingModal(false);\n      message.error('Payment failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const checkPaymentConfirmation = async (orderId) => {\n    console.log('🚀 Starting payment confirmation check for order:', orderId);\n    let isPolling = true;\n    let handleVisibilityChange;\n\n    try {\n      setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n\n      // Poll payment status every 2 seconds for optimal responsiveness\n      let attempts = 0;\n      const maxAttempts = 150; // 150 attempts * 2 seconds = 5 minutes\n\n      const pollPaymentStatus = async () => {\n        attempts++;\n        console.log(`🔍 Payment status check attempt ${attempts}/${maxAttempts} for order:`, orderId);\n\n        try {\n          const statusResponse = await checkPaymentStatus({ orderId });\n          console.log('📊 Payment status response:', statusResponse);\n          console.log('🔍 Checking payment conditions:');\n          console.log('  - Live payment:', statusResponse?.paymentStatus === 'paid' && statusResponse?.status === 'active');\n          console.log('  - Demo payment:', statusResponse?.status === 'completed' && statusResponse?.success === true);\n\n          if (statusResponse && (\n            (statusResponse.paymentStatus === 'paid' && statusResponse.status === 'active') ||\n            (statusResponse.status === 'completed' && statusResponse.success === true)\n          )) {\n            // Payment confirmed immediately!\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('🎉 Payment confirmed! Activating your subscription...');\n            console.log('✅ Payment confirmed, preparing to show success modal...');\n\n            // Show success INSTANTLY - no delay\n            console.log('🔄 Setting modal states - Processing: false, Success: true');\n            setShowProcessingModal(false);\n            setShowSuccessModal(true);\n            setPaymentLoading(null);\n            console.log('✅ Success modal state set to true');\n\n            // Refresh subscription data\n            checkCurrentSubscription();\n\n            // Show immediate success message\n            message.success({\n              content: '🎉 Payment confirmed! All features are now unlocked!',\n              duration: 5,\n              style: {\n                marginTop: '20vh',\n                fontSize: '16px'\n              }\n            });\n\n            // Start countdown for auto-navigation to hub\n            setAutoNavigateCountdown(5);\n            const countdownInterval = setInterval(() => {\n              setAutoNavigateCountdown(prev => {\n                if (prev <= 1) {\n                  clearInterval(countdownInterval);\n                  console.log('🏠 Auto-navigating to hub after successful payment...');\n                  setShowSuccessModal(false);\n                  window.location.href = '/user/hub';\n                  return null;\n                }\n                return prev - 1;\n              });\n            }, 1000);\n\n          } else if (attempts >= maxAttempts) {\n            // Timeout - but don't fail completely\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n\n            setPaymentStatus('⏰ Still waiting for confirmation. Please complete the payment on your phone.');\n\n            setTimeout(() => {\n              setShowProcessingModal(false);\n              setPaymentLoading(null); // Reset to null\n              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status or try again.');\n            }, 2000);\n\n          } else {\n            // Continue polling - NO TIME INDICATION, just encouraging message\n            setPaymentStatus('📱 Complete the payment on your phone, we\\'ll detect it automatically...');\n            setTimeout(pollPaymentStatus, 2000); // Check every 2 seconds for better performance\n          }\n\n        } catch (error) {\n          console.error('Payment status check error:', error);\n\n          // Handle specific error types\n          if (error.message && error.message.includes('404')) {\n            console.error('❌ Payment status endpoint not found (404)');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Payment verification service is temporarily unavailable. Please contact support or check your subscription status manually.');\n            return;\n          }\n\n          if (error.message && error.message.includes('401')) {\n            console.error('❌ Authentication required for payment status check');\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange);\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Please login again to check payment status.');\n            return;\n          }\n\n          if (attempts >= maxAttempts) {\n            isPolling = false; // Stop polling\n            if (handleVisibilityChange) {\n              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n            }\n            setShowProcessingModal(false);\n            setPaymentLoading(null); // Reset to null\n            message.error('Unable to confirm payment status. Please check your subscription status manually.');\n          } else {\n            // Continue polling even if there's an error (unless it's a critical error)\n            setTimeout(pollPaymentStatus, 1000);\n          }\n        }\n      };\n\n      // Add visibility change listener to check immediately when user returns to tab\n      handleVisibilityChange = () => {\n        if (!document.hidden && isPolling) {\n          console.log('User returned to tab, checking payment status immediately...');\n          setPaymentStatus('🔍 Checking payment status...');\n          // Trigger immediate check\n          setTimeout(() => pollPaymentStatus(), 100);\n        }\n      };\n\n      document.addEventListener('visibilitychange', handleVisibilityChange);\n\n      // Start polling immediately (no delay) - check right away\n      setTimeout(pollPaymentStatus, 500); // Start checking after 0.5 seconds\n\n    } catch (error) {\n      isPolling = false; // Stop polling\n      if (handleVisibilityChange) {\n        document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener\n      }\n      setShowProcessingModal(false);\n      message.error('Payment confirmation failed: ' + error.message);\n      setPaymentLoading(null); // Reset to null\n    }\n  };\n\n  const getSubscriptionStatus = () => {\n    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {\n      const endDate = new Date(subscriptionData.endDate);\n      const now = new Date();\n      if (endDate > now) {\n        return 'active';\n      }\n    }\n    \n    if (user?.subscriptionStatus === 'expired' || (subscriptionData && subscriptionData.status === 'expired')) {\n      return 'expired';\n    }\n    \n    return 'none';\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return 'N/A';\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const getDaysRemaining = () => {\n    if (!subscriptionData?.endDate) return 0;\n    const endDate = new Date(subscriptionData.endDate);\n    const now = new Date();\n    const diffTime = endDate - now;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(0, diffDays);\n  };\n\n  const subscriptionStatus = getSubscriptionStatus();\n\n  return (\n    <div className=\"subscription-page\">\n      <div className=\"subscription-container\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"subscription-header\"\n        >\n          {/* Debug button - remove in production */}\n          <button\n            onClick={() => {\n              console.log('🧪 Testing success modal...');\n              setSelectedPlan(plans[0] || { title: 'Test Plan', duration: 1, discountedPrice: 13000 });\n              setShowSuccessModal(true);\n            }}\n            style={{\n              position: 'fixed',\n              top: '10px',\n              right: '10px',\n              background: '#52c41a',\n              color: 'white',\n              border: 'none',\n              padding: '8px 16px',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              zIndex: 9999\n            }}\n          >\n            🧪 Test Success Modal\n          </button>\n          <h1 className=\"page-title\">\n            <FaCrown className=\"title-icon\" />\n            Subscription Management\n          </h1>\n          <p className=\"page-subtitle\">Manage your subscription and access premium features</p>\n        </motion.div>\n\n        {/* Current Subscription Status */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"current-subscription\"\n        >\n          <h2 className=\"section-title\">Current Subscription</h2>\n          \n          {subscriptionStatus === 'active' && (\n            <div className=\"subscription-card active\">\n              <div className=\"subscription-status\">\n                <FaCheckCircle className=\"status-icon active\" />\n                <span className=\"status-text\">Active Subscription</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCrown className=\"detail-icon\" />\n                  <span>Plan: {subscriptionData?.activePlan?.title || 'Premium Plan'}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expires: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <div className=\"detail-item\">\n                  <FaCheckCircle className=\"detail-icon\" />\n                  <span>Days Remaining: {getDaysRemaining()}</span>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'expired' && (\n            <div className=\"subscription-card expired\">\n              <div className=\"subscription-status\">\n                <FaTimesCircle className=\"status-icon expired\" />\n                <span className=\"status-text\">Subscription Expired</span>\n              </div>\n              <div className=\"subscription-details\">\n                <div className=\"detail-item\">\n                  <FaCalendarAlt className=\"detail-icon\" />\n                  <span>Expired: {formatDate(subscriptionData?.endDate)}</span>\n                </div>\n                <p className=\"renewal-message\">\n                  Your subscription has expired. Choose a new plan below to continue accessing premium features.\n                </p>\n              </div>\n            </div>\n          )}\n\n          {subscriptionStatus === 'none' && (\n            <div className=\"subscription-card none\">\n              <div className=\"subscription-status\">\n                <FaUser className=\"status-icon none\" />\n                <span className=\"status-text\">Free Account</span>\n              </div>\n              <div className=\"subscription-details\">\n                <p className=\"upgrade-message\">\n                  You're currently using a free account. Upgrade to a premium plan to unlock all features.\n                </p>\n              </div>\n            </div>\n          )}\n        </motion.div>\n\n        {/* Available Plans */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"available-plans\"\n        >\n          <h2 className=\"section-title\">\n            {subscriptionStatus === 'active'\n              ? '🚀 Upgrade Your Plan'\n              : subscriptionStatus === 'expired'\n                ? '🔄 Renew Your Subscription'\n                : '🎯 Choose Your Plan'\n            }\n          </h2>\n\n          {/* Temporary Test Buttons */}\n          <div style={{ display: 'flex', gap: '10px', justifyContent: 'center', marginBottom: '20px' }}>\n            <button\n              onClick={testProcessingModal}\n              style={{\n                background: '#ff6b6b',\n                color: 'white',\n                border: 'none',\n                padding: '8px 16px',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              🧪 Test Processing Modal\n            </button>\n            <button\n              onClick={testSuccessModal}\n              style={{\n                background: '#51cf66',\n                color: 'white',\n                border: 'none',\n                padding: '8px 16px',\n                borderRadius: '6px',\n                cursor: 'pointer',\n                fontSize: '12px'\n              }}\n            >\n              🧪 Test Success Modal\n            </button>\n          </div>\n          <p className=\"section-subtitle\">\n            {subscriptionStatus === 'active'\n              ? 'Upgrade to a longer plan for better value and extended access'\n              : subscriptionStatus === 'expired'\n                ? 'Your subscription has expired. Renew now to continue accessing premium features'\n                : 'Select a subscription plan to unlock all premium features and start your learning journey'\n            }\n          </p>\n          \n          {loading ? (\n            <div className=\"loading-state\">\n              <div className=\"spinner\"></div>\n              <p>Loading plans...</p>\n            </div>\n          ) : plans.length === 0 ? (\n            <div className=\"no-plans-state\">\n              <div className=\"no-plans-icon\">📋</div>\n              <h3>No Plans Available</h3>\n              <p>Plans are currently being loaded. Please refresh the page or try again later.</p>\n              <button className=\"refresh-btn\" onClick={fetchPlans}>\n                🔄 Refresh Plans\n              </button>\n            </div>\n          ) : (\n            <div className=\"plans-grid\">\n              {plans.map((plan) => (\n                <motion.div\n                  key={plan._id}\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  className=\"plan-card\"\n                >\n                  <div className=\"plan-header\">\n                    <h3 className=\"plan-title\">{plan.title}</h3>\n                    {plan.title?.toLowerCase().includes('standard') && (\n                      <span className=\"plan-badge\">🔥 Popular</span>\n                    )}\n                  </div>\n                  \n                  <div className=\"plan-pricing\">\n                    <div className=\"price-display\">\n                      <div className=\"current-price\">\n                        <span className=\"currency\">TZS</span>\n                        {plan.discountedPrice?.toLocaleString()}\n                      </div>\n                      {plan.actualPrice > plan.discountedPrice && (\n                        <>\n                          <span className=\"original-price\">{plan.actualPrice?.toLocaleString()} TZS</span>\n                          <span className=\"discount-badge\">\n                            {Math.round(((plan.actualPrice - plan.discountedPrice) / plan.actualPrice) * 100)}% OFF\n                          </span>\n                        </>\n                      )}\n                    </div>\n                    <div className=\"plan-duration\">\n                      <span className=\"duration-highlight\">{plan.duration}</span> month{plan.duration > 1 ? 's' : ''} access\n                    </div>\n                  </div>\n\n                  <div className=\"plan-features\">\n                    {plan.features?.slice(0, 5).map((feature, index) => (\n                      <div key={index} className=\"feature-item\">\n                        <FaCheckCircle className=\"feature-icon\" />\n                        <span>{feature}</span>\n                      </div>\n                    ))}\n                  </div>\n\n                  <button\n                    className=\"select-plan-btn\"\n                    onClick={() => handlePlanSelect(plan)}\n                    disabled={paymentLoading === plan._id}\n                    style={{\n                      background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '12px',\n                      padding: '1rem 1.5rem',\n                      fontSize: '1rem',\n                      fontWeight: '600',\n                      cursor: paymentLoading === plan._id ? 'not-allowed' : 'pointer',\n                      transition: 'all 0.3s ease',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      gap: '0.5rem',\n                      width: '100%',\n                      opacity: paymentLoading === plan._id ? 0.6 : 1\n                    }}\n                    onMouseEnter={(e) => {\n                      if (paymentLoading !== plan._id) {\n                        e.target.style.background = 'linear-gradient(135deg, #1d4ed8, #1e40af)';\n                        e.target.style.transform = 'translateY(-2px)';\n                        e.target.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.4)';\n                      }\n                    }}\n                    onMouseLeave={(e) => {\n                      if (paymentLoading !== plan._id) {\n                        e.target.style.background = 'linear-gradient(135deg, #3b82f6, #1d4ed8)';\n                        e.target.style.transform = 'translateY(0)';\n                        e.target.style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.3)';\n                      }\n                    }}\n                  >\n                    <FaCreditCard className=\"btn-icon\" />\n                    {paymentLoading === plan._id\n                      ? 'Processing...'\n                      : subscriptionStatus === 'active'\n                        ? 'Click to Upgrade'\n                        : subscriptionStatus === 'expired'\n                          ? 'Click to Renew'\n                          : 'Click to Pay'\n                    }\n                  </button>\n                </motion.div>\n              ))}\n            </div>\n          )}\n        </motion.div>\n\n        {/* Phone Number Warning */}\n        {(!user.phoneNumber || !/^(06|07)\\d{8}$/.test(user.phoneNumber)) && (\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6, delay: 0.6 }}\n            className=\"phone-warning\"\n          >\n            <div className=\"warning-content\">\n              <FaTimesCircle className=\"warning-icon\" />\n              <div>\n                <h4>Phone Number Required</h4>\n                <p>Please update your phone number in your profile to subscribe to a plan.</p>\n                <button \n                  className=\"update-phone-btn\"\n                  onClick={() => window.location.href = '/profile'}\n                >\n                  Update Phone Number\n                </button>\n              </div>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Enhanced Payment Processing Modal */}\n        {showProcessingModal && (\n          <div\n            className=\"modal-overlay-best\"\n            style={{\n              position: 'fixed',\n              top: '0',\n              left: '0',\n              width: '100vw',\n              height: '100vh',\n              background: 'rgba(0, 0, 0, 0.4)', // Lighter overlay\n              backdropFilter: 'blur(8px)',\n              WebkitBackdropFilter: 'blur(8px)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              zIndex: '10000',\n              padding: '20px',\n              boxSizing: 'border-box',\n              overflowY: 'auto' // Enable scrolling\n            }}\n            onClick={(e) => e.target === e.currentTarget && handleCloseProcessingModal()}\n          >\n            <div\n              className=\"modal-container-best\"\n              style={{\n                background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',\n                borderRadius: '20px',\n                boxShadow: '0 20px 60px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.1)',\n                border: '1px solid rgba(59, 130, 246, 0.1)',\n                width: '100%',\n                maxWidth: '600px', // Larger modal\n                maxHeight: '85vh', // More height\n                display: 'flex',\n                flexDirection: 'column',\n                overflow: 'hidden',\n                position: 'relative',\n                transform: 'translateZ(0)',\n                margin: 'auto' // Center in scrollable area\n              }}\n              onClick={(e) => e.stopPropagation()}\n            >\n              {/* Professional Header */}\n              <div style={{\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                padding: '1.5rem',\n                color: 'white',\n                textAlign: 'center',\n                position: 'relative'\n              }}>\n                <button\n                  className=\"payment-modal-close\"\n                  onClick={handleCloseProcessingModal}\n                  title=\"Close payment window\"\n                  style={{\n                    position: 'absolute',\n                    top: '12px',\n                    right: '12px',\n                    background: 'rgba(255, 255, 255, 0.2)',\n                    border: 'none',\n                    borderRadius: '50%',\n                    width: '32px',\n                    height: '32px',\n                    color: 'white',\n                    fontSize: '16px',\n                    cursor: 'pointer',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    transition: 'all 0.3s ease',\n                    zIndex: 10\n                  }}\n                  onMouseEnter={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.3)'}\n                  onMouseLeave={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.2)'}\n                >\n                  ✕\n                </button>\n\n                <div style={{ marginBottom: '0.5rem' }}>\n                  <div className=\"payment-processing-animation\" style={{\n                    width: '60px',\n                    height: '60px',\n                    margin: '0 auto 1rem',\n                    position: 'relative'\n                  }}>\n                    <div className=\"payment-spinner\" style={{\n                      width: '100%',\n                      height: '100%',\n                      border: '3px solid rgba(255, 255, 255, 0.3)',\n                      borderTop: '3px solid white',\n                      borderRadius: '50%',\n                      animation: 'spin 1s linear infinite'\n                    }}></div>\n                    <div style={{\n                      position: 'absolute',\n                      top: '50%',\n                      left: '50%',\n                      transform: 'translate(-50%, -50%)',\n                      fontSize: '20px'\n                    }}>💳</div>\n                  </div>\n                </div>\n\n                <h3 style={{\n                  margin: '0',\n                  fontSize: '1.4rem',\n                  fontWeight: '700',\n                  textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'\n                }}>\n                  Processing Payment\n                </h3>\n                <p style={{\n                  margin: '0.5rem 0 0 0',\n                  fontSize: '0.9rem',\n                  opacity: '0.9'\n                }}>\n                  Secure transaction in progress\n                </p>\n              </div>\n\n              {/* Scrollable Content Area */}\n              <div style={{\n                flex: '1',\n                overflowY: 'auto',\n                overflowX: 'hidden',\n                padding: '0'\n              }}>\n                <div style={{\n                  padding: '20px',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: '16px',\n                  minHeight: '100%'\n                }}>\n                {/* Payment Status */}\n                <div style={{\n                  background: 'rgba(59, 130, 246, 0.05)',\n                  border: '1px solid rgba(59, 130, 246, 0.2)',\n                  borderRadius: '10px',\n                  padding: '0.75rem',\n                  textAlign: 'center'\n                }}>\n                  <p style={{\n                    margin: '0',\n                    color: '#1e40af',\n                    fontSize: '0.95rem',\n                    fontWeight: '500'\n                  }}>\n                    {paymentStatus}\n                  </p>\n                </div>\n\n                {/* Plan Details */}\n                <div style={{\n                  background: 'rgba(16, 185, 129, 0.05)',\n                  border: '1px solid rgba(16, 185, 129, 0.2)',\n                  borderRadius: '10px',\n                  padding: '0.75rem'\n                }}>\n                  <h4 style={{\n                    margin: '0 0 0.5rem 0',\n                    color: '#065f46',\n                    fontSize: '1.1rem',\n                    fontWeight: '600'\n                  }}>\n                    {selectedPlan?.title}\n                  </h4>\n                  <div style={{\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    fontSize: '0.9rem',\n                    color: '#047857'\n                  }}>\n                    <span>Amount: <strong>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</strong></span>\n                    <span>Duration: <strong>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</strong></span>\n                  </div>\n                </div>\n\n                {/* Phone Instructions */}\n                <div style={{\n                  background: 'rgba(245, 158, 11, 0.05)',\n                  border: '1px solid rgba(245, 158, 11, 0.2)',\n                  borderRadius: '10px',\n                  padding: '0.75rem'\n                }}>\n                  <h4 style={{\n                    margin: '0 0 0.75rem 0',\n                    color: '#92400e',\n                    fontSize: '1rem',\n                    fontWeight: '600',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.5rem'\n                  }}>\n                    📱 Check Your Phone\n                  </h4>\n                  <p style={{\n                    margin: '0 0 0.75rem 0',\n                    color: '#b45309',\n                    fontSize: '0.9rem',\n                    fontWeight: '500',\n                    background: 'rgba(245, 158, 11, 0.1)',\n                    padding: '0.5rem',\n                    borderRadius: '8px',\n                    textAlign: 'center'\n                  }}>\n                    Number: {user?.phoneNumber}\n                  </p>\n                  <div style={{\n                    color: '#a16207',\n                    fontSize: '0.85rem',\n                    lineHeight: '1.5'\n                  }}>\n                    <div style={{ marginBottom: '0.25rem' }}>1. You will receive an SMS with payment instructions</div>\n                    <div style={{ marginBottom: '0.25rem' }}>2. Follow the SMS steps to confirm payment</div>\n                    <div>3. Complete the mobile money transaction</div>\n                  </div>\n                </div>\n\n                  {/* Try Again Section - Always Visible */}\n                  {showTryAgain && (\n                    <div style={{\n                      background: 'rgba(239, 68, 68, 0.05)',\n                      border: '1px solid rgba(239, 68, 68, 0.2)',\n                      borderRadius: '12px',\n                      padding: '16px',\n                      textAlign: 'center',\n                      marginTop: '8px'\n                    }}>\n                    <p style={{\n                      margin: '0 0 0.5rem 0',\n                      color: '#dc2626',\n                      fontSize: '0.9rem',\n                      fontWeight: '600'\n                    }}>\n                      ⚠️ Taking longer than expected?\n                    </p>\n                    <p style={{\n                      margin: '0 0 1rem 0',\n                      color: '#b91c1c',\n                      fontSize: '0.8rem'\n                    }}>\n                      If you haven't received SMS or facing connection issues:\n                    </p>\n                    <button\n                      className=\"try-again-button\"\n                      onClick={handleTryAgain}\n                      style={{\n                        background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                        color: 'white',\n                        border: 'none',\n                        padding: '0.75rem 1.5rem',\n                        borderRadius: '10px',\n                        fontSize: '0.9rem',\n                        fontWeight: '600',\n                        cursor: 'pointer',\n                        transition: 'all 0.3s ease',\n                        boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',\n                        width: '100%',\n                        maxWidth: '200px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        gap: '0.5rem',\n                        margin: '0 auto'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.target.style.background = 'linear-gradient(135deg, #1d4ed8 0%, #1e3a8a 100%)';\n                        e.target.style.transform = 'translateY(-2px)';\n                        e.target.style.boxShadow = '0 6px 16px rgba(59, 130, 246, 0.4)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.target.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';\n                        e.target.style.transform = 'translateY(0)';\n                        e.target.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.3)';\n                      }}\n                    >\n                      🔄 Try Again\n                    </button>\n                  </div>\n                )}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Best Design Success Modal */}\n        {showSuccessModal && (\n          <div\n            className=\"modal-overlay-best\"\n            style={{\n              position: 'fixed',\n              top: '0',\n              left: '0',\n              width: '100vw',\n              height: '100vh',\n              background: 'rgba(0, 0, 0, 0.9)',\n              backdropFilter: 'blur(20px)',\n              WebkitBackdropFilter: 'blur(20px)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              zIndex: '10000',\n              padding: '20px',\n              boxSizing: 'border-box'\n            }}\n            onClick={(e) => {\n              if (e.target === e.currentTarget) {\n                setAutoNavigateCountdown(null); // Clear countdown\n                setShowSuccessModal(false);\n              }\n            }}\n          >\n            <div\n              className=\"modal-container-best success-modal-best\"\n              style={{\n                background: 'linear-gradient(145deg, #ffffff 0%, #f0fdf4 100%)',\n                borderRadius: '24px',\n                boxShadow: '0 25px 80px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(34, 197, 94, 0.2)',\n                border: '2px solid rgba(34, 197, 94, 0.1)',\n                width: '100%',\n                maxWidth: '600px',\n                maxHeight: '95vh',\n                display: 'flex',\n                flexDirection: 'column',\n                overflow: 'hidden',\n                position: 'relative',\n                transform: 'translateZ(0)'\n              }}\n              onClick={(e) => e.stopPropagation()}\n            >\n              {/* Professional Success Header */}\n              <div style={{\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                padding: '2rem',\n                color: 'white',\n                textAlign: 'center',\n                position: 'relative',\n                borderRadius: '28px 28px 0 0',\n                flexShrink: 0\n              }}>\n                <button\n                  onClick={() => {\n                    setAutoNavigateCountdown(null); // Clear countdown\n                    setShowSuccessModal(false);\n                  }}\n                  style={{\n                    position: 'absolute',\n                    top: '16px',\n                    right: '16px',\n                    background: 'rgba(255, 255, 255, 0.2)',\n                    border: 'none',\n                    borderRadius: '50%',\n                    width: '36px',\n                    height: '36px',\n                    color: 'white',\n                    fontSize: '18px',\n                    cursor: 'pointer',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    transition: 'all 0.3s ease',\n                    zIndex: 10\n                  }}\n                  onMouseEnter={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.3)'}\n                  onMouseLeave={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.2)'}\n                >\n                  ✕\n                </button>\n\n                <div style={{\n                  fontSize: '4rem',\n                  marginBottom: '1rem',\n                  animation: 'bounce 2s infinite'\n                }}>\n                  🎉\n                </div>\n\n                <h2 style={{\n                  margin: '0',\n                  fontSize: '2rem',\n                  fontWeight: '800',\n                  textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',\n                  marginBottom: '0.5rem'\n                }}>\n                  Payment Successful!\n                </h2>\n\n                <p style={{\n                  margin: '0',\n                  fontSize: '1.1rem',\n                  opacity: '0.95',\n                  fontWeight: '500'\n                }}>\n                  Welcome to {selectedPlan?.title}! 🚀\n                </p>\n              </div>\n\n              {/* Scrollable Success Content */}\n              <div style={{\n                flex: '1',\n                overflowY: 'auto',\n                overflowX: 'hidden',\n                padding: '0'\n              }}>\n                <div style={{\n                  padding: '24px',\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: '20px',\n                  minHeight: '100%'\n                }}>\n                {/* Success Status Card */}\n                <div style={{\n                  background: 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)',\n                  border: '2px solid #22c55e',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  textAlign: 'center',\n                  boxShadow: '0 4px 20px rgba(34, 197, 94, 0.2)'\n                }}>\n                  <div style={{\n                    background: 'rgba(34, 197, 94, 0.1)',\n                    borderRadius: '12px',\n                    padding: '1rem',\n                    marginBottom: '1rem'\n                  }}>\n                    <h3 style={{\n                      color: '#15803d',\n                      marginBottom: '1rem',\n                      fontSize: '1.2rem',\n                      fontWeight: '700'\n                    }}>\n                      🎯 Subscription Details\n                    </h3>\n                    <div style={{\n                      display: 'grid',\n                      gap: '0.75rem',\n                      textAlign: 'left'\n                    }}>\n                      <div style={{\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.5rem',\n                        background: 'rgba(255, 255, 255, 0.7)',\n                        borderRadius: '8px'\n                      }}>\n                        <span style={{ fontWeight: '600', color: '#374151' }}>📋 Plan:</span>\n                        <span style={{ color: '#059669', fontWeight: '700' }}>{selectedPlan?.title}</span>\n                      </div>\n                      <div style={{\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.5rem',\n                        background: 'rgba(255, 255, 255, 0.7)',\n                        borderRadius: '8px'\n                      }}>\n                        <span style={{ fontWeight: '600', color: '#374151' }}>⏰ Duration:</span>\n                        <span style={{ color: '#059669', fontWeight: '700' }}>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</span>\n                      </div>\n                      <div style={{\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.5rem',\n                        background: 'rgba(255, 255, 255, 0.7)',\n                        borderRadius: '8px'\n                      }}>\n                        <span style={{ fontWeight: '600', color: '#374151' }}>💰 Amount:</span>\n                        <span style={{ color: '#059669', fontWeight: '700' }}>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</span>\n                      </div>\n                      <div style={{\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        padding: '0.5rem',\n                        background: 'linear-gradient(135deg, #22c55e, #16a34a)',\n                        borderRadius: '8px',\n                        color: 'white'\n                      }}>\n                        <span style={{ fontWeight: '600' }}>💎 Status:</span>\n                        <span style={{ fontWeight: '800', fontSize: '1.1rem' }}>ACTIVE ✅</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Features Unlocked Card */}\n                <div style={{\n                  background: 'linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%)',\n                  border: '2px solid #f59e0b',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  boxShadow: '0 4px 20px rgba(245, 158, 11, 0.2)'\n                }}>\n                  <h3 style={{\n                    color: '#92400e',\n                    marginBottom: '1rem',\n                    textAlign: 'center',\n                    fontSize: '1.2rem',\n                    fontWeight: '700'\n                  }}>\n                    🚀 All Premium Features Unlocked!\n                  </h3>\n                  <div style={{\n                    display: 'grid',\n                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                    gap: '1rem'\n                  }}>\n                    <div style={{\n                      background: 'rgba(255, 255, 255, 0.8)',\n                      borderRadius: '12px',\n                      padding: '1rem'\n                    }}>\n                      <div style={{ marginBottom: '0.5rem', fontSize: '0.95rem', fontWeight: '600', color: '#374151' }}>\n                        <div style={{ marginBottom: '0.25rem' }}>✅ <strong>Unlimited Quizzes</strong></div>\n                        <div style={{ marginBottom: '0.25rem' }}>🤖 <strong>AI Assistant</strong></div>\n                        <div>📚 <strong>Study Materials</strong></div>\n                      </div>\n                    </div>\n                    <div style={{\n                      background: 'rgba(255, 255, 255, 0.8)',\n                      borderRadius: '12px',\n                      padding: '1rem'\n                    }}>\n                      <div style={{ marginBottom: '0.5rem', fontSize: '0.95rem', fontWeight: '600', color: '#374151' }}>\n                        <div style={{ marginBottom: '0.25rem' }}>📊 <strong>Progress Tracking</strong></div>\n                        <div style={{ marginBottom: '0.25rem' }}>🎥 <strong>Learning Videos</strong></div>\n                        <div>💬 <strong>Forum Access</strong></div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Auto-Navigation Notice */}\n                {autoNavigateCountdown && (\n                  <div style={{\n                    background: 'linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%)',\n                    border: '2px solid #0288d1',\n                    borderRadius: '12px',\n                    padding: '1rem',\n                    textAlign: 'center',\n                    marginTop: '1rem'\n                  }}>\n                    <p style={{\n                      margin: '0',\n                      color: '#01579b',\n                      fontSize: '0.9rem',\n                      fontWeight: '600'\n                    }}>\n                      🚀 Automatically redirecting to Hub in {autoNavigateCountdown} seconds...\n                    </p>\n                  </div>\n                )}\n\n                {/* Action Buttons */}\n                <div style={{\n                  display: 'flex',\n                  gap: '1rem',\n                  justifyContent: 'center',\n                  flexWrap: 'wrap',\n                  marginTop: 'auto',\n                  paddingTop: '1rem'\n                }}>\n                  <button\n                    onClick={() => {\n                      setAutoNavigateCountdown(null); // Clear countdown\n                      setShowSuccessModal(false);\n                      window.location.href = '/user/hub';\n                    }}\n                    style={{\n                      background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                      border: 'none',\n                      color: 'white',\n                      padding: '1rem 2rem',\n                      borderRadius: '12px',\n                      fontSize: '1rem',\n                      fontWeight: '700',\n                      cursor: 'pointer',\n                      transition: 'all 0.3s ease',\n                      boxShadow: '0 4px 20px rgba(59, 130, 246, 0.3)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      minWidth: '180px',\n                      justifyContent: 'center'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.target.style.background = 'linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%)';\n                      e.target.style.transform = 'translateY(-2px)';\n                      e.target.style.boxShadow = '0 6px 25px rgba(59, 130, 246, 0.4)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.target.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';\n                      e.target.style.transform = 'translateY(0)';\n                      e.target.style.boxShadow = '0 4px 20px rgba(59, 130, 246, 0.3)';\n                    }}\n                  >\n                    🏠 Continue to Hub {autoNavigateCountdown ? `(${autoNavigateCountdown}s)` : ''}\n                  </button>\n                  <button\n                    onClick={() => {\n                      setAutoNavigateCountdown(null); // Clear countdown\n                      setShowSuccessModal(false);\n                    }}\n                    style={{\n                      background: 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)',\n                      border: '2px solid #d1d5db',\n                      color: '#374151',\n                      padding: '1rem 2rem',\n                      borderRadius: '12px',\n                      fontSize: '1rem',\n                      fontWeight: '600',\n                      cursor: 'pointer',\n                      transition: 'all 0.3s ease',\n                      boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      minWidth: '120px',\n                      justifyContent: 'center'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.target.style.background = 'linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%)';\n                      e.target.style.transform = 'translateY(-1px)';\n                      e.target.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.15)';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.target.style.background = 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)';\n                      e.target.style.transform = 'translateY(0)';\n                      e.target.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';\n                    }}\n                  >\n                    ✨ Close\n                  </button>\n                </div>\n\n                {/* Congratulations Message */}\n                <div style={{\n                  marginTop: '1.5rem',\n                  padding: '1rem',\n                  background: 'rgba(34, 197, 94, 0.1)',\n                  borderRadius: '12px',\n                  textAlign: 'center',\n                  border: '1px solid rgba(34, 197, 94, 0.2)'\n                }}>\n                  <p style={{\n                    margin: '0',\n                    fontSize: '1rem',\n                    color: '#15803d',\n                    fontWeight: '600',\n                    lineHeight: '1.5'\n                  }}>\n                    🎉 Congratulations! You now have full access to all BrainWave features. Start exploring and excel in your studies! 🚀\n                  </p>\n                </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Upgrade Restriction Modal */}\n        <UpgradeRestrictionModal\n          visible={showUpgradeRestriction}\n          onClose={() => setShowUpgradeRestriction(false)}\n          currentPlan={plans.find(p => p._id === subscriptionData?.activePlan) || subscriptionData?.plan}\n          subscription={subscriptionData}\n          user={user}\n        />\n\n        {/* Subscription Expired Modal */}\n        <SubscriptionExpiredModal\n          visible={showExpiredModal}\n          onClose={() => setShowExpiredModal(false)}\n          onRenew={handleRenewSubscription}\n          subscription={subscriptionData}\n          user={user}\n          plans={plans}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default Subscription;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,OAAO,EAAEC,aAAa,EAAEC,aAAa,EAAEC,aAAa,EAAEC,YAAY,EAAEC,MAAM,QAAQ,gBAAgB;AAC3G,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,2BAA2B;AAC1E,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,OAAOC,uBAAuB,MAAM,qEAAqE;AACzG,OAAOC,wBAAwB,MAAM,uEAAuE;AAC5G,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EACzB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACkC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;;EAErE;EACAC,SAAS,CAAC,MAAM;IACdmC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEH,mBAAmB,CAAC;EAC9E,CAAC,EAAE,CAACA,mBAAmB,CAAC,CAAC;EACzB,MAAM,CAACI,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACwC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4C,sBAAsB,EAAEC,yBAAyB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC3E,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACgD,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoD,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACxE,MAAM;IAAEsD;EAAK,CAAC,GAAGpD,WAAW,CAAEqD,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EACnD,MAAM;IAAEE;EAAiB,CAAC,GAAGtD,WAAW,CAAEqD,KAAK,IAAKA,KAAK,CAACE,YAAY,CAAC;EACvE,MAAMC,QAAQ,GAAGvD,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMwD,WAAW,GAAG,CAClB;IACEC,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,kBAAkB;IACzBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,CAC/B;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,EACD;IACEP,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE,cAAc;IACrBC,QAAQ,EAAE,CACR,qBAAqB,EACrB,mBAAmB,EACnB,sBAAsB,EACtB,0BAA0B,EAC1B,+BAA+B,EAC/B,aAAa,EACb,aAAa,EACb,OAAO,EACP,iBAAiB,EACjB,8BAA8B,EAC9B,kBAAkB,CACnB;IACDC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,KAAK;IACtBC,kBAAkB,EAAE,EAAE;IACtBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC,CACF;EAEDlE,SAAS,CAAC,MAAM;IACdmE,UAAU,CAAC,CAAC;IACZC,wBAAwB,CAAC,CAAC;EAC5B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACApE,SAAS,CAAC,MAAM;IACd,IAAIiC,mBAAmB,IAAII,gBAAgB,EAAE;MAC3C;MACAgC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ;IACzC,CAAC,MAAM;MACL;MACAH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;IACnC;;IAEA;IACA,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,EAAE;IACnC,CAAC;EACH,CAAC,EAAE,CAACvC,mBAAmB,EAAEI,gBAAgB,CAAC,CAAC;;EAE3C;EACArC,SAAS,CAAC,MAAM;IACd,IAAIuD,gBAAgB,IAAIkB,qBAAqB,CAAC,CAAC,EAAE;MAC/CtC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrDU,mBAAmB,CAAC,IAAI,CAAC;IAC3B,CAAC,MAAM;MACLA,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC,EAAE,CAACS,gBAAgB,CAAC,CAAC;EAEtB,MAAMY,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFrC,UAAU,CAAC,IAAI,CAAC;MAChBK,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAChC,MAAMsC,QAAQ,GAAG,MAAM/D,QAAQ,CAAC,CAAC;MACjCwB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEsC,QAAQ,CAAC;MAExC,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;QACjEjD,QAAQ,CAAC8C,QAAQ,CAACE,IAAI,CAAC;QACvBzC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEsC,QAAQ,CAACE,IAAI,CAAC;MACnE,CAAC,MAAM,IAAIE,KAAK,CAACC,OAAO,CAACL,QAAQ,CAAC,IAAIA,QAAQ,CAACG,MAAM,GAAG,CAAC,EAAE;QACzD;QACAjD,QAAQ,CAAC8C,QAAQ,CAAC;QAClBvC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEsC,QAAQ,CAAC;MAC1D,CAAC,MAAM;QACLvC,OAAO,CAAC6C,IAAI,CAAC,uCAAuC,CAAC;QACrDpD,QAAQ,CAAC8B,WAAW,CAAC;QACrBtD,OAAO,CAAC6E,IAAI,CAAC,qDAAqD,CAAC;MACrE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd/C,OAAO,CAAC+C,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD/C,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1CR,QAAQ,CAAC8B,WAAW,CAAC;MACrBtD,OAAO,CAAC+E,OAAO,CAAC,iEAAiE,CAAC;IACpF,CAAC,SAAS;MACRrD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsC,wBAAwB,GAAG,MAAAA,CAAA,KAAY;IAC3C,IAAI;MACF,MAAMM,QAAQ,GAAG,MAAM7D,kBAAkB,CAAC,CAAC;MAC3CsB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEsC,QAAQ,CAAC;IAChD,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACd/C,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAC7C;EACF,CAAC;;EAED;EACA,MAAMqC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAAClB,gBAAgB,EAAE,OAAO,IAAI;;IAElC;IACA,IAAI,CAACA,gBAAgB,CAAC6B,OAAO,EAAE,OAAO,IAAI;;IAE1C;IACA,IAAI7B,gBAAgB,CAACd,aAAa,KAAK,MAAM,EAAE,OAAO,IAAI;;IAE1D;IACA,IAAIc,gBAAgB,CAACW,MAAM,KAAK,QAAQ,EAAE,OAAO,IAAI;;IAErD;IACA,MAAMkB,OAAO,GAAG,IAAIC,IAAI,CAAC9B,gBAAgB,CAAC6B,OAAO,CAAC;IAClD,MAAME,KAAK,GAAG,IAAID,IAAI,CAAC,CAAC;IACxBC,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC5BH,OAAO,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAE9B,OAAOH,OAAO,GAAGE,KAAK;EACxB,CAAC;;EAED;EACA,MAAME,uBAAuB,GAAG,MAAOjD,YAAY,IAAK;IACtDO,mBAAmB,CAAC,KAAK,CAAC;IAC1B,MAAM2C,gBAAgB,CAAClD,YAAY,CAAC;EACtC,CAAC;;EAED;EACA,MAAMmD,0BAA0B,GAAGA,CAAA,KAAM;IACvCxD,sBAAsB,CAAC,KAAK,CAAC;IAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IACzBkB,eAAe,CAAC,KAAK,CAAC;IACtBF,sBAAsB,CAAC,IAAI,CAAC;IAC5BN,gBAAgB,CAAC,EAAE,CAAC;IACpBtC,OAAO,CAAC6E,IAAI,CAAC,uDAAuD,CAAC;EACvE,CAAC;;EAED;EACA,MAAMU,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIpD,YAAY,EAAE;MAChBW,eAAe,CAAC,KAAK,CAAC;MACtBF,sBAAsB,CAAC,IAAI,CAAC;MAC5ByC,gBAAgB,CAAClD,YAAY,CAAC;IAChC;EACF,CAAC;;EAED;EACA,MAAMqD,gBAAgB,GAAGA,CAAA,KAAM;IAC7BzD,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC1CF,sBAAsB,CAAC,KAAK,CAAC;IAC7BI,mBAAmB,CAAC,IAAI,CAAC;IACzBN,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM6D,mBAAmB,GAAGA,CAAA,KAAM;IAChC1D,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAC7CF,sBAAsB,CAAC,IAAI,CAAC;IAC5BQ,gBAAgB,CAAC,6BAA6B,CAAC;IAC/CF,eAAe,CAACb,KAAK,CAAC,CAAC,CAAC,IAAI;MAAEiC,KAAK,EAAE,WAAW;MAAEG,eAAe,EAAE,IAAI;MAAEE,QAAQ,EAAE;IAAE,CAAC,CAAC;EACzF,CAAC;EAED,MAAMwB,gBAAgB,GAAG,MAAOK,IAAI,IAAK;IACvC;IACA,IAAIvC,gBAAgB,IAAIA,gBAAgB,CAACW,MAAM,KAAK,QAAQ,IAAIX,gBAAgB,CAACd,aAAa,KAAK,MAAM,EAAE;MACzGN,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEmB,gBAAgB,CAAC;MACzEX,yBAAyB,CAAC,IAAI,CAAC;MAC/B;IACF;IAEA,IAAI,CAACS,IAAI,CAAC0C,WAAW,IAAI,CAAC,gBAAgB,CAACC,IAAI,CAAC3C,IAAI,CAAC0C,WAAW,CAAC,EAAE;MACjE3F,OAAO,CAAC8E,KAAK,CAAC,oEAAoE,CAAC;MACnF;IACF;IAEA,IAAI;MAAA,IAAAe,UAAA;MACF9D,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE0D,IAAI,CAAClC,KAAK,CAAC;MACxDzB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;;MAEzD;MACAI,eAAe,CAACsD,IAAI,CAAC;MACrB9D,iBAAiB,CAAC8D,IAAI,CAACnC,GAAG,CAAC;MAC3BzB,sBAAsB,CAAC,IAAI,CAAC;MAC5BgB,eAAe,CAAC,KAAK,CAAC;MACtBF,sBAAsB,CAACqC,IAAI,CAACa,GAAG,CAAC,CAAC,CAAC;MAClCxD,gBAAgB,CAAC,sCAAsC,CAAC;MAExDP,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;;MAEvD;MACA,MAAM,IAAI+D,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;;MAEtD;MACA,MAAME,aAAa,GAAGD,UAAU,CAAC,MAAM;QACrCnD,eAAe,CAAC,IAAI,CAAC;MACvB,CAAC,EAAE,KAAK,CAAC;MAET,MAAMqD,WAAW,GAAG;QAClBT,IAAI,EAAEA,IAAI;QACVU,MAAM,EAAEnD,IAAI,CAACM,GAAG;QAChB8C,SAAS,EAAEpD,IAAI,CAAC0C,WAAW;QAC3BW,SAAS,EAAErD,IAAI,CAACsD,KAAK,IAAK,IAAAV,UAAA,GAAE5C,IAAI,CAACuD,IAAI,cAAAX,UAAA,uBAATA,UAAA,CAAWY,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACC,WAAW,CAAC,CAAE;MAC3E,CAAC;MAEDpE,gBAAgB,CAAC,0CAA0C,CAAC;MAC5D,MAAMgC,QAAQ,GAAG,MAAM9D,UAAU,CAAC2F,WAAW,CAAC;MAE9C,IAAI7B,QAAQ,CAACC,OAAO,EAAE;QAAA,IAAAoC,cAAA;QACpBrE,gBAAgB,CAAC,wDAAwD,CAAC;QAE1EP,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEsC,QAAQ,CAAC;QAC7CvC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEsC,QAAQ,CAACsC,QAAQ,CAAC;;QAE9C;QACA5G,OAAO,CAACuE,OAAO,CAAC;UACdsC,OAAO,EAAG,8CAA6C5D,IAAI,CAAC0C,WAAY,sCAAqC;UAC7G9B,QAAQ,EAAE,CAAC;UACXM,KAAK,EAAE;YACL2C,SAAS,EAAE;UACb;QACF,CAAC,CAAC;;QAEF;QACA,MAAMC,cAAc,GAAGzC,QAAQ,CAACsC,QAAQ,MAAAD,cAAA,GAAIrC,QAAQ,CAACE,IAAI,cAAAmC,cAAA,uBAAbA,cAAA,CAAeC,QAAQ,KAAI,YAAY;QACnF7E,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE+E,cAAc,CAAC;QAEhFC,wBAAwB,CAACD,cAAc,CAAC;MAE1C,CAAC,MAAM;QACL,MAAM,IAAIE,KAAK,CAAC3C,QAAQ,CAACtE,OAAO,IAAI,gBAAgB,CAAC;MACvD;IACF,CAAC,CAAC,OAAO8E,KAAK,EAAE;MACd/C,OAAO,CAAC+C,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzChD,sBAAsB,CAAC,KAAK,CAAC;MAC7B9B,OAAO,CAAC8E,KAAK,CAAC,kBAAkB,GAAGA,KAAK,CAAC9E,OAAO,CAAC;MACjD4B,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3B;EACF,CAAC;;EAED,MAAMoF,wBAAwB,GAAG,MAAOE,OAAO,IAAK;IAClDnF,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEkF,OAAO,CAAC;IACzE,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,sBAAsB;IAE1B,IAAI;MACF9E,gBAAgB,CAAC,0EAA0E,CAAC;;MAE5F;MACA,IAAI+E,QAAQ,GAAG,CAAC;MAChB,MAAMC,WAAW,GAAG,GAAG,CAAC,CAAC;;MAEzB,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;QACpCF,QAAQ,EAAE;QACVtF,OAAO,CAACC,GAAG,CAAE,mCAAkCqF,QAAS,IAAGC,WAAY,aAAY,EAAEJ,OAAO,CAAC;QAE7F,IAAI;UACF,MAAMM,cAAc,GAAG,MAAM/G,kBAAkB,CAAC;YAAEyG;UAAQ,CAAC,CAAC;UAC5DnF,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEwF,cAAc,CAAC;UAC1DzF,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9CD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,CAAAwF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEnF,aAAa,MAAK,MAAM,IAAI,CAAAmF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE1D,MAAM,MAAK,QAAQ,CAAC;UACjH/B,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,CAAAwF,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE1D,MAAM,MAAK,WAAW,IAAI,CAAA0D,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEjD,OAAO,MAAK,IAAI,CAAC;UAE5G,IAAIiD,cAAc,KACfA,cAAc,CAACnF,aAAa,KAAK,MAAM,IAAImF,cAAc,CAAC1D,MAAM,KAAK,QAAQ,IAC7E0D,cAAc,CAAC1D,MAAM,KAAK,WAAW,IAAI0D,cAAc,CAACjD,OAAO,KAAK,IAAK,CAC3E,EAAE;YACD;YACA4C,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1BnD,QAAQ,CAACwD,mBAAmB,CAAC,kBAAkB,EAAEL,sBAAsB,CAAC,CAAC,CAAC;YAC5E;;YAEA9E,gBAAgB,CAAC,uDAAuD,CAAC;YACzEP,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;;YAEtE;YACAD,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;YACzEF,sBAAsB,CAAC,KAAK,CAAC;YAC7BI,mBAAmB,CAAC,IAAI,CAAC;YACzBN,iBAAiB,CAAC,IAAI,CAAC;YACvBG,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;;YAEhD;YACAgC,wBAAwB,CAAC,CAAC;;YAE1B;YACAhE,OAAO,CAACuE,OAAO,CAAC;cACdsC,OAAO,EAAE,sDAAsD;cAC/DhD,QAAQ,EAAE,CAAC;cACXM,KAAK,EAAE;gBACL2C,SAAS,EAAE,MAAM;gBACjBY,QAAQ,EAAE;cACZ;YACF,CAAC,CAAC;;YAEF;YACA1E,wBAAwB,CAAC,CAAC,CAAC;YAC3B,MAAM2E,iBAAiB,GAAGC,WAAW,CAAC,MAAM;cAC1C5E,wBAAwB,CAAC6E,IAAI,IAAI;gBAC/B,IAAIA,IAAI,IAAI,CAAC,EAAE;kBACbC,aAAa,CAACH,iBAAiB,CAAC;kBAChC5F,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;kBACpEE,mBAAmB,CAAC,KAAK,CAAC;kBAC1B6F,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,WAAW;kBAClC,OAAO,IAAI;gBACb;gBACA,OAAOJ,IAAI,GAAG,CAAC;cACjB,CAAC,CAAC;YACJ,CAAC,EAAE,IAAI,CAAC;UAEV,CAAC,MAAM,IAAIR,QAAQ,IAAIC,WAAW,EAAE;YAClC;YACAH,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1BnD,QAAQ,CAACwD,mBAAmB,CAAC,kBAAkB,EAAEL,sBAAsB,CAAC,CAAC,CAAC;YAC5E;;YAEA9E,gBAAgB,CAAC,8EAA8E,CAAC;YAEhG2D,UAAU,CAAC,MAAM;cACfnE,sBAAsB,CAAC,KAAK,CAAC;cAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;cACzB5B,OAAO,CAAC+E,OAAO,CAAC,0GAA0G,CAAC;YAC7H,CAAC,EAAE,IAAI,CAAC;UAEV,CAAC,MAAM;YACL;YACAzC,gBAAgB,CAAC,0EAA0E,CAAC;YAC5F2D,UAAU,CAACsB,iBAAiB,EAAE,IAAI,CAAC,CAAC,CAAC;UACvC;QAEF,CAAC,CAAC,OAAOzC,KAAK,EAAE;UACd/C,OAAO,CAAC+C,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;UAEnD;UACA,IAAIA,KAAK,CAAC9E,OAAO,IAAI8E,KAAK,CAAC9E,OAAO,CAACkI,QAAQ,CAAC,KAAK,CAAC,EAAE;YAClDnG,OAAO,CAAC+C,KAAK,CAAC,2CAA2C,CAAC;YAC1DqC,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1BnD,QAAQ,CAACwD,mBAAmB,CAAC,kBAAkB,EAAEL,sBAAsB,CAAC;YAC1E;YACAtF,sBAAsB,CAAC,KAAK,CAAC;YAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;YACzB5B,OAAO,CAAC8E,KAAK,CAAC,6HAA6H,CAAC;YAC5I;UACF;UAEA,IAAIA,KAAK,CAAC9E,OAAO,IAAI8E,KAAK,CAAC9E,OAAO,CAACkI,QAAQ,CAAC,KAAK,CAAC,EAAE;YAClDnG,OAAO,CAAC+C,KAAK,CAAC,oDAAoD,CAAC;YACnEqC,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1BnD,QAAQ,CAACwD,mBAAmB,CAAC,kBAAkB,EAAEL,sBAAsB,CAAC;YAC1E;YACAtF,sBAAsB,CAAC,KAAK,CAAC;YAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;YACzB5B,OAAO,CAAC8E,KAAK,CAAC,6CAA6C,CAAC;YAC5D;UACF;UAEA,IAAIuC,QAAQ,IAAIC,WAAW,EAAE;YAC3BH,SAAS,GAAG,KAAK,CAAC,CAAC;YACnB,IAAIC,sBAAsB,EAAE;cAC1BnD,QAAQ,CAACwD,mBAAmB,CAAC,kBAAkB,EAAEL,sBAAsB,CAAC,CAAC,CAAC;YAC5E;;YACAtF,sBAAsB,CAAC,KAAK,CAAC;YAC7BF,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;YACzB5B,OAAO,CAAC8E,KAAK,CAAC,mFAAmF,CAAC;UACpG,CAAC,MAAM;YACL;YACAmB,UAAU,CAACsB,iBAAiB,EAAE,IAAI,CAAC;UACrC;QACF;MACF,CAAC;;MAED;MACAH,sBAAsB,GAAGA,CAAA,KAAM;QAC7B,IAAI,CAACnD,QAAQ,CAACkE,MAAM,IAAIhB,SAAS,EAAE;UACjCpF,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;UAC3EM,gBAAgB,CAAC,+BAA+B,CAAC;UACjD;UACA2D,UAAU,CAAC,MAAMsB,iBAAiB,CAAC,CAAC,EAAE,GAAG,CAAC;QAC5C;MACF,CAAC;MAEDtD,QAAQ,CAACmE,gBAAgB,CAAC,kBAAkB,EAAEhB,sBAAsB,CAAC;;MAErE;MACAnB,UAAU,CAACsB,iBAAiB,EAAE,GAAG,CAAC,CAAC,CAAC;IAEtC,CAAC,CAAC,OAAOzC,KAAK,EAAE;MACdqC,SAAS,GAAG,KAAK,CAAC,CAAC;MACnB,IAAIC,sBAAsB,EAAE;QAC1BnD,QAAQ,CAACwD,mBAAmB,CAAC,kBAAkB,EAAEL,sBAAsB,CAAC,CAAC,CAAC;MAC5E;;MACAtF,sBAAsB,CAAC,KAAK,CAAC;MAC7B9B,OAAO,CAAC8E,KAAK,CAAC,+BAA+B,GAAGA,KAAK,CAAC9E,OAAO,CAAC;MAC9D4B,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3B;EACF,CAAC;;EAED,MAAMyG,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAIlF,gBAAgB,IAAIA,gBAAgB,CAACd,aAAa,KAAK,MAAM,IAAIc,gBAAgB,CAACW,MAAM,KAAK,QAAQ,EAAE;MACzG,MAAMkB,OAAO,GAAG,IAAIC,IAAI,CAAC9B,gBAAgB,CAAC6B,OAAO,CAAC;MAClD,MAAMc,GAAG,GAAG,IAAIb,IAAI,CAAC,CAAC;MACtB,IAAID,OAAO,GAAGc,GAAG,EAAE;QACjB,OAAO,QAAQ;MACjB;IACF;IAEA,IAAI,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqF,kBAAkB,MAAK,SAAS,IAAKnF,gBAAgB,IAAIA,gBAAgB,CAACW,MAAM,KAAK,SAAU,EAAE;MACzG,OAAO,SAAS;IAClB;IAEA,OAAO,MAAM;EACf,CAAC;EAED,MAAMyE,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;IAC7B,OAAO,IAAIvD,IAAI,CAACuD,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,EAAC1F,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAE6B,OAAO,GAAE,OAAO,CAAC;IACxC,MAAMA,OAAO,GAAG,IAAIC,IAAI,CAAC9B,gBAAgB,CAAC6B,OAAO,CAAC;IAClD,MAAMc,GAAG,GAAG,IAAIb,IAAI,CAAC,CAAC;IACtB,MAAM6D,QAAQ,GAAG9D,OAAO,GAAGc,GAAG;IAC9B,MAAMiD,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACH,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOE,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEH,QAAQ,CAAC;EAC9B,CAAC;EAED,MAAMT,kBAAkB,GAAGD,qBAAqB,CAAC,CAAC;EAElD,oBACEtH,OAAA;IAAKoI,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAChCrI,OAAA;MAAKoI,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErCrI,OAAA,CAAChB,MAAM,CAACsJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;QAAG,CAAE;QAChCC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAE7F,QAAQ,EAAE;QAAI,CAAE;QAC9BsF,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAG/BrI,OAAA;UACE4I,OAAO,EAAEA,CAAA,KAAM;YACb5H,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;YAC1CI,eAAe,CAACb,KAAK,CAAC,CAAC,CAAC,IAAI;cAAEiC,KAAK,EAAE,WAAW;cAAEK,QAAQ,EAAE,CAAC;cAAEF,eAAe,EAAE;YAAM,CAAC,CAAC;YACxFzB,mBAAmB,CAAC,IAAI,CAAC;UAC3B,CAAE;UACFiC,KAAK,EAAE;YACLyF,QAAQ,EAAE,OAAO;YACjBC,GAAG,EAAE,MAAM;YACXC,KAAK,EAAE,MAAM;YACbC,UAAU,EAAE,SAAS;YACrBC,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE,UAAU;YACnBC,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,SAAS;YACjBC,MAAM,EAAE;UACV,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1J,OAAA;UAAIoI,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACxBrI,OAAA,CAACd,OAAO;YAACkJ,SAAS,EAAC;UAAY;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,2BAEpC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL1J,OAAA;UAAGoI,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoD;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CAAC,eAGb1J,OAAA,CAAChB,MAAM,CAACsJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAE7F,QAAQ,EAAE,GAAG;UAAE6G,KAAK,EAAE;QAAI,CAAE;QAC1CvB,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBAEhCrI,OAAA;UAAIoI,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoB;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEtDnC,kBAAkB,KAAK,QAAQ,iBAC9BvH,OAAA;UAAKoI,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCrI,OAAA;YAAKoI,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCrI,OAAA,CAACZ,aAAa;cAACgJ,SAAS,EAAC;YAAoB;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChD1J,OAAA;cAAMoI,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAmB;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACN1J,OAAA;YAAKoI,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCrI,OAAA;cAAKoI,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BrI,OAAA,CAACd,OAAO;gBAACkJ,SAAS,EAAC;cAAa;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACnC1J,OAAA;gBAAAqI,QAAA,GAAM,QAAM,EAAC,CAAAjG,gBAAgB,aAAhBA,gBAAgB,wBAAA/B,qBAAA,GAAhB+B,gBAAgB,CAAEwH,UAAU,cAAAvJ,qBAAA,uBAA5BA,qBAAA,CAA8BoC,KAAK,KAAI,cAAc;cAAA;gBAAA8G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvE,CAAC,eACN1J,OAAA;cAAKoI,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BrI,OAAA,CAACb,aAAa;gBAACiJ,SAAS,EAAC;cAAa;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzC1J,OAAA;gBAAAqI,QAAA,GAAM,WAAS,EAACb,UAAU,CAACpF,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE6B,OAAO,CAAC;cAAA;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACN1J,OAAA;cAAKoI,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BrI,OAAA,CAACZ,aAAa;gBAACgJ,SAAS,EAAC;cAAa;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzC1J,OAAA;gBAAAqI,QAAA,GAAM,kBAAgB,EAACP,gBAAgB,CAAC,CAAC;cAAA;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAnC,kBAAkB,KAAK,SAAS,iBAC/BvH,OAAA;UAAKoI,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCrI,OAAA;YAAKoI,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCrI,OAAA,CAACX,aAAa;cAAC+I,SAAS,EAAC;YAAqB;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjD1J,OAAA;cAAMoI,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAoB;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACN1J,OAAA;YAAKoI,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnCrI,OAAA;cAAKoI,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BrI,OAAA,CAACb,aAAa;gBAACiJ,SAAS,EAAC;cAAa;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzC1J,OAAA;gBAAAqI,QAAA,GAAM,WAAS,EAACb,UAAU,CAACpF,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE6B,OAAO,CAAC;cAAA;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACN1J,OAAA;cAAGoI,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAnC,kBAAkB,KAAK,MAAM,iBAC5BvH,OAAA;UAAKoI,SAAS,EAAC,wBAAwB;UAAAC,QAAA,gBACrCrI,OAAA;YAAKoI,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCrI,OAAA,CAACT,MAAM;cAAC6I,SAAS,EAAC;YAAkB;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACvC1J,OAAA;cAAMoI,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAY;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eACN1J,OAAA;YAAKoI,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnCrI,OAAA;cAAGoI,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAE/B;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAGb1J,OAAA,CAAChB,MAAM,CAACsJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAE7F,QAAQ,EAAE,GAAG;UAAE6G,KAAK,EAAE;QAAI,CAAE;QAC1CvB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE3BrI,OAAA;UAAIoI,SAAS,EAAC,eAAe;UAAAC,QAAA,EAC1Bd,kBAAkB,KAAK,QAAQ,GAC5B,sBAAsB,GACtBA,kBAAkB,KAAK,SAAS,GAC9B,4BAA4B,GAC5B;QAAqB;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEzB,CAAC,eAGL1J,OAAA;UAAKoD,KAAK,EAAE;YAAEyG,OAAO,EAAE,MAAM;YAAEC,GAAG,EAAE,MAAM;YAAEC,cAAc,EAAE,QAAQ;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAA3B,QAAA,gBAC3FrI,OAAA;YACE4I,OAAO,EAAElE,mBAAoB;YAC7BtB,KAAK,EAAE;cACL4F,UAAU,EAAE,SAAS;cACrBC,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE,UAAU;cACnBC,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAE,SAAS;cACjB1C,QAAQ,EAAE;YACZ,CAAE;YAAA0B,QAAA,EACH;UAED;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT1J,OAAA;YACE4I,OAAO,EAAEnE,gBAAiB;YAC1BrB,KAAK,EAAE;cACL4F,UAAU,EAAE,SAAS;cACrBC,KAAK,EAAE,OAAO;cACdC,MAAM,EAAE,MAAM;cACdC,OAAO,EAAE,UAAU;cACnBC,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAE,SAAS;cACjB1C,QAAQ,EAAE;YACZ,CAAE;YAAA0B,QAAA,EACH;UAED;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN1J,OAAA;UAAGoI,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAC5Bd,kBAAkB,KAAK,QAAQ,GAC5B,+DAA+D,GAC/DA,kBAAkB,KAAK,SAAS,GAC9B,iFAAiF,GACjF;QAA2F;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEhG,CAAC,EAEHhJ,OAAO,gBACNV,OAAA;UAAKoI,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BrI,OAAA;YAAKoI,SAAS,EAAC;UAAS;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/B1J,OAAA;YAAAqI,QAAA,EAAG;UAAgB;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,GACJlJ,KAAK,CAACkD,MAAM,KAAK,CAAC,gBACpB1D,OAAA;UAAKoI,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BrI,OAAA;YAAKoI,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAE;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvC1J,OAAA;YAAAqI,QAAA,EAAI;UAAkB;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3B1J,OAAA;YAAAqI,QAAA,EAAG;UAA6E;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACpF1J,OAAA;YAAQoI,SAAS,EAAC,aAAa;YAACQ,OAAO,EAAE5F,UAAW;YAAAqF,QAAA,EAAC;UAErD;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,gBAEN1J,OAAA;UAAKoI,SAAS,EAAC,YAAY;UAAAC,QAAA,EACxB7H,KAAK,CAACyJ,GAAG,CAAEtF,IAAI;YAAA,IAAAuF,WAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,cAAA;YAAA,oBACdrK,OAAA,CAAChB,MAAM,CAACsJ,GAAG;cAETgC,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAC1BnC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAErBrI,OAAA;gBAAKoI,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BrI,OAAA;kBAAIoI,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAE1D,IAAI,CAAClC;gBAAK;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,EAC3C,EAAAQ,WAAA,GAAAvF,IAAI,CAAClC,KAAK,cAAAyH,WAAA,uBAAVA,WAAA,CAAYvE,WAAW,CAAC,CAAC,CAACwB,QAAQ,CAAC,UAAU,CAAC,kBAC7CnH,OAAA;kBAAMoI,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAU;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAC9C;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEN1J,OAAA;gBAAKoI,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BrI,OAAA;kBAAKoI,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BrI,OAAA;oBAAKoI,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5BrI,OAAA;sBAAMoI,SAAS,EAAC,UAAU;sBAAAC,QAAA,EAAC;oBAAG;sBAAAkB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,GAAAS,qBAAA,GACpCxF,IAAI,CAAC/B,eAAe,cAAAuH,qBAAA,uBAApBA,qBAAA,CAAsBM,cAAc,CAAC,CAAC;kBAAA;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,EACL/E,IAAI,CAAChC,WAAW,GAAGgC,IAAI,CAAC/B,eAAe,iBACtC5C,OAAA,CAAAE,SAAA;oBAAAmI,QAAA,gBACErI,OAAA;sBAAMoI,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,IAAA+B,iBAAA,GAAEzF,IAAI,CAAChC,WAAW,cAAAyH,iBAAA,uBAAhBA,iBAAA,CAAkBK,cAAc,CAAC,CAAC,EAAC,MAAI;oBAAA;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChF1J,OAAA;sBAAMoI,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,GAC7BJ,IAAI,CAACyC,KAAK,CAAE,CAAC/F,IAAI,CAAChC,WAAW,GAAGgC,IAAI,CAAC/B,eAAe,IAAI+B,IAAI,CAAChC,WAAW,GAAI,GAAG,CAAC,EAAC,OACpF;oBAAA;sBAAA4G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,eACP,CACH;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eACN1J,OAAA;kBAAKoI,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BrI,OAAA;oBAAMoI,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAE1D,IAAI,CAAC7B;kBAAQ;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,UAAM,EAAC/E,IAAI,CAAC7B,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,SACjG;gBAAA;kBAAAyG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN1J,OAAA;gBAAKoI,SAAS,EAAC,eAAe;gBAAAC,QAAA,GAAAgC,cAAA,GAC3B1F,IAAI,CAACjC,QAAQ,cAAA2H,cAAA,uBAAbA,cAAA,CAAeM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACV,GAAG,CAAC,CAACW,OAAO,EAAEC,KAAK,kBAC7C7K,OAAA;kBAAiBoI,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACvCrI,OAAA,CAACZ,aAAa;oBAACgJ,SAAS,EAAC;kBAAc;oBAAAmB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1C1J,OAAA;oBAAAqI,QAAA,EAAOuC;kBAAO;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA,GAFdmB,KAAK;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAEN1J,OAAA;gBACEoI,SAAS,EAAC,iBAAiB;gBAC3BQ,OAAO,EAAEA,CAAA,KAAMtE,gBAAgB,CAACK,IAAI,CAAE;gBACtCmG,QAAQ,EAAElK,cAAc,KAAK+D,IAAI,CAACnC,GAAI;gBACtCY,KAAK,EAAE;kBACL4F,UAAU,EAAE,2CAA2C;kBACvDC,KAAK,EAAE,OAAO;kBACdC,MAAM,EAAE,MAAM;kBACdE,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,aAAa;kBACtBxC,QAAQ,EAAE,MAAM;kBAChBoE,UAAU,EAAE,KAAK;kBACjB1B,MAAM,EAAEzI,cAAc,KAAK+D,IAAI,CAACnC,GAAG,GAAG,aAAa,GAAG,SAAS;kBAC/DmG,UAAU,EAAE,eAAe;kBAC3BkB,OAAO,EAAE,MAAM;kBACfmB,UAAU,EAAE,QAAQ;kBACpBjB,cAAc,EAAE,QAAQ;kBACxBD,GAAG,EAAE,QAAQ;kBACbmB,KAAK,EAAE,MAAM;kBACbzC,OAAO,EAAE5H,cAAc,KAAK+D,IAAI,CAACnC,GAAG,GAAG,GAAG,GAAG;gBAC/C,CAAE;gBACF0I,YAAY,EAAGC,CAAC,IAAK;kBACnB,IAAIvK,cAAc,KAAK+D,IAAI,CAACnC,GAAG,EAAE;oBAC/B2I,CAAC,CAACC,MAAM,CAAChI,KAAK,CAAC4F,UAAU,GAAG,2CAA2C;oBACvEmC,CAAC,CAACC,MAAM,CAAChI,KAAK,CAACiI,SAAS,GAAG,kBAAkB;oBAC7CF,CAAC,CAACC,MAAM,CAAChI,KAAK,CAACkI,SAAS,GAAG,oCAAoC;kBACjE;gBACF,CAAE;gBACFC,YAAY,EAAGJ,CAAC,IAAK;kBACnB,IAAIvK,cAAc,KAAK+D,IAAI,CAACnC,GAAG,EAAE;oBAC/B2I,CAAC,CAACC,MAAM,CAAChI,KAAK,CAAC4F,UAAU,GAAG,2CAA2C;oBACvEmC,CAAC,CAACC,MAAM,CAAChI,KAAK,CAACiI,SAAS,GAAG,eAAe;oBAC1CF,CAAC,CAACC,MAAM,CAAChI,KAAK,CAACkI,SAAS,GAAG,oCAAoC;kBACjE;gBACF,CAAE;gBAAAjD,QAAA,gBAEFrI,OAAA,CAACV,YAAY;kBAAC8I,SAAS,EAAC;gBAAU;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACpC9I,cAAc,KAAK+D,IAAI,CAACnC,GAAG,GACxB,eAAe,GACf+E,kBAAkB,KAAK,QAAQ,GAC7B,kBAAkB,GAClBA,kBAAkB,KAAK,SAAS,GAC9B,gBAAgB,GAChB,cAAc;cAAA;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CAAC;YAAA,GAtFJ/E,IAAI,CAACnC,GAAG;cAAA+G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuFH,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,EAGZ,CAAC,CAACxH,IAAI,CAAC0C,WAAW,IAAI,CAAC,gBAAgB,CAACC,IAAI,CAAC3C,IAAI,CAAC0C,WAAW,CAAC,kBAC7D5E,OAAA,CAAChB,MAAM,CAACsJ,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,OAAO,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAE7F,QAAQ,EAAE,GAAG;UAAE6G,KAAK,EAAE;QAAI,CAAE;QAC1CvB,SAAS,EAAC,eAAe;QAAAC,QAAA,eAEzBrI,OAAA;UAAKoI,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BrI,OAAA,CAACX,aAAa;YAAC+I,SAAS,EAAC;UAAc;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1C1J,OAAA;YAAAqI,QAAA,gBACErI,OAAA;cAAAqI,QAAA,EAAI;YAAqB;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9B1J,OAAA;cAAAqI,QAAA,EAAG;YAAuE;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC9E1J,OAAA;cACEoI,SAAS,EAAC,kBAAkB;cAC5BQ,OAAO,EAAEA,CAAA,KAAM5B,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,UAAW;cAAAmB,QAAA,EAClD;YAED;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACb,EAGA5I,mBAAmB,iBAClBd,OAAA;QACEoI,SAAS,EAAC,oBAAoB;QAC9BhF,KAAK,EAAE;UACLyF,QAAQ,EAAE,OAAO;UACjBC,GAAG,EAAE,GAAG;UACR0C,IAAI,EAAE,GAAG;UACTP,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,OAAO;UACfzC,UAAU,EAAE,oBAAoB;UAAE;UAClC0C,cAAc,EAAE,WAAW;UAC3BC,oBAAoB,EAAE,WAAW;UACjC9B,OAAO,EAAE,MAAM;UACfmB,UAAU,EAAE,QAAQ;UACpBjB,cAAc,EAAE,QAAQ;UACxBT,MAAM,EAAE,OAAO;UACfH,OAAO,EAAE,MAAM;UACfyC,SAAS,EAAE,YAAY;UACvBC,SAAS,EAAE,MAAM,CAAC;QACpB,CAAE;;QACFjD,OAAO,EAAGuC,CAAC,IAAKA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACW,aAAa,IAAIvH,0BAA0B,CAAC,CAAE;QAAA8D,QAAA,eAE7ErI,OAAA;UACEoI,SAAS,EAAC,sBAAsB;UAChChF,KAAK,EAAE;YACL4F,UAAU,EAAE,mDAAmD;YAC/DI,YAAY,EAAE,MAAM;YACpBkC,SAAS,EAAE,oEAAoE;YAC/EpC,MAAM,EAAE,mCAAmC;YAC3C+B,KAAK,EAAE,MAAM;YACbc,QAAQ,EAAE,OAAO;YAAE;YACnBC,SAAS,EAAE,MAAM;YAAE;YACnBnC,OAAO,EAAE,MAAM;YACfoC,aAAa,EAAE,QAAQ;YACvB5I,QAAQ,EAAE,QAAQ;YAClBwF,QAAQ,EAAE,UAAU;YACpBwC,SAAS,EAAE,eAAe;YAC1Ba,MAAM,EAAE,MAAM,CAAC;UACjB,CAAE;;UACFtD,OAAO,EAAGuC,CAAC,IAAKA,CAAC,CAACgB,eAAe,CAAC,CAAE;UAAA9D,QAAA,gBAGpCrI,OAAA;YAAKoD,KAAK,EAAE;cACV4F,UAAU,EAAE,mDAAmD;cAC/DG,OAAO,EAAE,QAAQ;cACjBF,KAAK,EAAE,OAAO;cACdmD,SAAS,EAAE,QAAQ;cACnBvD,QAAQ,EAAE;YACZ,CAAE;YAAAR,QAAA,gBACArI,OAAA;cACEoI,SAAS,EAAC,qBAAqB;cAC/BQ,OAAO,EAAErE,0BAA2B;cACpC9B,KAAK,EAAC,sBAAsB;cAC5BW,KAAK,EAAE;gBACLyF,QAAQ,EAAE,UAAU;gBACpBC,GAAG,EAAE,MAAM;gBACXC,KAAK,EAAE,MAAM;gBACbC,UAAU,EAAE,0BAA0B;gBACtCE,MAAM,EAAE,MAAM;gBACdE,YAAY,EAAE,KAAK;gBACnB6B,KAAK,EAAE,MAAM;gBACbQ,MAAM,EAAE,MAAM;gBACdxC,KAAK,EAAE,OAAO;gBACdtC,QAAQ,EAAE,MAAM;gBAChB0C,MAAM,EAAE,SAAS;gBACjBQ,OAAO,EAAE,MAAM;gBACfmB,UAAU,EAAE,QAAQ;gBACpBjB,cAAc,EAAE,QAAQ;gBACxBpB,UAAU,EAAE,eAAe;gBAC3BW,MAAM,EAAE;cACV,CAAE;cACF4B,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAChI,KAAK,CAAC4F,UAAU,GAAG,0BAA2B;cAC5EuC,YAAY,EAAGJ,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAChI,KAAK,CAAC4F,UAAU,GAAG,0BAA2B;cAAAX,QAAA,EAC7E;YAED;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET1J,OAAA;cAAKoD,KAAK,EAAE;gBAAE4G,YAAY,EAAE;cAAS,CAAE;cAAA3B,QAAA,eACrCrI,OAAA;gBAAKoI,SAAS,EAAC,8BAA8B;gBAAChF,KAAK,EAAE;kBACnD6H,KAAK,EAAE,MAAM;kBACbQ,MAAM,EAAE,MAAM;kBACdS,MAAM,EAAE,aAAa;kBACrBrD,QAAQ,EAAE;gBACZ,CAAE;gBAAAR,QAAA,gBACArI,OAAA;kBAAKoI,SAAS,EAAC,iBAAiB;kBAAChF,KAAK,EAAE;oBACtC6H,KAAK,EAAE,MAAM;oBACbQ,MAAM,EAAE,MAAM;oBACdvC,MAAM,EAAE,oCAAoC;oBAC5CmD,SAAS,EAAE,iBAAiB;oBAC5BjD,YAAY,EAAE,KAAK;oBACnBkD,SAAS,EAAE;kBACb;gBAAE;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACT1J,OAAA;kBAAKoD,KAAK,EAAE;oBACVyF,QAAQ,EAAE,UAAU;oBACpBC,GAAG,EAAE,KAAK;oBACV0C,IAAI,EAAE,KAAK;oBACXH,SAAS,EAAE,uBAAuB;oBAClC1E,QAAQ,EAAE;kBACZ,CAAE;kBAAA0B,QAAA,EAAC;gBAAE;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN1J,OAAA;cAAIoD,KAAK,EAAE;gBACT8I,MAAM,EAAE,GAAG;gBACXvF,QAAQ,EAAE,QAAQ;gBAClBoE,UAAU,EAAE,KAAK;gBACjBwB,UAAU,EAAE;cACd,CAAE;cAAAlE,QAAA,EAAC;YAEH;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1J,OAAA;cAAGoD,KAAK,EAAE;gBACR8I,MAAM,EAAE,cAAc;gBACtBvF,QAAQ,EAAE,QAAQ;gBAClB6B,OAAO,EAAE;cACX,CAAE;cAAAH,QAAA,EAAC;YAEH;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGN1J,OAAA;YAAKoD,KAAK,EAAE;cACVoJ,IAAI,EAAE,GAAG;cACTX,SAAS,EAAE,MAAM;cACjBY,SAAS,EAAE,QAAQ;cACnBtD,OAAO,EAAE;YACX,CAAE;YAAAd,QAAA,eACArI,OAAA;cAAKoD,KAAK,EAAE;gBACV+F,OAAO,EAAE,MAAM;gBACfU,OAAO,EAAE,MAAM;gBACfoC,aAAa,EAAE,QAAQ;gBACvBnC,GAAG,EAAE,MAAM;gBACX4C,SAAS,EAAE;cACb,CAAE;cAAArE,QAAA,gBAEFrI,OAAA;gBAAKoD,KAAK,EAAE;kBACV4F,UAAU,EAAE,0BAA0B;kBACtCE,MAAM,EAAE,mCAAmC;kBAC3CE,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,SAAS;kBAClBiD,SAAS,EAAE;gBACb,CAAE;gBAAA/D,QAAA,eACArI,OAAA;kBAAGoD,KAAK,EAAE;oBACR8I,MAAM,EAAE,GAAG;oBACXjD,KAAK,EAAE,SAAS;oBAChBtC,QAAQ,EAAE,SAAS;oBACnBoE,UAAU,EAAE;kBACd,CAAE;kBAAA1C,QAAA,EACC/G;gBAAa;kBAAAiI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGN1J,OAAA;gBAAKoD,KAAK,EAAE;kBACV4F,UAAU,EAAE,0BAA0B;kBACtCE,MAAM,EAAE,mCAAmC;kBAC3CE,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE;gBACX,CAAE;gBAAAd,QAAA,gBACArI,OAAA;kBAAIoD,KAAK,EAAE;oBACT8I,MAAM,EAAE,cAAc;oBACtBjD,KAAK,EAAE,SAAS;oBAChBtC,QAAQ,EAAE,QAAQ;oBAClBoE,UAAU,EAAE;kBACd,CAAE;kBAAA1C,QAAA,EACCjH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqB;gBAAK;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,eACL1J,OAAA;kBAAKoD,KAAK,EAAE;oBACVyG,OAAO,EAAE,MAAM;oBACfE,cAAc,EAAE,eAAe;oBAC/BiB,UAAU,EAAE,QAAQ;oBACpBrE,QAAQ,EAAE,QAAQ;oBAClBsC,KAAK,EAAE;kBACT,CAAE;kBAAAZ,QAAA,gBACArI,OAAA;oBAAAqI,QAAA,GAAM,UAAQ,eAAArI,OAAA;sBAAAqI,QAAA,GAASjH,YAAY,aAAZA,YAAY,wBAAAd,qBAAA,GAAZc,YAAY,CAAEwB,eAAe,cAAAtC,qBAAA,uBAA7BA,qBAAA,CAA+BmK,cAAc,CAAC,CAAC,EAAC,MAAI;oBAAA;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3F1J,OAAA;oBAAAqI,QAAA,GAAM,YAAU,eAAArI,OAAA;sBAAAqI,QAAA,GAASjH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0B,QAAQ,EAAC,QAAM,EAAC,CAAA1B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0B,QAAQ,IAAG,CAAC,GAAG,GAAG,GAAG,EAAE;oBAAA;sBAAAyG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1G,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN1J,OAAA;gBAAKoD,KAAK,EAAE;kBACV4F,UAAU,EAAE,0BAA0B;kBACtCE,MAAM,EAAE,mCAAmC;kBAC3CE,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE;gBACX,CAAE;gBAAAd,QAAA,gBACArI,OAAA;kBAAIoD,KAAK,EAAE;oBACT8I,MAAM,EAAE,eAAe;oBACvBjD,KAAK,EAAE,SAAS;oBAChBtC,QAAQ,EAAE,MAAM;oBAChBoE,UAAU,EAAE,KAAK;oBACjBlB,OAAO,EAAE,MAAM;oBACfmB,UAAU,EAAE,QAAQ;oBACpBlB,GAAG,EAAE;kBACP,CAAE;kBAAAzB,QAAA,EAAC;gBAEH;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1J,OAAA;kBAAGoD,KAAK,EAAE;oBACR8I,MAAM,EAAE,eAAe;oBACvBjD,KAAK,EAAE,SAAS;oBAChBtC,QAAQ,EAAE,QAAQ;oBAClBoE,UAAU,EAAE,KAAK;oBACjB/B,UAAU,EAAE,yBAAyB;oBACrCG,OAAO,EAAE,QAAQ;oBACjBC,YAAY,EAAE,KAAK;oBACnBgD,SAAS,EAAE;kBACb,CAAE;kBAAA/D,QAAA,GAAC,UACO,EAACnG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0C,WAAW;gBAAA;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACJ1J,OAAA;kBAAKoD,KAAK,EAAE;oBACV6F,KAAK,EAAE,SAAS;oBAChBtC,QAAQ,EAAE,SAAS;oBACnBgG,UAAU,EAAE;kBACd,CAAE;kBAAAtE,QAAA,gBACArI,OAAA;oBAAKoD,KAAK,EAAE;sBAAE4G,YAAY,EAAE;oBAAU,CAAE;oBAAA3B,QAAA,EAAC;kBAAoD;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnG1J,OAAA;oBAAKoD,KAAK,EAAE;sBAAE4G,YAAY,EAAE;oBAAU,CAAE;oBAAA3B,QAAA,EAAC;kBAA0C;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzF1J,OAAA;oBAAAqI,QAAA,EAAK;kBAAwC;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGH5H,YAAY,iBACX9B,OAAA;gBAAKoD,KAAK,EAAE;kBACV4F,UAAU,EAAE,yBAAyB;kBACrCE,MAAM,EAAE,kCAAkC;kBAC1CE,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,MAAM;kBACfiD,SAAS,EAAE,QAAQ;kBACnBrG,SAAS,EAAE;gBACb,CAAE;gBAAAsC,QAAA,gBACFrI,OAAA;kBAAGoD,KAAK,EAAE;oBACR8I,MAAM,EAAE,cAAc;oBACtBjD,KAAK,EAAE,SAAS;oBAChBtC,QAAQ,EAAE,QAAQ;oBAClBoE,UAAU,EAAE;kBACd,CAAE;kBAAA1C,QAAA,EAAC;gBAEH;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ1J,OAAA;kBAAGoD,KAAK,EAAE;oBACR8I,MAAM,EAAE,YAAY;oBACpBjD,KAAK,EAAE,SAAS;oBAChBtC,QAAQ,EAAE;kBACZ,CAAE;kBAAA0B,QAAA,EAAC;gBAEH;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACJ1J,OAAA;kBACEoI,SAAS,EAAC,kBAAkB;kBAC5BQ,OAAO,EAAEpE,cAAe;kBACxBpB,KAAK,EAAE;oBACL4F,UAAU,EAAE,mDAAmD;oBAC/DC,KAAK,EAAE,OAAO;oBACdC,MAAM,EAAE,MAAM;oBACdC,OAAO,EAAE,gBAAgB;oBACzBC,YAAY,EAAE,MAAM;oBACpBzC,QAAQ,EAAE,QAAQ;oBAClBoE,UAAU,EAAE,KAAK;oBACjB1B,MAAM,EAAE,SAAS;oBACjBV,UAAU,EAAE,eAAe;oBAC3B2C,SAAS,EAAE,oCAAoC;oBAC/CL,KAAK,EAAE,MAAM;oBACbc,QAAQ,EAAE,OAAO;oBACjBlC,OAAO,EAAE,MAAM;oBACfmB,UAAU,EAAE,QAAQ;oBACpBjB,cAAc,EAAE,QAAQ;oBACxBD,GAAG,EAAE,QAAQ;oBACboC,MAAM,EAAE;kBACV,CAAE;kBACFhB,YAAY,EAAGC,CAAC,IAAK;oBACnBA,CAAC,CAACC,MAAM,CAAChI,KAAK,CAAC4F,UAAU,GAAG,mDAAmD;oBAC/EmC,CAAC,CAACC,MAAM,CAAChI,KAAK,CAACiI,SAAS,GAAG,kBAAkB;oBAC7CF,CAAC,CAACC,MAAM,CAAChI,KAAK,CAACkI,SAAS,GAAG,oCAAoC;kBACjE,CAAE;kBACFC,YAAY,EAAGJ,CAAC,IAAK;oBACnBA,CAAC,CAACC,MAAM,CAAChI,KAAK,CAAC4F,UAAU,GAAG,mDAAmD;oBAC/EmC,CAAC,CAACC,MAAM,CAAChI,KAAK,CAACiI,SAAS,GAAG,eAAe;oBAC1CF,CAAC,CAACC,MAAM,CAAChI,KAAK,CAACkI,SAAS,GAAG,oCAAoC;kBACjE,CAAE;kBAAAjD,QAAA,EACH;gBAED;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAxI,gBAAgB,iBACflB,OAAA;QACEoI,SAAS,EAAC,oBAAoB;QAC9BhF,KAAK,EAAE;UACLyF,QAAQ,EAAE,OAAO;UACjBC,GAAG,EAAE,GAAG;UACR0C,IAAI,EAAE,GAAG;UACTP,KAAK,EAAE,OAAO;UACdQ,MAAM,EAAE,OAAO;UACfzC,UAAU,EAAE,oBAAoB;UAChC0C,cAAc,EAAE,YAAY;UAC5BC,oBAAoB,EAAE,YAAY;UAClC9B,OAAO,EAAE,MAAM;UACfmB,UAAU,EAAE,QAAQ;UACpBjB,cAAc,EAAE,QAAQ;UACxBT,MAAM,EAAE,OAAO;UACfH,OAAO,EAAE,MAAM;UACfyC,SAAS,EAAE;QACb,CAAE;QACFhD,OAAO,EAAGuC,CAAC,IAAK;UACd,IAAIA,CAAC,CAACC,MAAM,KAAKD,CAAC,CAACW,aAAa,EAAE;YAChC7J,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;YAChCd,mBAAmB,CAAC,KAAK,CAAC;UAC5B;QACF,CAAE;QAAAkH,QAAA,eAEFrI,OAAA;UACEoI,SAAS,EAAC,yCAAyC;UACnDhF,KAAK,EAAE;YACL4F,UAAU,EAAE,mDAAmD;YAC/DI,YAAY,EAAE,MAAM;YACpBkC,SAAS,EAAE,kEAAkE;YAC7EpC,MAAM,EAAE,kCAAkC;YAC1C+B,KAAK,EAAE,MAAM;YACbc,QAAQ,EAAE,OAAO;YACjBC,SAAS,EAAE,MAAM;YACjBnC,OAAO,EAAE,MAAM;YACfoC,aAAa,EAAE,QAAQ;YACvB5I,QAAQ,EAAE,QAAQ;YAClBwF,QAAQ,EAAE,UAAU;YACpBwC,SAAS,EAAE;UACb,CAAE;UACFzC,OAAO,EAAGuC,CAAC,IAAKA,CAAC,CAACgB,eAAe,CAAC,CAAE;UAAA9D,QAAA,gBAGpCrI,OAAA;YAAKoD,KAAK,EAAE;cACV4F,UAAU,EAAE,mDAAmD;cAC/DG,OAAO,EAAE,MAAM;cACfF,KAAK,EAAE,OAAO;cACdmD,SAAS,EAAE,QAAQ;cACnBvD,QAAQ,EAAE,UAAU;cACpBO,YAAY,EAAE,eAAe;cAC7BwD,UAAU,EAAE;YACd,CAAE;YAAAvE,QAAA,gBACArI,OAAA;cACE4I,OAAO,EAAEA,CAAA,KAAM;gBACb3G,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;gBAChCd,mBAAmB,CAAC,KAAK,CAAC;cAC5B,CAAE;cACFiC,KAAK,EAAE;gBACLyF,QAAQ,EAAE,UAAU;gBACpBC,GAAG,EAAE,MAAM;gBACXC,KAAK,EAAE,MAAM;gBACbC,UAAU,EAAE,0BAA0B;gBACtCE,MAAM,EAAE,MAAM;gBACdE,YAAY,EAAE,KAAK;gBACnB6B,KAAK,EAAE,MAAM;gBACbQ,MAAM,EAAE,MAAM;gBACdxC,KAAK,EAAE,OAAO;gBACdtC,QAAQ,EAAE,MAAM;gBAChB0C,MAAM,EAAE,SAAS;gBACjBQ,OAAO,EAAE,MAAM;gBACfmB,UAAU,EAAE,QAAQ;gBACpBjB,cAAc,EAAE,QAAQ;gBACxBpB,UAAU,EAAE,eAAe;gBAC3BW,MAAM,EAAE;cACV,CAAE;cACF4B,YAAY,EAAGC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAChI,KAAK,CAAC4F,UAAU,GAAG,0BAA2B;cAC5EuC,YAAY,EAAGJ,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAChI,KAAK,CAAC4F,UAAU,GAAG,0BAA2B;cAAAX,QAAA,EAC7E;YAED;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAET1J,OAAA;cAAKoD,KAAK,EAAE;gBACVuD,QAAQ,EAAE,MAAM;gBAChBqD,YAAY,EAAE,MAAM;gBACpBsC,SAAS,EAAE;cACb,CAAE;cAAAjE,QAAA,EAAC;YAEH;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAEN1J,OAAA;cAAIoD,KAAK,EAAE;gBACT8I,MAAM,EAAE,GAAG;gBACXvF,QAAQ,EAAE,MAAM;gBAChBoE,UAAU,EAAE,KAAK;gBACjBwB,UAAU,EAAE,8BAA8B;gBAC1CvC,YAAY,EAAE;cAChB,CAAE;cAAA3B,QAAA,EAAC;YAEH;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEL1J,OAAA;cAAGoD,KAAK,EAAE;gBACR8I,MAAM,EAAE,GAAG;gBACXvF,QAAQ,EAAE,QAAQ;gBAClB6B,OAAO,EAAE,MAAM;gBACfuC,UAAU,EAAE;cACd,CAAE;cAAA1C,QAAA,GAAC,aACU,EAACjH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqB,KAAK,EAAC,gBAClC;YAAA;cAAA8G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGN1J,OAAA;YAAKoD,KAAK,EAAE;cACVoJ,IAAI,EAAE,GAAG;cACTX,SAAS,EAAE,MAAM;cACjBY,SAAS,EAAE,QAAQ;cACnBtD,OAAO,EAAE;YACX,CAAE;YAAAd,QAAA,eACArI,OAAA;cAAKoD,KAAK,EAAE;gBACV+F,OAAO,EAAE,MAAM;gBACfU,OAAO,EAAE,MAAM;gBACfoC,aAAa,EAAE,QAAQ;gBACvBnC,GAAG,EAAE,MAAM;gBACX4C,SAAS,EAAE;cACb,CAAE;cAAArE,QAAA,gBAEFrI,OAAA;gBAAKoD,KAAK,EAAE;kBACV4F,UAAU,EAAE,mDAAmD;kBAC/DE,MAAM,EAAE,mBAAmB;kBAC3BE,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,QAAQ;kBACjBiD,SAAS,EAAE,QAAQ;kBACnBd,SAAS,EAAE;gBACb,CAAE;gBAAAjD,QAAA,eACArI,OAAA;kBAAKoD,KAAK,EAAE;oBACV4F,UAAU,EAAE,wBAAwB;oBACpCI,YAAY,EAAE,MAAM;oBACpBD,OAAO,EAAE,MAAM;oBACfa,YAAY,EAAE;kBAChB,CAAE;kBAAA3B,QAAA,gBACArI,OAAA;oBAAIoD,KAAK,EAAE;sBACT6F,KAAK,EAAE,SAAS;sBAChBe,YAAY,EAAE,MAAM;sBACpBrD,QAAQ,EAAE,QAAQ;sBAClBoE,UAAU,EAAE;oBACd,CAAE;oBAAA1C,QAAA,EAAC;kBAEH;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACL1J,OAAA;oBAAKoD,KAAK,EAAE;sBACVyG,OAAO,EAAE,MAAM;sBACfC,GAAG,EAAE,SAAS;sBACdsC,SAAS,EAAE;oBACb,CAAE;oBAAA/D,QAAA,gBACArI,OAAA;sBAAKoD,KAAK,EAAE;wBACVyG,OAAO,EAAE,MAAM;wBACfE,cAAc,EAAE,eAAe;wBAC/BiB,UAAU,EAAE,QAAQ;wBACpB7B,OAAO,EAAE,QAAQ;wBACjBH,UAAU,EAAE,0BAA0B;wBACtCI,YAAY,EAAE;sBAChB,CAAE;sBAAAf,QAAA,gBACArI,OAAA;wBAAMoD,KAAK,EAAE;0BAAE2H,UAAU,EAAE,KAAK;0BAAE9B,KAAK,EAAE;wBAAU,CAAE;wBAAAZ,QAAA,EAAC;sBAAQ;wBAAAkB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrE1J,OAAA;wBAAMoD,KAAK,EAAE;0BAAE6F,KAAK,EAAE,SAAS;0BAAE8B,UAAU,EAAE;wBAAM,CAAE;wBAAA1C,QAAA,EAAEjH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEqB;sBAAK;wBAAA8G,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/E,CAAC,eACN1J,OAAA;sBAAKoD,KAAK,EAAE;wBACVyG,OAAO,EAAE,MAAM;wBACfE,cAAc,EAAE,eAAe;wBAC/BiB,UAAU,EAAE,QAAQ;wBACpB7B,OAAO,EAAE,QAAQ;wBACjBH,UAAU,EAAE,0BAA0B;wBACtCI,YAAY,EAAE;sBAChB,CAAE;sBAAAf,QAAA,gBACArI,OAAA;wBAAMoD,KAAK,EAAE;0BAAE2H,UAAU,EAAE,KAAK;0BAAE9B,KAAK,EAAE;wBAAU,CAAE;wBAAAZ,QAAA,EAAC;sBAAW;wBAAAkB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACxE1J,OAAA;wBAAMoD,KAAK,EAAE;0BAAE6F,KAAK,EAAE,SAAS;0BAAE8B,UAAU,EAAE;wBAAM,CAAE;wBAAA1C,QAAA,GAAEjH,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0B,QAAQ,EAAC,QAAM,EAAC,CAAA1B,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE0B,QAAQ,IAAG,CAAC,GAAG,GAAG,GAAG,EAAE;sBAAA;wBAAAyG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/H,CAAC,eACN1J,OAAA;sBAAKoD,KAAK,EAAE;wBACVyG,OAAO,EAAE,MAAM;wBACfE,cAAc,EAAE,eAAe;wBAC/BiB,UAAU,EAAE,QAAQ;wBACpB7B,OAAO,EAAE,QAAQ;wBACjBH,UAAU,EAAE,0BAA0B;wBACtCI,YAAY,EAAE;sBAChB,CAAE;sBAAAf,QAAA,gBACArI,OAAA;wBAAMoD,KAAK,EAAE;0BAAE2H,UAAU,EAAE,KAAK;0BAAE9B,KAAK,EAAE;wBAAU,CAAE;wBAAAZ,QAAA,EAAC;sBAAU;wBAAAkB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvE1J,OAAA;wBAAMoD,KAAK,EAAE;0BAAE6F,KAAK,EAAE,SAAS;0BAAE8B,UAAU,EAAE;wBAAM,CAAE;wBAAA1C,QAAA,GAAEjH,YAAY,aAAZA,YAAY,wBAAAb,sBAAA,GAAZa,YAAY,CAAEwB,eAAe,cAAArC,sBAAA,uBAA7BA,sBAAA,CAA+BkK,cAAc,CAAC,CAAC,EAAC,MAAI;sBAAA;wBAAAlB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/G,CAAC,eACN1J,OAAA;sBAAKoD,KAAK,EAAE;wBACVyG,OAAO,EAAE,MAAM;wBACfE,cAAc,EAAE,eAAe;wBAC/BiB,UAAU,EAAE,QAAQ;wBACpB7B,OAAO,EAAE,QAAQ;wBACjBH,UAAU,EAAE,2CAA2C;wBACvDI,YAAY,EAAE,KAAK;wBACnBH,KAAK,EAAE;sBACT,CAAE;sBAAAZ,QAAA,gBACArI,OAAA;wBAAMoD,KAAK,EAAE;0BAAE2H,UAAU,EAAE;wBAAM,CAAE;wBAAA1C,QAAA,EAAC;sBAAU;wBAAAkB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACrD1J,OAAA;wBAAMoD,KAAK,EAAE;0BAAE2H,UAAU,EAAE,KAAK;0BAAEpE,QAAQ,EAAE;wBAAS,CAAE;wBAAA0B,QAAA,EAAC;sBAAQ;wBAAAkB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN1J,OAAA;gBAAKoD,KAAK,EAAE;kBACV4F,UAAU,EAAE,mDAAmD;kBAC/DE,MAAM,EAAE,mBAAmB;kBAC3BE,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,QAAQ;kBACjBmC,SAAS,EAAE;gBACb,CAAE;gBAAAjD,QAAA,gBACArI,OAAA;kBAAIoD,KAAK,EAAE;oBACT6F,KAAK,EAAE,SAAS;oBAChBe,YAAY,EAAE,MAAM;oBACpBoC,SAAS,EAAE,QAAQ;oBACnBzF,QAAQ,EAAE,QAAQ;oBAClBoE,UAAU,EAAE;kBACd,CAAE;kBAAA1C,QAAA,EAAC;gBAEH;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACL1J,OAAA;kBAAKoD,KAAK,EAAE;oBACVyG,OAAO,EAAE,MAAM;oBACfgD,mBAAmB,EAAE,sCAAsC;oBAC3D/C,GAAG,EAAE;kBACP,CAAE;kBAAAzB,QAAA,gBACArI,OAAA;oBAAKoD,KAAK,EAAE;sBACV4F,UAAU,EAAE,0BAA0B;sBACtCI,YAAY,EAAE,MAAM;sBACpBD,OAAO,EAAE;oBACX,CAAE;oBAAAd,QAAA,eACArI,OAAA;sBAAKoD,KAAK,EAAE;wBAAE4G,YAAY,EAAE,QAAQ;wBAAErD,QAAQ,EAAE,SAAS;wBAAEoE,UAAU,EAAE,KAAK;wBAAE9B,KAAK,EAAE;sBAAU,CAAE;sBAAAZ,QAAA,gBAC/FrI,OAAA;wBAAKoD,KAAK,EAAE;0BAAE4G,YAAY,EAAE;wBAAU,CAAE;wBAAA3B,QAAA,GAAC,SAAE,eAAArI,OAAA;0BAAAqI,QAAA,EAAQ;wBAAiB;0BAAAkB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACnF1J,OAAA;wBAAKoD,KAAK,EAAE;0BAAE4G,YAAY,EAAE;wBAAU,CAAE;wBAAA3B,QAAA,GAAC,eAAG,eAAArI,OAAA;0BAAAqI,QAAA,EAAQ;wBAAY;0BAAAkB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAC/E1J,OAAA;wBAAAqI,QAAA,GAAK,eAAG,eAAArI,OAAA;0BAAAqI,QAAA,EAAQ;wBAAe;0BAAAkB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN1J,OAAA;oBAAKoD,KAAK,EAAE;sBACV4F,UAAU,EAAE,0BAA0B;sBACtCI,YAAY,EAAE,MAAM;sBACpBD,OAAO,EAAE;oBACX,CAAE;oBAAAd,QAAA,eACArI,OAAA;sBAAKoD,KAAK,EAAE;wBAAE4G,YAAY,EAAE,QAAQ;wBAAErD,QAAQ,EAAE,SAAS;wBAAEoE,UAAU,EAAE,KAAK;wBAAE9B,KAAK,EAAE;sBAAU,CAAE;sBAAAZ,QAAA,gBAC/FrI,OAAA;wBAAKoD,KAAK,EAAE;0BAAE4G,YAAY,EAAE;wBAAU,CAAE;wBAAA3B,QAAA,GAAC,eAAG,eAAArI,OAAA;0BAAAqI,QAAA,EAAQ;wBAAiB;0BAAAkB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eACpF1J,OAAA;wBAAKoD,KAAK,EAAE;0BAAE4G,YAAY,EAAE;wBAAU,CAAE;wBAAA3B,QAAA,GAAC,eAAG,eAAArI,OAAA;0BAAAqI,QAAA,EAAQ;wBAAe;0BAAAkB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAClF1J,OAAA;wBAAAqI,QAAA,GAAK,eAAG,eAAArI,OAAA;0BAAAqI,QAAA,EAAQ;wBAAY;0BAAAkB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL1H,qBAAqB,iBACpBhC,OAAA;gBAAKoD,KAAK,EAAE;kBACV4F,UAAU,EAAE,mDAAmD;kBAC/DE,MAAM,EAAE,mBAAmB;kBAC3BE,YAAY,EAAE,MAAM;kBACpBD,OAAO,EAAE,MAAM;kBACfiD,SAAS,EAAE,QAAQ;kBACnBrG,SAAS,EAAE;gBACb,CAAE;gBAAAsC,QAAA,eACArI,OAAA;kBAAGoD,KAAK,EAAE;oBACR8I,MAAM,EAAE,GAAG;oBACXjD,KAAK,EAAE,SAAS;oBAChBtC,QAAQ,EAAE,QAAQ;oBAClBoE,UAAU,EAAE;kBACd,CAAE;kBAAA1C,QAAA,GAAC,mDACsC,EAACrG,qBAAqB,EAAC,aAChE;gBAAA;kBAAAuH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN,eAGD1J,OAAA;gBAAKoD,KAAK,EAAE;kBACVyG,OAAO,EAAE,MAAM;kBACfC,GAAG,EAAE,MAAM;kBACXC,cAAc,EAAE,QAAQ;kBACxB+C,QAAQ,EAAE,MAAM;kBAChB/G,SAAS,EAAE,MAAM;kBACjBgH,UAAU,EAAE;gBACd,CAAE;gBAAA1E,QAAA,gBACArI,OAAA;kBACE4I,OAAO,EAAEA,CAAA,KAAM;oBACb3G,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;oBAChCd,mBAAmB,CAAC,KAAK,CAAC;oBAC1B6F,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,WAAW;kBACpC,CAAE;kBACF9D,KAAK,EAAE;oBACL4F,UAAU,EAAE,mDAAmD;oBAC/DE,MAAM,EAAE,MAAM;oBACdD,KAAK,EAAE,OAAO;oBACdE,OAAO,EAAE,WAAW;oBACpBC,YAAY,EAAE,MAAM;oBACpBzC,QAAQ,EAAE,MAAM;oBAChBoE,UAAU,EAAE,KAAK;oBACjB1B,MAAM,EAAE,SAAS;oBACjBV,UAAU,EAAE,eAAe;oBAC3B2C,SAAS,EAAE,oCAAoC;oBAC/CzB,OAAO,EAAE,MAAM;oBACfmB,UAAU,EAAE,QAAQ;oBACpBlB,GAAG,EAAE,QAAQ;oBACbkD,QAAQ,EAAE,OAAO;oBACjBjD,cAAc,EAAE;kBAClB,CAAE;kBACFmB,YAAY,EAAGC,CAAC,IAAK;oBACnBA,CAAC,CAACC,MAAM,CAAChI,KAAK,CAAC4F,UAAU,GAAG,mDAAmD;oBAC/EmC,CAAC,CAACC,MAAM,CAAChI,KAAK,CAACiI,SAAS,GAAG,kBAAkB;oBAC7CF,CAAC,CAACC,MAAM,CAAChI,KAAK,CAACkI,SAAS,GAAG,oCAAoC;kBACjE,CAAE;kBACFC,YAAY,EAAGJ,CAAC,IAAK;oBACnBA,CAAC,CAACC,MAAM,CAAChI,KAAK,CAAC4F,UAAU,GAAG,mDAAmD;oBAC/EmC,CAAC,CAACC,MAAM,CAAChI,KAAK,CAACiI,SAAS,GAAG,eAAe;oBAC1CF,CAAC,CAACC,MAAM,CAAChI,KAAK,CAACkI,SAAS,GAAG,oCAAoC;kBACjE,CAAE;kBAAAjD,QAAA,GACH,+BACoB,EAACrG,qBAAqB,GAAI,IAAGA,qBAAsB,IAAG,GAAG,EAAE;gBAAA;kBAAAuH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC,eACT1J,OAAA;kBACE4I,OAAO,EAAEA,CAAA,KAAM;oBACb3G,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;oBAChCd,mBAAmB,CAAC,KAAK,CAAC;kBAC5B,CAAE;kBACFiC,KAAK,EAAE;oBACL4F,UAAU,EAAE,mDAAmD;oBAC/DE,MAAM,EAAE,mBAAmB;oBAC3BD,KAAK,EAAE,SAAS;oBAChBE,OAAO,EAAE,WAAW;oBACpBC,YAAY,EAAE,MAAM;oBACpBzC,QAAQ,EAAE,MAAM;oBAChBoE,UAAU,EAAE,KAAK;oBACjB1B,MAAM,EAAE,SAAS;oBACjBV,UAAU,EAAE,eAAe;oBAC3B2C,SAAS,EAAE,+BAA+B;oBAC1CzB,OAAO,EAAE,MAAM;oBACfmB,UAAU,EAAE,QAAQ;oBACpBlB,GAAG,EAAE,QAAQ;oBACbkD,QAAQ,EAAE,OAAO;oBACjBjD,cAAc,EAAE;kBAClB,CAAE;kBACFmB,YAAY,EAAGC,CAAC,IAAK;oBACnBA,CAAC,CAACC,MAAM,CAAChI,KAAK,CAAC4F,UAAU,GAAG,mDAAmD;oBAC/EmC,CAAC,CAACC,MAAM,CAAChI,KAAK,CAACiI,SAAS,GAAG,kBAAkB;oBAC7CF,CAAC,CAACC,MAAM,CAAChI,KAAK,CAACkI,SAAS,GAAG,gCAAgC;kBAC7D,CAAE;kBACFC,YAAY,EAAGJ,CAAC,IAAK;oBACnBA,CAAC,CAACC,MAAM,CAAChI,KAAK,CAAC4F,UAAU,GAAG,mDAAmD;oBAC/EmC,CAAC,CAACC,MAAM,CAAChI,KAAK,CAACiI,SAAS,GAAG,eAAe;oBAC1CF,CAAC,CAACC,MAAM,CAAChI,KAAK,CAACkI,SAAS,GAAG,+BAA+B;kBAC5D,CAAE;kBAAAjD,QAAA,EACH;gBAED;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAGN1J,OAAA;gBAAKoD,KAAK,EAAE;kBACV2C,SAAS,EAAE,QAAQ;kBACnBoD,OAAO,EAAE,MAAM;kBACfH,UAAU,EAAE,wBAAwB;kBACpCI,YAAY,EAAE,MAAM;kBACpBgD,SAAS,EAAE,QAAQ;kBACnBlD,MAAM,EAAE;gBACV,CAAE;gBAAAb,QAAA,eACArI,OAAA;kBAAGoD,KAAK,EAAE;oBACR8I,MAAM,EAAE,GAAG;oBACXvF,QAAQ,EAAE,MAAM;oBAChBsC,KAAK,EAAE,SAAS;oBAChB8B,UAAU,EAAE,KAAK;oBACjB4B,UAAU,EAAE;kBACd,CAAE;kBAAAtE,QAAA,EAAC;gBAEH;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD1J,OAAA,CAACH,uBAAuB;QACtBoN,OAAO,EAAEzL,sBAAuB;QAChC0L,OAAO,EAAEA,CAAA,KAAMzL,yBAAyB,CAAC,KAAK,CAAE;QAChD0L,WAAW,EAAE3M,KAAK,CAAC4M,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC7K,GAAG,MAAKJ,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEwH,UAAU,EAAC,KAAIxH,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEuC,IAAI,CAAC;QAC/FtC,YAAY,EAAED,gBAAiB;QAC/BF,IAAI,EAAEA;MAAK;QAAAqH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAGF1J,OAAA,CAACF,wBAAwB;QACvBmN,OAAO,EAAEvL,gBAAiB;QAC1BwL,OAAO,EAAEA,CAAA,KAAMvL,mBAAmB,CAAC,KAAK,CAAE;QAC1C2L,OAAO,EAAEjJ,uBAAwB;QACjChC,YAAY,EAAED,gBAAiB;QAC/BF,IAAI,EAAEA,IAAK;QACX1B,KAAK,EAAEA;MAAM;QAAA+I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtJ,EAAA,CAj7CID,YAAY;EAAA,QAkBCrB,WAAW,EACCA,WAAW,EACvBC,WAAW;AAAA;AAAAwO,EAAA,GApBxBpN,YAAY;AAm7ClB,eAAeA,YAAY;AAAC,IAAAoN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}