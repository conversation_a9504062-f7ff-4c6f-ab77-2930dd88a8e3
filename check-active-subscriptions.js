const mongoose = require('mongoose');
const User = require('./server/models/userModel');
const Subscription = require('./server/models/subscriptionModel');
const Plan = require('./server/models/planModel');

// Connect to MongoDB
const connectDB = async () => {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect('mongodb+srv://brainwaveuser:<EMAIL>/brainwave?retryWrites=true&w=majority', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
    return true;
  } catch (error) {
    console.error('❌ MongoDB connection error:', error.message);
    return false;
  }
};

// Check active subscriptions for all users
const checkActiveSubscriptions = async () => {
  try {
    console.log('\n🔍 CHECKING ALL USER SUBSCRIPTIONS');
    console.log('=' .repeat(60));
    
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day
    const todayString = today.toISOString().split('T')[0];
    
    console.log(`📅 Today's Date: ${todayString}`);
    console.log('=' .repeat(60));
    
    // Get all users
    const allUsers = await User.find({}).select('firstName lastName username email phoneNumber subscriptionStatus subscriptionEndDate paymentRequired');
    console.log(`\n👥 Total Users Found: ${allUsers.length}`);
    
    // Get all subscriptions
    const allSubscriptions = await Subscription.find({})
      .populate('user', 'firstName lastName username email')
      .populate('activePlan', 'title duration discountedPrice');
    
    console.log(`📋 Total Subscriptions Found: ${allSubscriptions.length}`);
    
    // Analyze subscription data
    let activeSubscriptions = [];
    let expiredSubscriptions = [];
    let pendingSubscriptions = [];
    let usersWithoutSubscriptions = [];
    
    // Check each user's subscription status
    for (const user of allUsers) {
      // Find user's subscriptions
      const userSubscriptions = allSubscriptions.filter(sub => 
        sub.user && sub.user._id.toString() === user._id.toString()
      );
      
      if (userSubscriptions.length === 0) {
        // User has no subscription records
        usersWithoutSubscriptions.push({
          user: user,
          reason: 'No subscription records found'
        });
        continue;
      }
      
      // Find the most recent subscription
      const latestSubscription = userSubscriptions.reduce((latest, current) => {
        const latestDate = new Date(latest.createdAt || latest.startDate || '1970-01-01');
        const currentDate = new Date(current.createdAt || current.startDate || '1970-01-01');
        return currentDate > latestDate ? current : latest;
      });
      
      // Check if subscription is active
      const endDate = latestSubscription.endDate ? new Date(latestSubscription.endDate) : null;
      const isDateValid = endDate && endDate >= today;
      const isStatusActive = latestSubscription.status === 'active';
      const isPaymentPaid = latestSubscription.paymentStatus === 'paid';
      
      const subscriptionInfo = {
        user: user,
        subscription: latestSubscription,
        endDate: endDate,
        endDateString: latestSubscription.endDate,
        isDateValid: isDateValid,
        isStatusActive: isStatusActive,
        isPaymentPaid: isPaymentPaid,
        daysRemaining: endDate ? Math.ceil((endDate - today) / (1000 * 60 * 60 * 24)) : null
      };
      
      if (isStatusActive && isPaymentPaid && isDateValid) {
        activeSubscriptions.push(subscriptionInfo);
      } else if (latestSubscription.status === 'expired' || (endDate && endDate < today)) {
        expiredSubscriptions.push(subscriptionInfo);
      } else {
        pendingSubscriptions.push(subscriptionInfo);
      }
    }
    
    // Display results
    console.log('\n🎯 SUBSCRIPTION ANALYSIS RESULTS');
    console.log('=' .repeat(60));
    
    console.log(`\n✅ ACTIVE SUBSCRIPTIONS: ${activeSubscriptions.length}`);
    console.log('-' .repeat(40));
    activeSubscriptions.forEach((info, index) => {
      const user = info.user;
      const sub = info.subscription;
      console.log(`${index + 1}. ${user.firstName} ${user.lastName} (@${user.username})`);
      console.log(`   📧 Email: ${user.email || 'Not provided'}`);
      console.log(`   📱 Phone: ${user.phoneNumber || 'Not provided'}`);
      console.log(`   📋 Plan: ${sub.activePlan?.title || 'Unknown'}`);
      console.log(`   📅 End Date: ${info.endDateString}`);
      console.log(`   ⏰ Days Remaining: ${info.daysRemaining} days`);
      console.log(`   💳 Payment Status: ${sub.paymentStatus}`);
      console.log(`   📊 Status: ${sub.status}`);
      console.log('');
    });
    
    console.log(`\n❌ EXPIRED SUBSCRIPTIONS: ${expiredSubscriptions.length}`);
    console.log('-' .repeat(40));
    expiredSubscriptions.slice(0, 10).forEach((info, index) => { // Show first 10
      const user = info.user;
      const sub = info.subscription;
      console.log(`${index + 1}. ${user.firstName} ${user.lastName} (@${user.username})`);
      console.log(`   📅 End Date: ${info.endDateString}`);
      console.log(`   ⏰ Days Expired: ${Math.abs(info.daysRemaining || 0)} days ago`);
      console.log(`   📊 Status: ${sub.status}`);
      console.log('');
    });
    if (expiredSubscriptions.length > 10) {
      console.log(`   ... and ${expiredSubscriptions.length - 10} more expired subscriptions`);
    }
    
    console.log(`\n⏳ PENDING/OTHER SUBSCRIPTIONS: ${pendingSubscriptions.length}`);
    console.log('-' .repeat(40));
    pendingSubscriptions.slice(0, 5).forEach((info, index) => { // Show first 5
      const user = info.user;
      const sub = info.subscription;
      console.log(`${index + 1}. ${user.firstName} ${user.lastName} (@${user.username})`);
      console.log(`   📊 Status: ${sub.status}`);
      console.log(`   💳 Payment: ${sub.paymentStatus}`);
      console.log(`   📅 End Date: ${info.endDateString || 'Not set'}`);
      console.log('');
    });
    if (pendingSubscriptions.length > 5) {
      console.log(`   ... and ${pendingSubscriptions.length - 5} more pending subscriptions`);
    }
    
    console.log(`\n👤 USERS WITHOUT SUBSCRIPTIONS: ${usersWithoutSubscriptions.length}`);
    console.log('-' .repeat(40));
    usersWithoutSubscriptions.slice(0, 5).forEach((info, index) => { // Show first 5
      const user = info.user;
      console.log(`${index + 1}. ${user.firstName} ${user.lastName} (@${user.username})`);
      console.log(`   📧 Email: ${user.email || 'Not provided'}`);
      console.log(`   📊 User Status: ${user.subscriptionStatus || 'Not set'}`);
      console.log('');
    });
    if (usersWithoutSubscriptions.length > 5) {
      console.log(`   ... and ${usersWithoutSubscriptions.length - 5} more users without subscriptions`);
    }
    
    // Summary
    console.log('\n📊 SUMMARY');
    console.log('=' .repeat(60));
    console.log(`Total Users: ${allUsers.length}`);
    console.log(`Active Subscriptions: ${activeSubscriptions.length}`);
    console.log(`Expired Subscriptions: ${expiredSubscriptions.length}`);
    console.log(`Pending Subscriptions: ${pendingSubscriptions.length}`);
    console.log(`Users Without Subscriptions: ${usersWithoutSubscriptions.length}`);
    
    const percentageActive = ((activeSubscriptions.length / allUsers.length) * 100).toFixed(1);
    console.log(`\n🎯 Active Subscription Rate: ${percentageActive}%`);
    
    // Export active users list
    if (activeSubscriptions.length > 0) {
      console.log('\n📋 ACTIVE USERS LIST (for easy reference):');
      console.log('-' .repeat(40));
      activeSubscriptions.forEach((info, index) => {
        const user = info.user;
        console.log(`${index + 1}. ${user.firstName} ${user.lastName} (@${user.username}) - Expires: ${info.endDateString} (${info.daysRemaining} days)`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error checking subscriptions:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n✅ Database connection closed');
  }
};

// Run the check
if (require.main === module) {
  connectDB().then((connected) => {
    if (connected) {
      checkActiveSubscriptions();
    } else {
      console.error('❌ Failed to connect to database');
      process.exit(1);
    }
  }).catch((error) => {
    console.error('❌ Unexpected error:', error.message);
    process.exit(1);
  });
}

module.exports = { checkActiveSubscriptions };
