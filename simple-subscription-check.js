const mongoose = require('mongoose');

// Simple subscription check script
console.log('🔄 Starting subscription check...');

// Connect to MongoDB
mongoose.connect('mongodb+srv://brainwaveuser:<EMAIL>/brainwave?retryWrites=true&w=majority')
  .then(() => {
    console.log('✅ Connected to MongoDB');
    return checkSubscriptions();
  })
  .catch((error) => {
    console.error('❌ Connection error:', error.message);
    process.exit(1);
  });

async function checkSubscriptions() {
  try {
    console.log('\n🔍 Checking subscription collections...');
    
    // Get collections directly
    const db = mongoose.connection.db;
    const collections = await db.listCollections().toArray();
    
    console.log('\n📋 Available collections:');
    collections.forEach(col => {
      console.log(`   - ${col.name}`);
    });
    
    // Check users collection
    const usersCollection = db.collection('users');
    const totalUsers = await usersCollection.countDocuments();
    console.log(`\n👥 Total users: ${totalUsers}`);
    
    // Check subscriptions collection
    const subscriptionsCollection = db.collection('subscriptions');
    const totalSubscriptions = await subscriptionsCollection.countDocuments();
    console.log(`📋 Total subscriptions: ${totalSubscriptions}`);
    
    if (totalSubscriptions === 0) {
      console.log('⚠️  No subscriptions found in database');
      await mongoose.connection.close();
      return;
    }
    
    // Get current date
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayString = today.toISOString().split('T')[0];
    
    console.log(`\n📅 Today's date: ${todayString}`);
    console.log('\n🔍 Analyzing subscriptions...');
    
    // Get all subscriptions with user data
    const subscriptions = await subscriptionsCollection.aggregate([
      {
        $lookup: {
          from: 'users',
          localField: 'user',
          foreignField: '_id',
          as: 'userData'
        }
      },
      {
        $lookup: {
          from: 'plans',
          localField: 'activePlan',
          foreignField: '_id',
          as: 'planData'
        }
      }
    ]).toArray();
    
    console.log(`📊 Found ${subscriptions.length} subscriptions with user data`);
    
    let activeCount = 0;
    let expiredCount = 0;
    let pendingCount = 0;
    
    const activeUsers = [];
    const expiredUsers = [];
    
    // Analyze each subscription
    subscriptions.forEach((sub, index) => {
      const user = sub.userData[0];
      const plan = sub.planData[0];
      
      if (!user) {
        console.log(`⚠️  Subscription ${index + 1}: No user data found`);
        return;
      }
      
      const endDate = sub.endDate ? new Date(sub.endDate) : null;
      const isDateValid = endDate && endDate >= today;
      const isStatusActive = sub.status === 'active';
      const isPaymentPaid = sub.paymentStatus === 'paid';
      
      const userInfo = {
        name: `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.username || 'Unknown',
        username: user.username || 'N/A',
        email: user.email || 'Not provided',
        phone: user.phoneNumber || 'Not provided',
        planTitle: plan?.title || 'Unknown Plan',
        endDate: sub.endDate || 'Not set',
        status: sub.status,
        paymentStatus: sub.paymentStatus,
        daysRemaining: endDate ? Math.ceil((endDate - today) / (1000 * 60 * 60 * 24)) : null
      };
      
      if (isStatusActive && isPaymentPaid && isDateValid) {
        activeCount++;
        activeUsers.push(userInfo);
      } else if (sub.status === 'expired' || (endDate && endDate < today)) {
        expiredCount++;
        expiredUsers.push(userInfo);
      } else {
        pendingCount++;
      }
    });
    
    // Display results
    console.log('\n🎯 SUBSCRIPTION ANALYSIS RESULTS');
    console.log('=' .repeat(60));
    console.log(`✅ Active subscriptions: ${activeCount}`);
    console.log(`❌ Expired subscriptions: ${expiredCount}`);
    console.log(`⏳ Pending/Other subscriptions: ${pendingCount}`);
    
    if (activeUsers.length > 0) {
      console.log('\n✅ USERS WITH ACTIVE SUBSCRIPTIONS:');
      console.log('-' .repeat(50));
      activeUsers.forEach((user, index) => {
        console.log(`${index + 1}. ${user.name} (@${user.username})`);
        console.log(`   📧 Email: ${user.email}`);
        console.log(`   📱 Phone: ${user.phone}`);
        console.log(`   📋 Plan: ${user.planTitle}`);
        console.log(`   📅 End Date: ${user.endDate}`);
        console.log(`   ⏰ Days Remaining: ${user.daysRemaining} days`);
        console.log(`   💳 Payment: ${user.paymentStatus} | Status: ${user.status}`);
        console.log('');
      });
    } else {
      console.log('\n⚠️  NO ACTIVE SUBSCRIPTIONS FOUND');
    }
    
    if (expiredUsers.length > 0) {
      console.log('\n❌ RECENTLY EXPIRED SUBSCRIPTIONS (showing first 10):');
      console.log('-' .repeat(50));
      expiredUsers.slice(0, 10).forEach((user, index) => {
        console.log(`${index + 1}. ${user.name} (@${user.username})`);
        console.log(`   📅 End Date: ${user.endDate}`);
        console.log(`   ⏰ Expired: ${Math.abs(user.daysRemaining || 0)} days ago`);
        console.log(`   📋 Plan: ${user.planTitle}`);
        console.log('');
      });
      
      if (expiredUsers.length > 10) {
        console.log(`   ... and ${expiredUsers.length - 10} more expired subscriptions`);
      }
    }
    
    // Summary
    const totalAnalyzed = activeCount + expiredCount + pendingCount;
    const activePercentage = totalAnalyzed > 0 ? ((activeCount / totalAnalyzed) * 100).toFixed(1) : 0;
    
    console.log('\n📊 SUMMARY');
    console.log('=' .repeat(60));
    console.log(`Total subscriptions analyzed: ${totalAnalyzed}`);
    console.log(`Active subscription rate: ${activePercentage}%`);
    
    if (activeUsers.length > 0) {
      console.log('\n📋 QUICK ACTIVE USERS LIST:');
      console.log('-' .repeat(40));
      activeUsers.forEach((user, index) => {
        console.log(`${index + 1}. ${user.name} - Expires: ${user.endDate} (${user.daysRemaining} days)`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error during analysis:', error.message);
    console.error(error.stack);
  } finally {
    await mongoose.connection.close();
    console.log('\n✅ Database connection closed');
  }
}
