import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { motion } from 'framer-motion';
import { message } from 'antd';
import { FaCrown, FaCalendarAlt, FaCheckCircle, FaTimesCircle, FaCreditCard, FaUser } from 'react-icons/fa';
import { getPlans } from '../../../apicalls/plans';
import { addPayment, checkPaymentStatus } from '../../../apicalls/payment';
import { ShowLoading, HideLoading } from '../../../redux/loaderSlice';
import UpgradeRestrictionModal from '../../../components/UpgradeRestrictionModal/UpgradeRestrictionModal';
import SubscriptionExpiredModal from '../../../components/SubscriptionExpiredModal/SubscriptionExpiredModal';
import './Subscription.css';

const Subscription = () => {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(false);
  const [paymentLoading, setPaymentLoading] = useState(null); // Changed to store plan ID instead of boolean
  const [showProcessingModal, setShowProcessingModal] = useState(false);

  // Debug: Log showProcessingModal state changes
  useEffect(() => {
    console.log('🔍 showProcessingModal state changed to:', showProcessingModal);
  }, [showProcessingModal]);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [paymentStatus, setPaymentStatus] = useState('');
  const [showUpgradeRestriction, setShowUpgradeRestriction] = useState(false);
  const [showExpiredModal, setShowExpiredModal] = useState(false);
  const [processingStartTime, setProcessingStartTime] = useState(null);
  const [showTryAgain, setShowTryAgain] = useState(false);
  const [autoNavigateCountdown, setAutoNavigateCountdown] = useState(null);
  const { user } = useSelector((state) => state.user);
  const { subscriptionData } = useSelector((state) => state.subscription);
  const dispatch = useDispatch();

  // Fallback sample plans in case API fails
  const samplePlans = [
    {
      _id: "basic-plan-sample",
      title: "Basic Membership",
      features: [
        "2-month full access",
        "Unlimited quizzes",
        "Personalized profile",
        "AI chat for instant help",
        "Forum for student discussions",
        "Study notes",
        "Past papers",
        "Books",
        "Learning videos",
        "Track progress with rankings"
      ],
      actualPrice: 28570,
      discountedPrice: 20000,
      discountPercentage: 30,
      duration: 2,
      status: true
    },
    {
      _id: "premium-plan-sample",
      title: "Premium Plan",
      features: [
        "3-month full access",
        "Unlimited quizzes",
        "Personalized profile",
        "AI chat for instant help",
        "Forum for student discussions",
        "Study notes",
        "Past papers",
        "Books",
        "Learning videos",
        "Track progress with rankings",
        "Priority support"
      ],
      actualPrice: 45000,
      discountedPrice: 35000,
      discountPercentage: 22,
      duration: 3,
      status: true
    }
  ];

  useEffect(() => {
    fetchPlans();
    checkCurrentSubscription();
  }, []);

  // Handle body scroll lock when modals are open (simplified approach)
  useEffect(() => {
    if (showProcessingModal || showSuccessModal) {
      // Simply prevent body scroll without position fixed
      document.body.style.overflow = 'hidden';
    } else {
      // Restore body scroll
      document.body.style.overflow = '';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = '';
    };
  }, [showProcessingModal, showSuccessModal]);

  // Check for expired subscription and show modal
  useEffect(() => {
    if (subscriptionData && isSubscriptionExpired()) {
      console.log('🚫 Subscription expired, showing modal');
      setShowExpiredModal(true);
    } else {
      setShowExpiredModal(false);
    }
  }, [subscriptionData]);

  const fetchPlans = async () => {
    try {
      setLoading(true);
      console.log('Fetching plans...');
      const response = await getPlans();
      console.log('Plans response:', response);

      if (response.success && response.data && response.data.length > 0) {
        setPlans(response.data);
        console.log('Plans loaded successfully from API:', response.data);
      } else if (Array.isArray(response) && response.length > 0) {
        // Handle case where response is directly an array of plans
        setPlans(response);
        console.log('Plans loaded as array from API:', response);
      } else {
        console.warn('No plans from API, using sample plans');
        setPlans(samplePlans);
        message.info('Showing sample plans. Please check your connection.');
      }
    } catch (error) {
      console.error('Error loading plans from API:', error);
      console.log('Using fallback sample plans');
      setPlans(samplePlans);
      message.warning('Using sample plans. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  const checkCurrentSubscription = async () => {
    try {
      const response = await checkPaymentStatus();
      console.log('Current subscription:', response);
    } catch (error) {
      console.log('No active subscription found');
    }
  };

  // Check if subscription is expired
  const isSubscriptionExpired = () => {
    if (!subscriptionData) return true;

    // If no subscription data, consider expired
    if (!subscriptionData.endDate) return true;

    // If payment status is not paid, consider expired
    if (subscriptionData.paymentStatus !== 'paid') return true;

    // If status is not active, consider expired
    if (subscriptionData.status !== 'active') return true;

    // Check if end date has passed
    const endDate = new Date(subscriptionData.endDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day
    endDate.setHours(0, 0, 0, 0); // Reset time to start of day

    return endDate < today;
  };

  // Handle subscription renewal from expired modal
  const handleRenewSubscription = async (selectedPlan) => {
    setShowExpiredModal(false);
    await handlePlanSelect(selectedPlan);
  };

  // Handle closing payment processing modal
  const handleCloseProcessingModal = () => {
    setShowProcessingModal(false);
    setPaymentLoading(null); // Reset to null instead of false
    setShowTryAgain(false);
    setProcessingStartTime(null);
    setPaymentStatus('');
    message.info('Payment process cancelled. You can try again anytime.');
  };

  // Handle try again functionality
  const handleTryAgain = () => {
    if (selectedPlan) {
      setShowTryAgain(false);
      setProcessingStartTime(null);
      handlePlanSelect(selectedPlan);
    }
  };

  // Test success modal (for debugging)
  const testSuccessModal = () => {
    console.log('🧪 Testing success modal...');
    setShowProcessingModal(false);
    setShowSuccessModal(true);
    setPaymentLoading(null);
  };

  // Test processing modal (for debugging)
  const testProcessingModal = () => {
    console.log('🧪 Testing processing modal...');
    setShowProcessingModal(true);
    setPaymentStatus('Testing processing modal...');
    setSelectedPlan(plans[0] || { title: 'Test Plan', discountedPrice: 5000, duration: 1 });
  };

  const handlePlanSelect = async (plan) => {
    // Check if user already has an active subscription
    if (subscriptionData && subscriptionData.status === 'active' && subscriptionData.paymentStatus === 'paid') {
      console.log('🚫 User already has active subscription:', subscriptionData);
      setShowUpgradeRestriction(true);
      return;
    }

    if (!user.phoneNumber || !/^(06|07)\d{8}$/.test(user.phoneNumber)) {
      message.error('Please update your phone number in your profile before subscribing');
      return;
    }

    try {
      console.log('🚀 Starting payment for plan:', plan.title);
      console.log('🔧 IMMEDIATELY showing processing modal...');

      // IMMEDIATELY show processing modal when user chooses plan
      setSelectedPlan(plan);
      setPaymentLoading(plan._id);
      setShowProcessingModal(true);
      setShowTryAgain(false);
      setProcessingStartTime(Date.now());
      setPaymentStatus('🚀 Preparing your payment request...');

      console.log('✅ Processing modal IMMEDIATELY displayed');

      // Small delay to ensure modal is visible before API call
      await new Promise(resolve => setTimeout(resolve, 200));

      // Set timer for try again button (10 seconds)
      const tryAgainTimer = setTimeout(() => {
        setShowTryAgain(true);
      }, 10000);

      const paymentData = {
        plan: plan,
        userId: user._id,
        userPhone: user.phoneNumber,
        userEmail: user.email || `${user.name?.replace(/\s+/g, '').toLowerCase()}@brainwave.temp`
      };

      setPaymentStatus('📤 Sending payment request to ZenoPay...');
      const response = await addPayment(paymentData);

      if (response.success) {
        setPaymentStatus('Payment sent! Check your phone for SMS confirmation...');

        console.log('💳 Payment response:', response);
        console.log('🆔 Order ID:', response.order_id);

        // Show confirmation message to user
        message.success({
          content: `💳 Payment initiated! 📱 Check your phone (${user.phoneNumber}) for SMS confirmation from ZenoPay.`,
          duration: 8,
          style: {
            marginTop: '20vh',
          }
        });

        // Start checking payment status immediately
        const orderIdToCheck = response.order_id || response.data?.order_id || 'demo_order';
        console.log('🔍 Starting payment confirmation check for order:', orderIdToCheck);

        checkPaymentConfirmation(orderIdToCheck);

      } else {
        throw new Error(response.message || 'Payment failed');
      }
    } catch (error) {
      console.error('❌ Payment failed:', error);
      setShowProcessingModal(false);
      message.error('Payment failed: ' + error.message);
      setPaymentLoading(null); // Reset to null
    }
  };

  const checkPaymentConfirmation = async (orderId) => {
    console.log('🚀 Starting payment confirmation check for order:', orderId);
    let isPolling = true;
    let handleVisibilityChange;

    try {
      setPaymentStatus('📱 Complete the payment on your phone, we\'ll detect it automatically...');

      // Poll payment status every 2 seconds for optimal responsiveness
      let attempts = 0;
      const maxAttempts = 150; // 150 attempts * 2 seconds = 5 minutes

      const pollPaymentStatus = async () => {
        attempts++;
        console.log(`🔍 Payment status check attempt ${attempts}/${maxAttempts} for order:`, orderId);

        try {
          const statusResponse = await checkPaymentStatus({ orderId });
          console.log('📊 Payment status response:', statusResponse);
          console.log('🔍 Checking payment conditions:');
          console.log('  - Live payment:', statusResponse?.paymentStatus === 'paid' && statusResponse?.status === 'active');
          console.log('  - Demo payment:', statusResponse?.status === 'completed' && statusResponse?.success === true);

          if (statusResponse && (
            (statusResponse.paymentStatus === 'paid' && statusResponse.status === 'active') ||
            (statusResponse.status === 'completed' && statusResponse.success === true)
          )) {
            // Payment confirmed immediately!
            isPolling = false; // Stop polling
            if (handleVisibilityChange) {
              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener
            }

            setPaymentStatus('🎉 Payment confirmed! Activating your subscription...');
            console.log('✅ Payment confirmed, preparing to show success modal...');

            // Show success INSTANTLY - no delay
            console.log('🔄 Setting modal states - Processing: false, Success: true');
            setShowProcessingModal(false);
            setShowSuccessModal(true);
            setPaymentLoading(null);
            console.log('✅ Success modal state set to true');

            // Refresh subscription data
            checkCurrentSubscription();

            // Show immediate success message
            message.success({
              content: '🎉 Payment confirmed! All features are now unlocked!',
              duration: 5,
              style: {
                marginTop: '20vh',
                fontSize: '16px'
              }
            });

            // Start countdown for auto-navigation to hub
            setAutoNavigateCountdown(5);
            const countdownInterval = setInterval(() => {
              setAutoNavigateCountdown(prev => {
                if (prev <= 1) {
                  clearInterval(countdownInterval);
                  console.log('🏠 Auto-navigating to hub after successful payment...');
                  setShowSuccessModal(false);
                  window.location.href = '/user/hub';
                  return null;
                }
                return prev - 1;
              });
            }, 1000);

          } else if (attempts >= maxAttempts) {
            // Timeout - but don't fail completely
            isPolling = false; // Stop polling
            if (handleVisibilityChange) {
              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener
            }

            setPaymentStatus('⏰ Still waiting for confirmation. Please complete the payment on your phone.');

            setTimeout(() => {
              setShowProcessingModal(false);
              setPaymentLoading(null); // Reset to null
              message.warning('Payment confirmation is taking longer than expected. Please check your subscription status or try again.');
            }, 2000);

          } else {
            // Continue polling - NO TIME INDICATION, just encouraging message
            setPaymentStatus('📱 Complete the payment on your phone, we\'ll detect it automatically...');
            setTimeout(pollPaymentStatus, 2000); // Check every 2 seconds for better performance
          }

        } catch (error) {
          console.error('Payment status check error:', error);

          // Handle specific error types
          if (error.message && error.message.includes('404')) {
            console.error('❌ Payment status endpoint not found (404)');
            isPolling = false; // Stop polling
            if (handleVisibilityChange) {
              document.removeEventListener('visibilitychange', handleVisibilityChange);
            }
            setShowProcessingModal(false);
            setPaymentLoading(null); // Reset to null
            message.error('Payment verification service is temporarily unavailable. Please contact support or check your subscription status manually.');
            return;
          }

          if (error.message && error.message.includes('401')) {
            console.error('❌ Authentication required for payment status check');
            isPolling = false; // Stop polling
            if (handleVisibilityChange) {
              document.removeEventListener('visibilitychange', handleVisibilityChange);
            }
            setShowProcessingModal(false);
            setPaymentLoading(null); // Reset to null
            message.error('Please login again to check payment status.');
            return;
          }

          if (attempts >= maxAttempts) {
            isPolling = false; // Stop polling
            if (handleVisibilityChange) {
              document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener
            }
            setShowProcessingModal(false);
            setPaymentLoading(null); // Reset to null
            message.error('Unable to confirm payment status. Please check your subscription status manually.');
          } else {
            // Continue polling even if there's an error (unless it's a critical error)
            setTimeout(pollPaymentStatus, 1000);
          }
        }
      };

      // Add visibility change listener to check immediately when user returns to tab
      handleVisibilityChange = () => {
        if (!document.hidden && isPolling) {
          console.log('User returned to tab, checking payment status immediately...');
          setPaymentStatus('🔍 Checking payment status...');
          // Trigger immediate check
          setTimeout(() => pollPaymentStatus(), 100);
        }
      };

      document.addEventListener('visibilitychange', handleVisibilityChange);

      // Start polling immediately (no delay) - check right away
      setTimeout(pollPaymentStatus, 500); // Start checking after 0.5 seconds

    } catch (error) {
      isPolling = false; // Stop polling
      if (handleVisibilityChange) {
        document.removeEventListener('visibilitychange', handleVisibilityChange); // Clean up listener
      }
      setShowProcessingModal(false);
      message.error('Payment confirmation failed: ' + error.message);
      setPaymentLoading(null); // Reset to null
    }
  };

  const getSubscriptionStatus = () => {
    if (subscriptionData && subscriptionData.paymentStatus === 'paid' && subscriptionData.status === 'active') {
      const endDate = new Date(subscriptionData.endDate);
      const now = new Date();
      if (endDate > now) {
        return 'active';
      }
    }
    
    if (user?.subscriptionStatus === 'expired' || (subscriptionData && subscriptionData.status === 'expired')) {
      return 'expired';
    }
    
    return 'none';
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getDaysRemaining = () => {
    if (!subscriptionData?.endDate) return 0;
    const endDate = new Date(subscriptionData.endDate);
    const now = new Date();
    const diffTime = endDate - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  const subscriptionStatus = getSubscriptionStatus();

  return (
    <div className="subscription-page">
      <div className="subscription-container">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="subscription-header"
        >
          {/* Debug button - remove in production */}
          <button
            onClick={() => {
              console.log('🧪 Testing success modal...');
              setSelectedPlan(plans[0] || { title: 'Test Plan', duration: 1, discountedPrice: 13000 });
              setShowSuccessModal(true);
            }}
            style={{
              position: 'fixed',
              top: '10px',
              right: '10px',
              background: '#52c41a',
              color: 'white',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '4px',
              cursor: 'pointer',
              zIndex: 9999
            }}
          >
            🧪 Test Success Modal
          </button>
          <h1 className="page-title">
            <FaCrown className="title-icon" />
            Subscription Management
          </h1>
          <p className="page-subtitle">Manage your subscription and access premium features</p>
        </motion.div>

        {/* Current Subscription Status */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="current-subscription"
        >
          <h2 className="section-title">Current Subscription</h2>
          
          {subscriptionStatus === 'active' && (
            <div className="subscription-card active">
              <div className="subscription-status">
                <FaCheckCircle className="status-icon active" />
                <span className="status-text">Active Subscription</span>
              </div>
              <div className="subscription-details">
                <div className="detail-item">
                  <FaCrown className="detail-icon" />
                  <span>Plan: {subscriptionData?.activePlan?.title || 'Premium Plan'}</span>
                </div>
                <div className="detail-item">
                  <FaCalendarAlt className="detail-icon" />
                  <span>Expires: {formatDate(subscriptionData?.endDate)}</span>
                </div>
                <div className="detail-item">
                  <FaCheckCircle className="detail-icon" />
                  <span>Days Remaining: {getDaysRemaining()}</span>
                </div>
              </div>
            </div>
          )}

          {subscriptionStatus === 'expired' && (
            <div className="subscription-card expired">
              <div className="subscription-status">
                <FaTimesCircle className="status-icon expired" />
                <span className="status-text">Subscription Expired</span>
              </div>
              <div className="subscription-details">
                <div className="detail-item">
                  <FaCalendarAlt className="detail-icon" />
                  <span>Expired: {formatDate(subscriptionData?.endDate)}</span>
                </div>
                <p className="renewal-message">
                  Your subscription has expired. Choose a new plan below to continue accessing premium features.
                </p>
              </div>
            </div>
          )}

          {subscriptionStatus === 'none' && (
            <div className="subscription-card none">
              <div className="subscription-status">
                <FaUser className="status-icon none" />
                <span className="status-text">Free Account</span>
              </div>
              <div className="subscription-details">
                <p className="upgrade-message">
                  You're currently using a free account. Upgrade to a premium plan to unlock all features.
                </p>
              </div>
            </div>
          )}
        </motion.div>

        {/* Available Plans */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="available-plans"
        >
          <h2 className="section-title">
            {subscriptionStatus === 'active'
              ? '🚀 Upgrade Your Plan'
              : subscriptionStatus === 'expired'
                ? '🔄 Renew Your Subscription'
                : '🎯 Choose Your Plan'
            }
          </h2>

          {/* Temporary Test Buttons */}
          <div style={{ display: 'flex', gap: '10px', justifyContent: 'center', marginBottom: '20px' }}>
            <button
              onClick={testProcessingModal}
              style={{
                background: '#ff6b6b',
                color: 'white',
                border: 'none',
                padding: '8px 16px',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              🧪 Test Processing Modal
            </button>
            <button
              onClick={testSuccessModal}
              style={{
                background: '#51cf66',
                color: 'white',
                border: 'none',
                padding: '8px 16px',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              🧪 Test Success Modal
            </button>
          </div>
          <p className="section-subtitle">
            {subscriptionStatus === 'active'
              ? 'Upgrade to a longer plan for better value and extended access'
              : subscriptionStatus === 'expired'
                ? 'Your subscription has expired. Renew now to continue accessing premium features'
                : 'Select a subscription plan to unlock all premium features and start your learning journey'
            }
          </p>
          
          {loading ? (
            <div className="loading-state">
              <div className="spinner"></div>
              <p>Loading plans...</p>
            </div>
          ) : plans.length === 0 ? (
            <div className="no-plans-state">
              <div className="no-plans-icon">📋</div>
              <h3>No Plans Available</h3>
              <p>Plans are currently being loaded. Please refresh the page or try again later.</p>
              <button className="refresh-btn" onClick={fetchPlans}>
                🔄 Refresh Plans
              </button>
            </div>
          ) : (
            <div className="plans-grid">
              {plans.map((plan) => (
                <motion.div
                  key={plan._id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="plan-card"
                >
                  <div className="plan-header">
                    <h3 className="plan-title">{plan.title}</h3>
                    {plan.title?.toLowerCase().includes('standard') && (
                      <span className="plan-badge">🔥 Popular</span>
                    )}
                  </div>
                  
                  <div className="plan-pricing">
                    <div className="price-display">
                      <div className="current-price">
                        <span className="currency">TZS</span>
                        {plan.discountedPrice?.toLocaleString()}
                      </div>
                      {plan.actualPrice > plan.discountedPrice && (
                        <>
                          <span className="original-price">{plan.actualPrice?.toLocaleString()} TZS</span>
                          <span className="discount-badge">
                            {Math.round(((plan.actualPrice - plan.discountedPrice) / plan.actualPrice) * 100)}% OFF
                          </span>
                        </>
                      )}
                    </div>
                    <div className="plan-duration">
                      <span className="duration-highlight">{plan.duration}</span> month{plan.duration > 1 ? 's' : ''} access
                    </div>
                  </div>

                  <div className="plan-features">
                    {plan.features?.slice(0, 5).map((feature, index) => (
                      <div key={index} className="feature-item">
                        <FaCheckCircle className="feature-icon" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>

                  <button
                    className="select-plan-btn"
                    onClick={() => handlePlanSelect(plan)}
                    disabled={paymentLoading === plan._id}
                    style={{
                      background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '12px',
                      padding: '1rem 1.5rem',
                      fontSize: '1rem',
                      fontWeight: '600',
                      cursor: paymentLoading === plan._id ? 'not-allowed' : 'pointer',
                      transition: 'all 0.3s ease',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '0.5rem',
                      width: '100%',
                      opacity: paymentLoading === plan._id ? 0.6 : 1
                    }}
                    onMouseEnter={(e) => {
                      if (paymentLoading !== plan._id) {
                        e.target.style.background = 'linear-gradient(135deg, #1d4ed8, #1e40af)';
                        e.target.style.transform = 'translateY(-2px)';
                        e.target.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.4)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (paymentLoading !== plan._id) {
                        e.target.style.background = 'linear-gradient(135deg, #3b82f6, #1d4ed8)';
                        e.target.style.transform = 'translateY(0)';
                        e.target.style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.3)';
                      }
                    }}
                  >
                    <FaCreditCard className="btn-icon" />
                    {paymentLoading === plan._id
                      ? 'Processing...'
                      : subscriptionStatus === 'active'
                        ? 'Click to Upgrade'
                        : subscriptionStatus === 'expired'
                          ? 'Click to Renew'
                          : 'Click to Pay'
                    }
                  </button>
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>

        {/* Phone Number Warning */}
        {(!user.phoneNumber || !/^(06|07)\d{8}$/.test(user.phoneNumber)) && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="phone-warning"
          >
            <div className="warning-content">
              <FaTimesCircle className="warning-icon" />
              <div>
                <h4>Phone Number Required</h4>
                <p>Please update your phone number in your profile to subscribe to a plan.</p>
                <button 
                  className="update-phone-btn"
                  onClick={() => window.location.href = '/profile'}
                >
                  Update Phone Number
                </button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Enhanced Payment Processing Modal */}
        {showProcessingModal && (
          <div
            className="modal-overlay-best modal-overlay-enhanced"
            style={{
              position: 'fixed',
              top: '0',
              left: '0',
              width: '100vw',
              height: '100vh',
              background: 'rgba(0, 0, 0, 0.3)', // Much lighter overlay
              backdropFilter: 'blur(6px)',
              WebkitBackdropFilter: 'blur(6px)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: '10000',
              padding: '20px',
              boxSizing: 'border-box',
              overflowY: 'auto' // Enable scrolling
            }}
            onClick={(e) => e.target === e.currentTarget && handleCloseProcessingModal()}
          >
            <div
              className="modal-container-best modal-container-enhanced"
              style={{
                background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
                borderRadius: '20px',
                boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1)',
                border: '1px solid rgba(59, 130, 246, 0.1)',
                width: '100%',
                maxWidth: '650px', // Larger modal
                maxHeight: '85vh', // More height
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden',
                position: 'relative',
                transform: 'translateZ(0)',
                margin: 'auto' // Center in scrollable area
              }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Professional Header */}
              <div style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                padding: '1.5rem',
                color: 'white',
                textAlign: 'center',
                position: 'relative'
              }}>
                <button
                  className="payment-modal-close"
                  onClick={handleCloseProcessingModal}
                  title="Close payment window"
                  style={{
                    position: 'absolute',
                    top: '12px',
                    right: '12px',
                    background: 'rgba(255, 255, 255, 0.2)',
                    border: 'none',
                    borderRadius: '50%',
                    width: '32px',
                    height: '32px',
                    color: 'white',
                    fontSize: '16px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.3s ease',
                    zIndex: 10
                  }}
                  onMouseEnter={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.3)'}
                  onMouseLeave={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.2)'}
                >
                  ✕
                </button>

                <div style={{ marginBottom: '0.5rem' }}>
                  <div className="payment-processing-animation" style={{
                    width: '60px',
                    height: '60px',
                    margin: '0 auto 1rem',
                    position: 'relative'
                  }}>
                    <div className="payment-spinner" style={{
                      width: '100%',
                      height: '100%',
                      border: '3px solid rgba(255, 255, 255, 0.3)',
                      borderTop: '3px solid white',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }}></div>
                    <div style={{
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                      fontSize: '20px'
                    }}>💳</div>
                  </div>
                </div>

                <h3 style={{
                  margin: '0',
                  fontSize: '1.4rem',
                  fontWeight: '700',
                  textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
                }}>
                  Processing Payment
                </h3>
                <p style={{
                  margin: '0.5rem 0 0 0',
                  fontSize: '0.9rem',
                  opacity: '0.9'
                }}>
                  Secure transaction in progress
                </p>
              </div>

              {/* Enhanced Scrollable Content Area */}
              <div
                className="modal-scrollable-content"
                style={{
                  flex: '1',
                  overflowY: 'auto',
                  overflowX: 'hidden',
                  padding: '0',
                  maxHeight: 'calc(85vh - 140px)', // Ensure scrolling works
                  scrollbarWidth: 'thin',
                  scrollbarColor: 'rgba(59, 130, 246, 0.6) rgba(0, 0, 0, 0.1)'
                }}>
                <div style={{
                  padding: '28px',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '24px',
                  minHeight: '100%'
                }}>
                {/* Payment Status */}
                <div style={{
                  background: 'rgba(59, 130, 246, 0.05)',
                  border: '1px solid rgba(59, 130, 246, 0.2)',
                  borderRadius: '10px',
                  padding: '0.75rem',
                  textAlign: 'center'
                }}>
                  <p style={{
                    margin: '0',
                    color: '#1e40af',
                    fontSize: '0.95rem',
                    fontWeight: '500'
                  }}>
                    {paymentStatus}
                  </p>
                </div>

                {/* Plan Details */}
                <div style={{
                  background: 'rgba(16, 185, 129, 0.05)',
                  border: '1px solid rgba(16, 185, 129, 0.2)',
                  borderRadius: '10px',
                  padding: '0.75rem'
                }}>
                  <h4 style={{
                    margin: '0 0 0.5rem 0',
                    color: '#065f46',
                    fontSize: '1.1rem',
                    fontWeight: '600'
                  }}>
                    {selectedPlan?.title}
                  </h4>
                  <div style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    fontSize: '0.9rem',
                    color: '#047857'
                  }}>
                    <span>Amount: <strong>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</strong></span>
                    <span>Duration: <strong>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</strong></span>
                  </div>
                </div>

                {/* Phone Instructions */}
                <div style={{
                  background: 'rgba(245, 158, 11, 0.05)',
                  border: '1px solid rgba(245, 158, 11, 0.2)',
                  borderRadius: '10px',
                  padding: '0.75rem'
                }}>
                  <h4 style={{
                    margin: '0 0 0.75rem 0',
                    color: '#92400e',
                    fontSize: '1rem',
                    fontWeight: '600',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}>
                    📱 Check Your Phone
                  </h4>
                  <p style={{
                    margin: '0 0 0.75rem 0',
                    color: '#b45309',
                    fontSize: '0.9rem',
                    fontWeight: '500',
                    background: 'rgba(245, 158, 11, 0.1)',
                    padding: '0.5rem',
                    borderRadius: '8px',
                    textAlign: 'center'
                  }}>
                    Number: {user?.phoneNumber}
                  </p>
                  <div style={{
                    color: '#a16207',
                    fontSize: '0.85rem',
                    lineHeight: '1.5'
                  }}>
                    <div style={{ marginBottom: '0.25rem' }}>1. You will receive an SMS with payment instructions</div>
                    <div style={{ marginBottom: '0.25rem' }}>2. Follow the SMS steps to confirm payment</div>
                    <div>3. Complete the mobile money transaction</div>
                  </div>
                </div>

                  {/* Try Again Section - Always Visible */}
                  {showTryAgain && (
                    <div style={{
                      background: 'rgba(239, 68, 68, 0.05)',
                      border: '1px solid rgba(239, 68, 68, 0.2)',
                      borderRadius: '12px',
                      padding: '16px',
                      textAlign: 'center',
                      marginTop: '8px'
                    }}>
                    <p style={{
                      margin: '0 0 0.5rem 0',
                      color: '#dc2626',
                      fontSize: '0.9rem',
                      fontWeight: '600'
                    }}>
                      ⚠️ Taking longer than expected?
                    </p>
                    <p style={{
                      margin: '0 0 1rem 0',
                      color: '#b91c1c',
                      fontSize: '0.8rem'
                    }}>
                      If you haven't received SMS or facing connection issues:
                    </p>
                    <button
                      className="try-again-button"
                      onClick={handleTryAgain}
                      style={{
                        background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                        color: 'white',
                        border: 'none',
                        padding: '0.75rem 1.5rem',
                        borderRadius: '10px',
                        fontSize: '0.9rem',
                        fontWeight: '600',
                        cursor: 'pointer',
                        transition: 'all 0.3s ease',
                        boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',
                        width: '100%',
                        maxWidth: '200px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '0.5rem',
                        margin: '0 auto'
                      }}
                      onMouseEnter={(e) => {
                        e.target.style.background = 'linear-gradient(135deg, #1d4ed8 0%, #1e3a8a 100%)';
                        e.target.style.transform = 'translateY(-2px)';
                        e.target.style.boxShadow = '0 6px 16px rgba(59, 130, 246, 0.4)';
                      }}
                      onMouseLeave={(e) => {
                        e.target.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';
                        e.target.style.transform = 'translateY(0)';
                        e.target.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.3)';
                      }}
                    >
                      🔄 Try Again
                    </button>
                  </div>
                )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Enhanced Success Modal */}
        {showSuccessModal && (
          <div
            className="modal-overlay-best modal-overlay-enhanced"
            style={{
              position: 'fixed',
              top: '0',
              left: '0',
              width: '100vw',
              height: '100vh',
              background: 'rgba(0, 0, 0, 0.25)', // Very light overlay
              backdropFilter: 'blur(6px)',
              WebkitBackdropFilter: 'blur(6px)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: '10000',
              padding: '20px',
              boxSizing: 'border-box',
              overflowY: 'auto' // Enable scrolling
            }}
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                setAutoNavigateCountdown(null); // Clear countdown
                setShowSuccessModal(false);
              }
            }}
          >
            <div
              className="modal-container-best modal-container-enhanced success success-modal-best"
              style={{
                background: 'linear-gradient(145deg, #ffffff 0%, #f0fdf4 100%)',
                borderRadius: '24px',
                boxShadow: '0 25px 80px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(34, 197, 94, 0.2)',
                border: '2px solid rgba(34, 197, 94, 0.1)',
                width: '100%',
                maxWidth: '750px', // Larger modal
                maxHeight: '90vh', // More height
                display: 'flex',
                flexDirection: 'column',
                overflow: 'hidden',
                position: 'relative',
                transform: 'translateZ(0)',
                margin: 'auto' // Center in scrollable area
              }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Professional Success Header */}
              <div style={{
                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
                padding: '2rem',
                color: 'white',
                textAlign: 'center',
                position: 'relative',
                borderRadius: '28px 28px 0 0',
                flexShrink: 0
              }}>
                <button
                  onClick={() => {
                    setAutoNavigateCountdown(null); // Clear countdown
                    setShowSuccessModal(false);
                  }}
                  style={{
                    position: 'absolute',
                    top: '16px',
                    right: '16px',
                    background: 'rgba(255, 255, 255, 0.2)',
                    border: 'none',
                    borderRadius: '50%',
                    width: '36px',
                    height: '36px',
                    color: 'white',
                    fontSize: '18px',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.3s ease',
                    zIndex: 10
                  }}
                  onMouseEnter={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.3)'}
                  onMouseLeave={(e) => e.target.style.background = 'rgba(255, 255, 255, 0.2)'}
                >
                  ✕
                </button>

                <div style={{
                  fontSize: '4rem',
                  marginBottom: '1rem',
                  animation: 'bounce 2s infinite'
                }}>
                  🎉
                </div>

                <h2 style={{
                  margin: '0',
                  fontSize: '2rem',
                  fontWeight: '800',
                  textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
                  marginBottom: '0.5rem'
                }}>
                  Payment Successful!
                </h2>

                <p style={{
                  margin: '0',
                  fontSize: '1.1rem',
                  opacity: '0.95',
                  fontWeight: '500'
                }}>
                  Welcome to {selectedPlan?.title}! 🚀
                </p>
              </div>

              {/* Enhanced Scrollable Success Content */}
              <div
                className="modal-scrollable-content success"
                style={{
                  flex: '1',
                  overflowY: 'auto',
                  overflowX: 'hidden',
                  padding: '0',
                  maxHeight: 'calc(90vh - 160px)', // Ensure scrolling works
                  scrollbarWidth: 'thin',
                  scrollbarColor: 'rgba(34, 197, 94, 0.6) rgba(0, 0, 0, 0.1)'
                }}>
                <div style={{
                  padding: '32px',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '28px',
                  minHeight: '100%'
                }}>
                {/* Success Status Card */}
                <div style={{
                  background: 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)',
                  border: '2px solid #22c55e',
                  borderRadius: '16px',
                  padding: '1.5rem',
                  textAlign: 'center',
                  boxShadow: '0 4px 20px rgba(34, 197, 94, 0.2)'
                }}>
                  <div style={{
                    background: 'rgba(34, 197, 94, 0.1)',
                    borderRadius: '12px',
                    padding: '1rem',
                    marginBottom: '1rem'
                  }}>
                    <h3 style={{
                      color: '#15803d',
                      marginBottom: '1rem',
                      fontSize: '1.2rem',
                      fontWeight: '700'
                    }}>
                      🎯 Subscription Details
                    </h3>
                    <div style={{
                      display: 'grid',
                      gap: '0.75rem',
                      textAlign: 'left'
                    }}>
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        padding: '0.5rem',
                        background: 'rgba(255, 255, 255, 0.7)',
                        borderRadius: '8px'
                      }}>
                        <span style={{ fontWeight: '600', color: '#374151' }}>📋 Plan:</span>
                        <span style={{ color: '#059669', fontWeight: '700' }}>{selectedPlan?.title}</span>
                      </div>
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        padding: '0.5rem',
                        background: 'rgba(255, 255, 255, 0.7)',
                        borderRadius: '8px'
                      }}>
                        <span style={{ fontWeight: '600', color: '#374151' }}>⏰ Duration:</span>
                        <span style={{ color: '#059669', fontWeight: '700' }}>{selectedPlan?.duration} month{selectedPlan?.duration > 1 ? 's' : ''}</span>
                      </div>
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        padding: '0.5rem',
                        background: 'rgba(255, 255, 255, 0.7)',
                        borderRadius: '8px'
                      }}>
                        <span style={{ fontWeight: '600', color: '#374151' }}>💰 Amount:</span>
                        <span style={{ color: '#059669', fontWeight: '700' }}>{selectedPlan?.discountedPrice?.toLocaleString()} TZS</span>
                      </div>
                      <div style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        padding: '0.5rem',
                        background: 'linear-gradient(135deg, #22c55e, #16a34a)',
                        borderRadius: '8px',
                        color: 'white'
                      }}>
                        <span style={{ fontWeight: '600' }}>💎 Status:</span>
                        <span style={{ fontWeight: '800', fontSize: '1.1rem' }}>ACTIVE ✅</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Features Unlocked Card */}
                <div style={{
                  background: 'linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%)',
                  border: '2px solid #f59e0b',
                  borderRadius: '16px',
                  padding: '1.5rem',
                  boxShadow: '0 4px 20px rgba(245, 158, 11, 0.2)'
                }}>
                  <h3 style={{
                    color: '#92400e',
                    marginBottom: '1rem',
                    textAlign: 'center',
                    fontSize: '1.2rem',
                    fontWeight: '700'
                  }}>
                    🚀 All Premium Features Unlocked!
                  </h3>
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                    gap: '1rem'
                  }}>
                    <div style={{
                      background: 'rgba(255, 255, 255, 0.8)',
                      borderRadius: '12px',
                      padding: '1rem'
                    }}>
                      <div style={{ marginBottom: '0.5rem', fontSize: '0.95rem', fontWeight: '600', color: '#374151' }}>
                        <div style={{ marginBottom: '0.25rem' }}>✅ <strong>Unlimited Quizzes</strong></div>
                        <div style={{ marginBottom: '0.25rem' }}>🤖 <strong>AI Assistant</strong></div>
                        <div>📚 <strong>Study Materials</strong></div>
                      </div>
                    </div>
                    <div style={{
                      background: 'rgba(255, 255, 255, 0.8)',
                      borderRadius: '12px',
                      padding: '1rem'
                    }}>
                      <div style={{ marginBottom: '0.5rem', fontSize: '0.95rem', fontWeight: '600', color: '#374151' }}>
                        <div style={{ marginBottom: '0.25rem' }}>📊 <strong>Progress Tracking</strong></div>
                        <div style={{ marginBottom: '0.25rem' }}>🎥 <strong>Learning Videos</strong></div>
                        <div>💬 <strong>Forum Access</strong></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Auto-Navigation Notice */}
                {autoNavigateCountdown && (
                  <div style={{
                    background: 'linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%)',
                    border: '2px solid #0288d1',
                    borderRadius: '12px',
                    padding: '1rem',
                    textAlign: 'center',
                    marginTop: '1rem'
                  }}>
                    <p style={{
                      margin: '0',
                      color: '#01579b',
                      fontSize: '0.9rem',
                      fontWeight: '600'
                    }}>
                      🚀 Automatically redirecting to Hub in {autoNavigateCountdown} seconds...
                    </p>
                  </div>
                )}

                {/* Action Buttons */}
                <div style={{
                  display: 'flex',
                  gap: '1rem',
                  justifyContent: 'center',
                  flexWrap: 'wrap',
                  marginTop: 'auto',
                  paddingTop: '1rem'
                }}>
                  <button
                    onClick={() => {
                      setAutoNavigateCountdown(null); // Clear countdown
                      setShowSuccessModal(false);
                      window.location.href = '/user/hub';
                    }}
                    style={{
                      background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                      border: 'none',
                      color: 'white',
                      padding: '1rem 2rem',
                      borderRadius: '12px',
                      fontSize: '1rem',
                      fontWeight: '700',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      boxShadow: '0 4px 20px rgba(59, 130, 246, 0.3)',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      minWidth: '180px',
                      justifyContent: 'center'
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.background = 'linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%)';
                      e.target.style.transform = 'translateY(-2px)';
                      e.target.style.boxShadow = '0 6px 25px rgba(59, 130, 246, 0.4)';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.background = 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';
                      e.target.style.transform = 'translateY(0)';
                      e.target.style.boxShadow = '0 4px 20px rgba(59, 130, 246, 0.3)';
                    }}
                  >
                    🏠 Continue to Hub {autoNavigateCountdown ? `(${autoNavigateCountdown}s)` : ''}
                  </button>
                  <button
                    onClick={() => {
                      setAutoNavigateCountdown(null); // Clear countdown
                      setShowSuccessModal(false);
                    }}
                    style={{
                      background: 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)',
                      border: '2px solid #d1d5db',
                      color: '#374151',
                      padding: '1rem 2rem',
                      borderRadius: '12px',
                      fontSize: '1rem',
                      fontWeight: '600',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      minWidth: '120px',
                      justifyContent: 'center'
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.background = 'linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%)';
                      e.target.style.transform = 'translateY(-1px)';
                      e.target.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.15)';
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.background = 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)';
                      e.target.style.transform = 'translateY(0)';
                      e.target.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
                    }}
                  >
                    ✨ Close
                  </button>
                </div>

                {/* Congratulations Message */}
                <div style={{
                  marginTop: '1.5rem',
                  padding: '1rem',
                  background: 'rgba(34, 197, 94, 0.1)',
                  borderRadius: '12px',
                  textAlign: 'center',
                  border: '1px solid rgba(34, 197, 94, 0.2)'
                }}>
                  <p style={{
                    margin: '0',
                    fontSize: '1rem',
                    color: '#15803d',
                    fontWeight: '600',
                    lineHeight: '1.5'
                  }}>
                    🎉 Congratulations! You now have full access to all BrainWave features. Start exploring and excel in your studies! 🚀
                  </p>
                </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Upgrade Restriction Modal */}
        <UpgradeRestrictionModal
          visible={showUpgradeRestriction}
          onClose={() => setShowUpgradeRestriction(false)}
          currentPlan={plans.find(p => p._id === subscriptionData?.activePlan) || subscriptionData?.plan}
          subscription={subscriptionData}
          user={user}
        />

        {/* Subscription Expired Modal */}
        <SubscriptionExpiredModal
          visible={showExpiredModal}
          onClose={() => setShowExpiredModal(false)}
          onRenew={handleRenewSubscription}
          subscription={subscriptionData}
          user={user}
          plans={plans}
        />
      </div>
    </div>
  );
};

export default Subscription;
